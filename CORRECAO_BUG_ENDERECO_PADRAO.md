# Correção do Bug de Endereço Padrão em Formulários de Entidades

## **Problema Identificado**

### **Descrição do Bug**
Após alterar qual endereço está marcado como padrão em uma entidade e salvar o cadastro, o formulário de edição não refletia a mudança corretamente. Especificamente:

1. **Usuário altera endereço padrão** no modal da entidade
2. **Salvamento é executado** corretamente no banco de dados
3. **Modal é fechado** e reaberto para a mesma entidade
4. **Sistema ainda exibe o endereço anterior como padrão**

### **Causa Raiz**
O problema estava relacionado ao **ciclo de vida dos dados no frontend**:

1. **Cache do React Query não era invalidado** adequadamente após operações de endereço
2. **Estado local dos modais não era limpo** corretamente
3. **Hook `useEntityAddresses` usava cache antigo** com `staleTime` muito alto
4. **Dependências do useEffect** não incluíam o estado de abertura do modal

## **Soluções Implementadas**

### **1. Invalidação Explícita do Cache**

**Arquivos modificados:**
- `frontend/src/pages/Customers.tsx`
- `frontend/src/pages/Suppliers.tsx`

**Mudanças:**
```typescript
// Após salvamento da entidade, invalidar cache dos endereços
await syncEntityAddresses(selectedCustomer.id, customerData.addresses);

// ✅ NOVO: Invalidar cache dos endereços da entidade para forçar recarregamento
queryClient.invalidateQueries({ queryKey: ['entity-addresses', selectedCustomer.id] });
```

**Benefícios:**
- Força o React Query a buscar dados atualizados do servidor
- Garante que o próximo carregamento do modal terá dados frescos

### **2. Melhoria no Hook useEntityAddresses**

**Arquivo modificado:**
- `frontend/src/hooks/api/useEntityAddresses.ts`

**Mudanças:**
```typescript
// ✅ ANTES
export const useEntityAddresses = (entityId: string) => {
  return useQuery({
    staleTime: 5 * 60 * 1000, // 5 minutos - muito tempo para modais
  });
};

// ✅ DEPOIS
export const useEntityAddresses = (entityId: string, options?: { staleTime?: number }) => {
  return useQuery({
    staleTime: options?.staleTime ?? 5 * 60 * 1000, // Configurável
  });
};
```

**Benefícios:**
- Permite configurar `staleTime` específico para modais
- Mantém cache otimizado para outras partes da aplicação

### **3. Configuração de Cache Otimizada para Modais**

**Arquivos modificados:**
- `frontend/src/components/customers/CustomerModal.tsx`
- `frontend/src/components/suppliers/SupplierModal.tsx`

**Mudanças:**
```typescript
// ✅ Usar staleTime menor para garantir dados atualizados em modais
const { data: entityAddresses, isLoading: loadingAddresses } = useEntityAddresses(
  customer?.id || '',
  { staleTime: 0 } // Sempre buscar dados frescos em modais
);
```

**Benefícios:**
- Modais sempre carregam dados atualizados
- Não afeta performance de outras partes da aplicação

### **4. Melhoria nas Dependências do useEffect**

**Arquivos modificados:**
- `frontend/src/components/customers/CustomerModal.tsx`
- `frontend/src/components/suppliers/SupplierModal.tsx`

**Mudanças:**
```typescript
// ✅ ANTES
useEffect(() => {
  // Carregar endereços...
}, [customer, entityAddresses, loadingAddresses]);

// ✅ DEPOIS
useEffect(() => {
  // Carregar endereços...
}, [customer, entityAddresses, loadingAddresses, isOpen]); // Incluir isOpen
```

**Benefícios:**
- useEffect é reexecutado quando o modal é reaberto
- Garante que dados são recarregados a cada abertura

### **5. Limpeza de Estado Aprimorada**

**Arquivos modificados:**
- `frontend/src/pages/Customers.tsx`
- `frontend/src/pages/Suppliers.tsx`

**Mudanças:**
```typescript
// ✅ ANTES
onClose={() => setIsModalOpen(false)}

// ✅ DEPOIS
onClose={() => {
  setIsModalOpen(false);
  setSelectedCustomer(null); // Limpar estado selecionado
}}
```

**Benefícios:**
- Estado é limpo adequadamente quando modal é fechado
- Evita conflitos entre diferentes entidades

## **Fluxo Corrigido**

### **Antes da Correção:**
1. Usuário edita endereço padrão ❌
2. Dados são salvos no banco ✅
3. Modal é fechado ❌ (cache não invalidado)
4. Modal é reaberto ❌ (usa cache antigo)
5. Endereço anterior ainda aparece como padrão ❌

### **Após a Correção:**
1. Usuário edita endereço padrão ✅
2. Dados são salvos no banco ✅
3. Cache é invalidado explicitamente ✅
4. Modal é fechado com estado limpo ✅
5. Modal é reaberto com dados frescos ✅
6. Novo endereço padrão é exibido corretamente ✅

## **Impacto das Mudanças**

### **Performance:**
- ✅ **Mínimo impacto**: Invalidação só ocorre após salvamento
- ✅ **Cache otimizado**: Outras partes da aplicação mantêm cache eficiente
- ✅ **Busca seletiva**: Apenas modais fazem busca sempre atualizada

### **Confiabilidade:**
- ✅ **Dados sempre atualizados** em modais de edição
- ✅ **Estado consistente** entre banco de dados e interface
- ✅ **Experiência do usuário melhorada**

### **Manutenibilidade:**
- ✅ **Código mais robusto** com invalidação explícita
- ✅ **Padrão replicável** para outros modais
- ✅ **Debugging facilitado** com logs de invalidação

## **Testes Recomendados**

### **Cenário de Teste:**
1. Abrir modal de edição de cliente/fornecedor
2. Alterar endereço padrão (desmarcar atual, marcar outro)
3. Salvar entidade
4. Fechar modal
5. Reabrir modal da mesma entidade
6. **Verificar**: Novo endereço deve estar marcado como padrão

### **Casos Adicionais:**
- Testar com múltiplos endereços
- Testar criação de novo endereço como padrão
- Testar remoção de endereço padrão
- Testar em diferentes navegadores

## **Monitoramento**

### **Logs para Acompanhar:**
- Invalidação de cache: `queryClient.invalidateQueries`
- Carregamento de endereços: `useEntityAddresses`
- Operações de salvamento: `syncEntityAddresses`

### **Métricas de Sucesso:**
- ✅ Zero relatos de endereço padrão incorreto
- ✅ Tempo de carregamento de modais mantido
- ✅ Satisfação do usuário com consistência de dados
