import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedBanks() {
  console.log('🏦 Seeding banks...');

  const banks = [
    {
      code: '001',
      name: 'Banco do Brasil',
      logo: 'https://logoeps.com/wp-content/uploads/2013/03/banco-do-brasil-vector-logo.png'
    },
    {
      code: '033',
      name: 'Banco Santander',
      logo: 'https://logoeps.com/wp-content/uploads/2013/03/santander-vector-logo.png'
    },
    {
      code: '104',
      name: 'Caixa Econômica Federal',
      logo: 'https://logoeps.com/wp-content/uploads/2013/03/caixa-economica-federal-vector-logo.png'
    },
    {
      code: '237',
      name: 'Banco Bradesco',
      logo: 'https://logoeps.com/wp-content/uploads/2013/03/bradesco-vector-logo.png'
    },
    {
      code: '341',
      name: 'Banco Itaú',
      logo: 'https://logoeps.com/wp-content/uploads/2013/03/itau-vector-logo.png'
    },
    {
      code: '260',
      name: '<PERSON><PERSON>',
      logo: 'https://logoeps.com/wp-content/uploads/2020/09/nubank-vector-logo.png'
    },
    {
      code: '077',
      name: 'Banco Inter',
      logo: 'https://logoeps.com/wp-content/uploads/2020/09/banco-inter-vector-logo.png'
    },
    {
      code: '212',
      name: 'Banco Original',
      logo: 'https://logoeps.com/wp-content/uploads/2020/09/banco-original-vector-logo.png'
    }
  ];

  for (const bank of banks) {
    try {
      const existingBank = await prisma.bank.findUnique({
        where: { code: bank.code }
      });

      if (!existingBank) {
        const createdBank = await prisma.bank.create({
          data: bank
        });
        console.log(`✅ Banco criado: ${createdBank.name} (${createdBank.code})`);
      } else {
        console.log(`⚠️  Banco já existe: ${existingBank.name} (${existingBank.code})`);
      }
    } catch (error) {
      console.error(`❌ Erro ao criar banco ${bank.name}:`, error);
    }
  }

  console.log('🏦 Seed de bancos concluído!');
}

async function main() {
  try {
    await seedBanks();
  } catch (error) {
    console.error('❌ Erro durante o seed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
