-- Migration: Expand entities table for Brazilian companies
-- This migration adds support for:
-- - Person type distinction (individual/company)
-- - Brazilian fiscal documents (CPF, IE, IM)
-- - Mixed entity types (customer AND supplier)
-- - Commercial and banking information
-- - Enhanced status management

-- Step 1: Add new columns to entities table
ALTER TABLE entities 
ADD COLUMN entity_type VARCHAR(20) DEFAULT 'customer',
ADD COLUMN person_type VARCHAR(20),
ADD COLUMN trade_name VARCHAR(255),
ADD COLUMN cpf VARCHAR(14),
ADD COLUMN state_registration VARCHAR(20),
ADD COLUMN municipal_registration VARCHAR(20),
ADD COLUMN rg VARCHAR(20),
ADD COLUMN rg_issuer VARCHAR(10),
ADD COLUMN tax_regime VARCHAR(30),
ADD COLUMN status VARCHAR(20) DEFAULT 'active',
ADD COLUMN mobile VARCHAR(20),
ADD COLUMN website VARCHAR(255),
ADD COLUMN credit_limit DECIMAL(12,2) DEFAULT 0,
ADD COLUMN payment_term_days INTEGER DEFAULT 30,
ADD COLUMN icms_taxpayer BOOLEAN DEFAULT false,
ADD COLUMN simple_national BOOLEAN DEFAULT false,
ADD COLUMN withhold_taxes BOOLEAN DEFAULT false,
ADD COLUMN send_nfe BOOLEAN DEFAULT true,
ADD COLUMN nfe_email VARCHAR(255),
ADD COLUMN bank_code VARCHAR(10),
ADD COLUMN bank_name VARCHAR(100),
ADD COLUMN agency VARCHAR(10),
ADD COLUMN account VARCHAR(20),
ADD COLUMN account_digit VARCHAR(2),
ADD COLUMN pix_key VARCHAR(255),
ADD COLUMN discount_percentage DECIMAL(5,2) DEFAULT 0,
ADD COLUMN notes TEXT;

-- Step 2: Update existing data
-- Set entity_type based on current type field
UPDATE entities 
SET entity_type = type;

-- Set person_type based on existing CNPJ data
UPDATE entities 
SET person_type = CASE 
  WHEN cnpj IS NOT NULL AND cnpj != '' THEN 'company'
  ELSE 'individual'
END;

-- Step 3: Add constraints
-- Remove old type constraint and add new ones
ALTER TABLE entities 
DROP CONSTRAINT IF EXISTS entities_type_check;

-- Add new constraints
ALTER TABLE entities 
ADD CONSTRAINT entities_entity_type_check 
CHECK (entity_type IN ('customer', 'supplier', 'both'));

ALTER TABLE entities 
ADD CONSTRAINT entities_person_type_check 
CHECK (person_type IN ('individual', 'company'));

ALTER TABLE entities 
ADD CONSTRAINT entities_status_check 
CHECK (status IN ('active', 'inactive', 'suspended', 'blocked'));

ALTER TABLE entities 
ADD CONSTRAINT entities_tax_regime_check 
CHECK (tax_regime IS NULL OR tax_regime IN ('simple_national', 'presumed_profit', 'real_profit', 'individual'));

-- Step 4: Make person_type required after data migration
ALTER TABLE entities 
ALTER COLUMN person_type SET NOT NULL;

-- Step 5: Add validation constraints for Brazilian documents
-- CPF format validation (XXX.XXX.XXX-XX)
ALTER TABLE entities 
ADD CONSTRAINT entities_cpf_format_check 
CHECK (cpf IS NULL OR cpf ~* '^[0-9]{3}\.[0-9]{3}\.[0-9]{3}-[0-9]{2}$');

-- CNPJ format validation (XX.XXX.XXX/XXXX-XX) - update existing constraint
ALTER TABLE entities 
DROP CONSTRAINT IF EXISTS entities_cnpj_check;

ALTER TABLE entities 
ADD CONSTRAINT entities_cnpj_format_check 
CHECK (cnpj IS NULL OR cnpj ~* '^[0-9]{2}\.[0-9]{3}\.[0-9]{3}/[0-9]{4}-[0-9]{2}$');

-- Step 6: Add business logic constraints
-- For individual persons, CPF should be provided
-- For companies, CNPJ and state_registration should be provided
-- Note: These will be enforced at application level for flexibility

-- Step 7: Create performance indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_entities_cnpj_active 
ON entities(cnpj) WHERE cnpj IS NOT NULL AND deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_entities_cpf_active 
ON entities(cpf) WHERE cpf IS NOT NULL AND deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_entities_company_entity_type 
ON entities(company_id, entity_type) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_entities_person_type 
ON entities(person_type) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_entities_status 
ON entities(status) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_entities_tax_regime 
ON entities(tax_regime) WHERE tax_regime IS NOT NULL AND deleted_at IS NULL;

-- Step 8: Create composite indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_entities_company_type_status 
ON entities(company_id, entity_type, status) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_entities_person_entity_type 
ON entities(person_type, entity_type) WHERE deleted_at IS NULL;

-- Step 9: Add full-text search index for names (requires pg_trgm extension)
-- This will be created conditionally if the extension is available
DO $$
BEGIN
  -- Check if pg_trgm extension exists
  IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_trgm') THEN
    -- Create trigram index for name search
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_entities_name_trgm 
    ON entities USING gin(name gin_trgm_ops);
    
    -- Create trigram index for trade_name search
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_entities_trade_name_trgm 
    ON entities USING gin(trade_name gin_trgm_ops) 
    WHERE trade_name IS NOT NULL;
  END IF;
END $$;

-- Step 10: Add comments for documentation
COMMENT ON COLUMN entities.entity_type IS 'Type of commercial relationship: customer, supplier, or both';
COMMENT ON COLUMN entities.person_type IS 'Type of person: individual (pessoa física) or company (pessoa jurídica)';
COMMENT ON COLUMN entities.trade_name IS 'Trade name (nome fantasia) for companies';
COMMENT ON COLUMN entities.cpf IS 'CPF for individual persons (format: XXX.XXX.XXX-XX)';
COMMENT ON COLUMN entities.state_registration IS 'State registration (Inscrição Estadual) for companies';
COMMENT ON COLUMN entities.municipal_registration IS 'Municipal registration (Inscrição Municipal) for companies';
COMMENT ON COLUMN entities.tax_regime IS 'Tax regime: simple_national, presumed_profit, real_profit, individual';
COMMENT ON COLUMN entities.icms_taxpayer IS 'Whether the entity is an ICMS taxpayer';
COMMENT ON COLUMN entities.simple_national IS 'Whether the entity is enrolled in Simples Nacional';
COMMENT ON COLUMN entities.credit_limit IS 'Credit limit for the entity';
COMMENT ON COLUMN entities.payment_term_days IS 'Default payment term in days';
COMMENT ON COLUMN entities.pix_key IS 'PIX key for payments';

-- Step 11: Update table comment
COMMENT ON TABLE entities IS 'Entities table supporting Brazilian companies with fiscal compliance';
