-- Add missing DEFAULT values for UUID and timestamp fields
-- This migration ensures that all required fields have proper default values
-- even after schema consolidation

-- Fix roles table
ALTER TABLE roles ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE roles ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

-- Fix user_company_roles table  
ALTER TABLE user_company_roles ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE user_company_roles ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

-- Fix other tables that might have the same issue
ALTER TABLE users ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE users ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE profiles ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE system_permissions ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE system_permissions ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE permissions ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE permissions ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE companies ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE companies ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE address_types ALTER COLUMN id SET DEFAULT gen_random_uuid();

ALTER TABLE addresses ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE addresses ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE banks ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE banks ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE zip_codes ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE accounts_payable ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE accounts_payable ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE accounts_receivable ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE accounts_receivable ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE entities ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE entities ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE currencies ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE currencies ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE categories ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE categories ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE projects ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE projects ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE payment_methods ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE payment_methods ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE bank_accounts ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE bank_accounts ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE recurrence_types ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE recurrence_types ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE custom_periods ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE custom_periods ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE transactions ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE transactions ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE recurring_schedules ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE recurring_schedules ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE company_invitations ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE company_invitations ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE company_codes ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE company_codes ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;
