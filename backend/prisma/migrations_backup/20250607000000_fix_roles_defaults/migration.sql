-- Fix roles and user_company_roles tables to have proper default values
-- This migration fixes the issue where these tables were missing default values
-- for id and updated_at columns, causing INSERT operations to fail

-- Add default UUID generation to the id column in roles table
ALTER TABLE roles ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- Add default timestamp to the updated_at column in roles table
ALTER TABLE roles ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

-- Add default UUID generation to the id column in user_company_roles table
ALTER TABLE user_company_roles ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- Add default timestamp to the updated_at column in user_company_roles table
ALTER TABLE user_company_roles ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;
