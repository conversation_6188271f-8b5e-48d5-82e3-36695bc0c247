-- Fix roles and user_company_roles tables id columns to have default UUID generation
-- This migration fixes the issue where these tables id columns
-- were missing default values, causing INSERT operations to fail

-- Add default UUID generation to the id column in roles table
ALTER TABLE roles ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- Add default UUID generation to the id column in user_company_roles table
ALTER TABLE user_company_roles ALTER COLUMN id SET DEFAULT gen_random_uuid();
