// backend/src/routes/banks/dto/create-bank.dto.ts
import {
  IsString,
  IsOptional,
  MaxLength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateBankDto {
  @ApiProperty({
    description: 'Código do banco',
    example: '001',
    maxLength: 10
  })
  @IsString()
  @MaxLength(10)
  code: string;

  @ApiProperty({
    description: 'Nome do banco',
    example: 'Banco do Brasil',
    maxLength: 100
  })
  @IsString()
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({
    description: 'URL do logotipo do banco ou imagem em base64',
    example: 'https://example.com/logo.png'
  })
  @IsOptional()
  @IsString()
  @MaxLength(3000000) // Limite de ~3MB para string base64
  logo?: string;
}
