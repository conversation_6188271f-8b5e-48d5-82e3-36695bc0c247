// backend/src/routes/categories/categories.service.ts
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoryStatisticsDto } from './dto/category-statistics.dto';
import { Prisma, Category } from '@prisma/client';

// Define a type for the authenticated user payload
interface AuthenticatedUser {
  id: string;
  companyId: string;
}

// Type for category with children (for tree structure)
type CategoryWithChildren = Category & {
  children?: CategoryWithChildren[];
};

// Type for the response format expected by frontend
export type CategoryResponse = {
  id: string;
  companyId: string;
  name: string;
  transactionType: string;
  parentCategoryId: string | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  children?: CategoryResponse[];
};

@Injectable()
export class CategoriesService {
  private readonly MAX_CATEGORY_DEPTH = 5;

  constructor(private prisma: PrismaService) {}

  /**
   * Transform category data to match frontend expectations
   * Maps parentId to parentCategoryId
   */
  private transformCategory(category: Category): CategoryResponse {
    return {
      id: category.id,
      companyId: category.companyId,
      name: category.name,
      transactionType: category.transactionType,
      parentCategoryId: category.parentId,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
      deletedAt: category.deletedAt,
    };
  }

  /**
   * Transform category with children to match frontend expectations
   */
  private transformCategoryWithChildren(category: CategoryWithChildren): CategoryResponse {
    const transformed = this.transformCategory(category);
    if (category.children && category.children.length > 0) {
      transformed.children = category.children.map(child =>
        this.transformCategoryWithChildren(child)
      );
    }
    return transformed;
  }

  /**
   * Create a new category
   */
  async create(
    createDto: CreateCategoryDto,
    user: AuthenticatedUser,
  ): Promise<CategoryResponse> {
    const { companyId } = user;

    // Verificar se o usuário tem uma empresa associada
    if (!companyId) {
      throw new BadRequestException(
        'Usuário deve estar associado a uma empresa para criar categorias'
      );
    }

    // If parentCategoryId is provided, validate it
    if (createDto.parentCategoryId) {
      await this.validateParentCategory(createDto.parentCategoryId, companyId, createDto.transactionType);

      // Validate maximum depth
      await this.validateMaxDepth(createDto.parentCategoryId, companyId);
    }

    // Prepare data
    const data: Prisma.CategoryCreateInput = {
      name: createDto.name,
      transactionType: createDto.transactionType,
      company: { connect: { id: companyId } },
      // Connect parent if provided
      ...(createDto.parentCategoryId && {
        parent: { connect: { id: createDto.parentCategoryId } },
      }),
    };

    try {
      const category = await this.prisma.category.create({ data });
      return this.transformCategory(category);
    } catch (error) {
      console.error('Error creating category:', error);
      throw new InternalServerErrorException('Could not create category');
    }
  }

  /**
   * Find all categories with filtering and pagination
   */
  async findAll(
    user: AuthenticatedUser,
    { page = 1, limit = 10, transactionType }: {
      page?: number;
      limit?: number;
      transactionType?: string;
    } = {}
  ): Promise<{ data: CategoryResponse[]; total: number; page: number; limit: number }> {
    const { companyId } = user;

    // Verificar se o usuário tem uma empresa associada
    if (!companyId) {
      throw new BadRequestException(
        'Usuário deve estar associado a uma empresa para acessar categorias'
      );
    }

    const skip = (page - 1) * limit;

    const where: Prisma.CategoryWhereInput = {
      companyId,
      deletedAt: null,
      ...(transactionType && { transactionType }),
    };

    try {
      const [categories, total] = await Promise.all([
        this.prisma.category.findMany({
          where,
          skip,
          take: limit,
          orderBy: { name: 'asc' },
          include: {
            parent: true,
            children: {
              where: { deletedAt: null },
            },
          },
        }),
        this.prisma.category.count({ where }),
      ]);

      return {
        data: categories.map(category => this.transformCategory(category)),
        total,
        page,
        limit,
      };
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw new InternalServerErrorException('Could not fetch categories');
    }
  }

  /**
   * Find one category by ID
   */
  async findOne(id: string, user: AuthenticatedUser): Promise<CategoryResponse> {
    const { companyId } = user;

    try {
      const category = await this.prisma.category.findFirst({
        where: {
          id,
          companyId,
          deletedAt: null,
        },
        include: {
          parent: true,
          children: {
            where: { deletedAt: null },
          },
        },
      });

      if (!category) {
        throw new NotFoundException(`Category with ID ${id} not found`);
      }

      return this.transformCategory(category);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error fetching category with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not fetch category');
    }
  }

  /**
   * Update a category
   */
  async update(
    id: string,
    updateDto: UpdateCategoryDto,
    user: AuthenticatedUser,
  ): Promise<CategoryResponse> {
    const { companyId } = user;

    // Ensure the category exists and belongs to the company
    await this.findOne(id, user);

    // If parentCategoryId is provided, validate it
    if (updateDto.parentCategoryId) {
      await this.validateParentCategory(updateDto.parentCategoryId, companyId, updateDto.transactionType);

      // Check for circular reference
      if (updateDto.parentCategoryId === id) {
        throw new BadRequestException('A category cannot be its own parent');
      }

      // Check if the new parent is not a descendant of this category
      await this.checkForCircularReference(id, updateDto.parentCategoryId);

      // Validate maximum depth
      await this.validateMaxDepth(updateDto.parentCategoryId, companyId);
    }

    // Prepare update data
    const data: Prisma.CategoryUpdateInput = {
      ...(updateDto.name && { name: updateDto.name }),
      ...(updateDto.transactionType && { transactionType: updateDto.transactionType }),
      ...(updateDto.parentCategoryId !== undefined && {
        parent: updateDto.parentCategoryId
          ? { connect: { id: updateDto.parentCategoryId } }
          : { disconnect: true },
      }),
    };

    try {
      const category = await this.prisma.category.update({
        where: { id },
        data,
        include: {
          parent: true,
          children: {
            where: { deletedAt: null },
          },
        },
      });
      return this.transformCategory(category);
    } catch (error) {
      console.error(`Error updating category with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not update category');
    }
  }

  /**
   * Soft delete a category
   */
  async remove(id: string, user: AuthenticatedUser): Promise<void> {
    // Ensure the category exists and belongs to the company
    await this.findOne(id, user);

    // Check if the category has children
    const children = await this.prisma.category.findMany({
      where: {
        parentId: id,
        deletedAt: null,
      },
    });

    if (children.length > 0) {
      throw new BadRequestException(
        'Cannot delete a category that has subcategories. Delete the subcategories first or update them to remove the parent reference.',
      );
    }

    // Check if the category is used in any transactions
    const usedInTransactions = await this.prisma.transaction.findFirst({
      where: {
        categoryId: id,
        deletedAt: null,
      },
    });

    if (usedInTransactions) {
      throw new BadRequestException(
        'Cannot delete a category that is used in transactions.',
      );
    }

    // Check if the category is used in accounts payable
    const usedInAccountsPayable = await this.prisma.accountsPayable.findFirst({
      where: {
        categoryId: id,
        deletedAt: null,
      },
    });

    if (usedInAccountsPayable) {
      throw new BadRequestException(
        'Cannot delete a category that is used in accounts payable.',
      );
    }

    // Check if the category is used in accounts receivable
    const usedInAccountsReceivable = await this.prisma.accountsReceivable.findFirst({
      where: {
        categoryId: id,
        deletedAt: null,
      },
    });

    if (usedInAccountsReceivable) {
      throw new BadRequestException(
        'Cannot delete a category that is used in accounts receivable.',
      );
    }

    // Perform soft delete
    try {
      await this.prisma.category.update({
        where: { id },
        data: { deletedAt: new Date() },
      });
    } catch (error) {
      console.error(`Error soft-deleting category with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not delete category');
    }
  }

  /**
   * Get category tree
   */
  async getTree(
    user: AuthenticatedUser,
    transactionType?: string,
  ): Promise<CategoryResponse[]> {
    const { companyId } = user;

    // Verificar se o usuário tem uma empresa associada
    if (!companyId) {
      throw new BadRequestException(
        'Usuário deve estar associado a uma empresa para acessar categorias'
      );
    }

    try {
      // First, get all root categories (those without a parent)
      const rootCategories = await this.prisma.category.findMany({
        where: {
          companyId,
          deletedAt: null,
          parentId: null,
          ...(transactionType && { transactionType }),
        },
        orderBy: { name: 'asc' },
      });

      // Then, for each root category, recursively get its children
      const result = await Promise.all(
        rootCategories.map(async (root) => {
          const children = await this.getChildrenRecursive(root.id, companyId);
          const categoryWithChildren: CategoryWithChildren = {
            ...root,
            children,
          };
          return this.transformCategoryWithChildren(categoryWithChildren);
        }),
      );

      return result;
    } catch (error) {
      console.error('Error fetching category tree:', error);
      throw new InternalServerErrorException('Could not fetch category tree');
    }
  }

  /**
   * Helper method to recursively get children of a category
   */
  private async getChildrenRecursive(
    parentId: string,
    companyId: string,
  ): Promise<CategoryWithChildren[]> {
    const children = await this.prisma.category.findMany({
      where: {
        parentId,
        companyId,
        deletedAt: null,
      },
      orderBy: { name: 'asc' },
    });

    if (children.length === 0) {
      return [];
    }

    const result = await Promise.all(
      children.map(async (child) => {
        const grandchildren = await this.getChildrenRecursive(child.id, companyId);
        return {
          ...child,
          children: grandchildren,
        };
      }),
    );

    return result;
  }

  /**
   * Helper method to validate parent category
   */
  private async validateParentCategory(
    parentId: string,
    companyId: string,
    transactionType?: string,
  ): Promise<void> {
    const parent = await this.prisma.category.findFirst({
      where: {
        id: parentId,
        companyId,
        deletedAt: null,
      },
    });

    if (!parent) {
      throw new BadRequestException(
        `Parent category with ID ${parentId} not found or does not belong to your company`,
      );
    }

    // Validate transaction type compatibility if provided
    if (transactionType && parent.transactionType !== transactionType) {
      throw new BadRequestException(
        `Parent category type (${parent.transactionType}) must match the subcategory type (${transactionType})`,
      );
    }
  }

  /**
   * Helper method to check for circular references when updating parent
   */
  private async checkForCircularReference(
    categoryId: string,
    newParentId: string,
  ): Promise<void> {
    // Get all ancestors of the new parent
    const ancestors = await this.getAllAncestors(newParentId);

    // If the category ID is in the ancestors, it would create a circular reference
    if (ancestors.some(ancestor => ancestor.id === categoryId)) {
      throw new BadRequestException(
        'Cannot set parent: this would create a circular reference in the category hierarchy',
      );
    }
  }

  /**
   * Helper method to get all ancestors of a category
   */
  private async getAllAncestors(categoryId: string): Promise<Category[]> {
    const category = await this.prisma.category.findUnique({
      where: { id: categoryId },
      include: { parent: true },
    });

    if (!category || !category.parentId || !category.parent) {
      return [];
    }

    const ancestors = await this.getAllAncestors(category.parentId);
    return [category.parent, ...ancestors];
  }

  /**
   * Helper method to validate maximum depth
   */
  private async validateMaxDepth(parentId: string, companyId: string): Promise<void> {
    const ancestors = await this.getAllAncestors(parentId);

    // Current depth would be ancestors.length + 1 (for the parent) + 1 (for the new category)
    const currentDepth = ancestors.length + 2;

    if (currentDepth > this.MAX_CATEGORY_DEPTH) {
      throw new BadRequestException(
        `Maximum category depth of ${this.MAX_CATEGORY_DEPTH} levels exceeded. Current depth would be ${currentDepth}.`,
      );
    }
  }

  /**
   * Get statistics for categories including transaction counts
   */
  async getStatistics(
    user: AuthenticatedUser,
    transactionType?: string,
  ): Promise<CategoryStatisticsDto[]> {
    const { companyId } = user;

    // Verificar se o usuário tem uma empresa associada
    if (!companyId) {
      throw new BadRequestException(
        'Usuário deve estar associado a uma empresa para acessar estatísticas de categorias'
      );
    }

    try {
      // Buscar todas as categorias da empresa
      const categories = await this.prisma.category.findMany({
        where: {
          companyId,
          deletedAt: null,
          ...(transactionType && { transactionType }),
        },
        include: {
          children: {
            where: { deletedAt: null },
          },
        },
        orderBy: { name: 'asc' },
      });

      // Calcular estatísticas para cada categoria
      const statistics = await Promise.all(
        categories.map(async (category) => {
          // Contar transações diretas da categoria
          const directTransactionCount = await this.prisma.transaction.count({
            where: {
              categoryId: category.id,
              companyId,
              deletedAt: null,
            },
          });

          // Contar transações de todas as subcategorias recursivamente
          const subcategoryTransactionCount = await this.countTransactionsInSubcategories(
            category.id,
            companyId,
          );

          // Total de transações (diretas + subcategorias)
          const totalTransactionCount = directTransactionCount + subcategoryTransactionCount;

          // Contar subcategorias diretas
          const subcategoryCount = category.children?.length || 0;

          // Calcular nível hierárquico
          const level = await this.calculateCategoryLevel(category.id);

          return {
            categoryId: category.id,
            categoryName: category.name,
            transactionCount: totalTransactionCount,
            subcategoryCount,
            level,
          };
        }),
      );

      return statistics;
    } catch (error) {
      console.error('Error fetching category statistics:', error);
      throw new InternalServerErrorException('Could not fetch category statistics');
    }
  }

  /**
   * Helper method to count transactions in all subcategories recursively
   */
  private async countTransactionsInSubcategories(
    parentCategoryId: string,
    companyId: string,
  ): Promise<number> {
    // Buscar subcategorias diretas
    const subcategories = await this.prisma.category.findMany({
      where: {
        parentId: parentCategoryId,
        companyId,
        deletedAt: null,
      },
    });

    if (subcategories.length === 0) {
      return 0;
    }

    let totalCount = 0;

    for (const subcategory of subcategories) {
      // Contar transações diretas da subcategoria
      const directCount = await this.prisma.transaction.count({
        where: {
          categoryId: subcategory.id,
          companyId,
          deletedAt: null,
        },
      });

      // Contar transações das subcategorias filhas recursivamente
      const childrenCount = await this.countTransactionsInSubcategories(
        subcategory.id,
        companyId,
      );

      totalCount += directCount + childrenCount;
    }

    return totalCount;
  }

  /**
   * Helper method to calculate the hierarchical level of a category
   */
  private async calculateCategoryLevel(categoryId: string): Promise<number> {
    const category = await this.prisma.category.findUnique({
      where: { id: categoryId },
      select: { parentId: true },
    });

    if (!category || !category.parentId) {
      return 0; // Categoria raiz
    }

    // Recursivamente calcular o nível do pai + 1
    const parentLevel = await this.calculateCategoryLevel(category.parentId);
    return parentLevel + 1;
  }
}
