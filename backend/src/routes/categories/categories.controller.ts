// backend/src/routes/categories/categories.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Req,
  Query,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { CategoriesService, CategoryResponse } from './categories.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import { RequestWithUser } from '../../interfaces/request-with-user.interface';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';

@ApiTags('categories')
@Controller('categories')
@UseGuards(JwtAuthGuard) // Apply JWT authentication to all routes in this controller
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar uma nova categoria' })
  @ApiBody({ type: CreateCategoryDto })
  @ApiResponse({
    status: 201,
    description: 'Categoria criada com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  create(
    @Body() createCategoryDto: CreateCategoryDto,
    @Req() req: RequestWithUser,
  ): Promise<CategoryResponse> {
    return this.categoriesService.create(createCategoryDto, req.user);
  }

  @Get()
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todas as categorias' })
  @ApiQuery({ name: 'page', required: false, description: 'Página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite por página' })
  @ApiQuery({ name: 'transactionType', required: false, description: 'Tipo de transação (payable, receivable)' })
  @ApiResponse({ status: 200, description: 'Lista de categorias' })
  findAll(
    @Req() req: RequestWithUser,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('transactionType') transactionType?: string,
  ): Promise<{ data: CategoryResponse[]; total: number; page: number; limit: number }> {
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 10;

    // Basic validation for page and limit
    if (isNaN(pageNumber) || pageNumber < 1) {
      throw new BadRequestException('Invalid page number');
    }
    if (isNaN(limitNumber) || limitNumber < 1) {
      throw new BadRequestException('Invalid limit number');
    }

    return this.categoriesService.findAll(req.user, {
      page: pageNumber,
      limit: limitNumber,
      transactionType,
    });
  }

  @Get('tree')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Obter árvore de categorias' })
  @ApiQuery({ name: 'transactionType', required: false, description: 'Tipo de transação (payable, receivable)' })
  @ApiResponse({ status: 200, description: 'Árvore de categorias' })
  getTree(
    @Req() req: RequestWithUser,
    @Query('transactionType') transactionType?: string,
  ): Promise<CategoryResponse[]> {
    return this.categoriesService.getTree(req.user, transactionType);
  }

  @Get('statistics')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Obter estatísticas das categorias' })
  @ApiQuery({ name: 'transactionType', required: false, description: 'Tipo de transação (payable, receivable)' })
  @ApiResponse({ status: 200, description: 'Estatísticas das categorias' })
  getStatistics(
    @Req() req: RequestWithUser,
    @Query('transactionType') transactionType?: string,
  ) {
    return this.categoriesService.getStatistics(req.user, transactionType);
  }

  @Get(':id')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar uma categoria pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da categoria' })
  @ApiResponse({ status: 200, description: 'Categoria encontrada' })
  @ApiResponse({ status: 404, description: 'Categoria não encontrada' })
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ): Promise<CategoryResponse> {
    return this.categoriesService.findOne(id, req.user);
  }

  @Put(':id')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar uma categoria' })
  @ApiParam({ name: 'id', description: 'ID da categoria' })
  @ApiBody({ type: UpdateCategoryDto })
  @ApiResponse({ status: 200, description: 'Categoria atualizada com sucesso' })
  @ApiResponse({ status: 404, description: 'Categoria não encontrada' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCategoryDto: UpdateCategoryDto,
    @Req() req: RequestWithUser,
  ): Promise<CategoryResponse> {
    return this.categoriesService.update(id, updateCategoryDto, req.user);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover uma categoria' })
  @ApiParam({ name: 'id', description: 'ID da categoria' })
  @ApiResponse({ status: 204, description: 'Categoria removida com sucesso' })
  @ApiResponse({ status: 404, description: 'Categoria não encontrada' })
  @ApiResponse({ status: 400, description: 'Não é possível excluir esta categoria' })
  remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    return this.categoriesService.remove(id, req.user);
  }
}
