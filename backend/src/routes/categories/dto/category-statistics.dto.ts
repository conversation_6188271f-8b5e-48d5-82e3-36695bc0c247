import { ApiProperty } from '@nestjs/swagger';

export class CategoryStatisticsDto {
  @ApiProperty({ description: 'ID da categoria' })
  categoryId: string;

  @ApiProperty({ description: 'Nome da categoria' })
  categoryName: string;

  @ApiProperty({ description: 'Número total de transações (incluindo subcategorias)' })
  transactionCount: number;

  @ApiProperty({ description: 'Número de subcategorias diretas' })
  subcategoryCount: number;

  @ApiProperty({ description: 'Nível hierárquico da categoria (0 = raiz)' })
  level: number;
}

export class CategoryStatisticsResponseDto {
  @ApiProperty({ type: [CategoryStatisticsDto], description: 'Estatísticas das categorias' })
  data: CategoryStatisticsDto[];
}
