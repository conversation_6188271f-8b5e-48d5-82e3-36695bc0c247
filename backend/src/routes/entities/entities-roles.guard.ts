// backend/src/routes/entities/entities-roles.guard.ts
import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PrismaService } from '../../services/prisma.service';
import { ROLES_KEY } from '../../decorators/roles.decorator';

@Injectable()
export class EntitiesRolesGuard implements CanActivate {
  private readonly logger = new Logger(EntitiesRolesGuard.name);

  constructor(
    private reflector: Reflector,
    private prisma: PrismaService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // Se não houver papéis definidos, permite o acesso
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;
    
    // Log para debug
    this.logger.debug(`User object: ${JSON.stringify(user)}`);
    
    // Se não houver usuário, nega o acesso
    if (!user) {
      this.logger.warn('Acesso negado: usuário não encontrado no request');
      throw new ForbiddenException('Acesso negado: usuário não autenticado');
    }

    // Extrair userId e companyId do objeto user
    const userId = user.userId || user.id;
    let companyId = user.companyId;

    // Se não houver userId, nega o acesso
    if (!userId) {
      this.logger.warn(`Acesso negado: userId não encontrado. User: ${JSON.stringify(user)}`);
      throw new ForbiddenException('Acesso negado: usuário não autenticado');
    }

    // Se não houver companyId no token, buscar no banco de dados
    if (!companyId) {
      this.logger.debug(`CompanyId não encontrado no token, buscando no banco para usuário: ${userId}`);

      try {
        const userCompanyRole = await this.prisma.userCompanyRole.findFirst({
          where: {
            userId,
            deletedAt: null
          },
          select: {
            companyId: true
          }
        });

        if (userCompanyRole) {
          companyId = userCompanyRole.companyId;
          this.logger.debug(`CompanyId encontrado no banco: ${companyId}`);
        } else {
          this.logger.warn(`Nenhuma empresa encontrada para o usuário ${userId}`);
          throw new ForbiddenException('Acesso negado: usuário não possui empresa associada');
        }
      } catch (error) {
        this.logger.error(`Erro ao buscar empresa do usuário: ${error.message}`, error.stack);
        throw new ForbiddenException('Erro ao verificar permissões');
      }
    }

    // Se ainda não houver companyId, nega o acesso
    if (!companyId) {
      this.logger.warn(`Acesso negado: companyId não encontrado para usuário ${userId}`);
      throw new ForbiddenException('Acesso negado: informações de empresa incompletas');
    }

    // Adicionar o companyId ao objeto user para uso posterior
    user.companyId = companyId;

    try {
      // Verificar se o usuário tem algum dos papéis necessários
      const userRoles = await this.prisma.$queryRaw<{ name: string }[]>`
        SELECT r.name
        FROM user_company_roles ucr
        JOIN roles r ON ucr.role_id = r.id
        WHERE ucr.user_id = ${userId}::uuid
        AND ucr.company_id = ${companyId}::uuid
        AND ucr.deleted_at IS NULL
        AND r.deleted_at IS NULL
      `;

      // Log para debug
      this.logger.debug(`User roles: ${JSON.stringify(userRoles.map(r => r.name))}`);
      this.logger.debug(`Required roles: ${JSON.stringify(requiredRoles)}`);

      const userRoleNames = userRoles.map(role => role.name);
      
      // Verificar se o usuário tem pelo menos um dos papéis necessários
      const hasRequiredRole = requiredRoles.some(role => userRoleNames.includes(role));
      
      if (!hasRequiredRole) {
        this.logger.warn(`Acesso negado: usuário não tem os papéis necessários. User roles: ${JSON.stringify(userRoleNames)}, Required roles: ${JSON.stringify(requiredRoles)}`);
        throw new ForbiddenException('Acesso negado: você não tem permissão para acessar este recurso');
      }
      
      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      this.logger.error(`Erro ao verificar papéis: ${error.message}`, error.stack);
      throw new ForbiddenException('Erro ao verificar permissões');
    }
  }
}
