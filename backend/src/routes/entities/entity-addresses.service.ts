// backend/src/routes/entities/entity-addresses.service.ts
import { Injectable, NotFoundException, ConflictException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { AuthenticatedUser } from '../../interfaces/authenticated-user.interface';
import { Address } from '@prisma/client';
import { AddressTypeFrontend } from './dto/create-entity-address.dto';

@Injectable()
export class EntityAddressesService {
  constructor(private readonly prisma: PrismaService) {}

  // Mapeamento de tipos do frontend para nomes no banco de dados
  private readonly ADDRESS_TYPE_MAPPING: Record<AddressTypeFrontend, string> = {
    [AddressTypeFrontend.MAIN]: 'Comercial',
    [AddressTypeFrontend.BILLING]: 'Faturamento',
    [AddressTypeFrontend.SHIPPING]: 'Entrega',
  };

  /**
   * Buscar o ID do tipo de endereço pelo nome
   */
  private async getAddressTypeIdByName(typeName: string): Promise<string | undefined> {
    try {
      const addressType = await this.prisma.addressType.findUnique({
        where: { name: typeName },
      });
      return addressType?.id;
    } catch (error) {
      console.error(`Erro ao buscar tipo de endereço '${typeName}':`, error);
      return undefined;
    }
  }

  /**
   * Converter tipo do frontend para addressTypeId
   */
  private async convertFrontendTypeToAddressTypeId(type: AddressTypeFrontend): Promise<string | undefined> {
    const typeName = this.ADDRESS_TYPE_MAPPING[type];
    if (!typeName) {
      console.warn(`Tipo de endereço '${type}' não mapeado`);
      return undefined;
    }
    return await this.getAddressTypeIdByName(typeName);
  }

  /**
   * Verificar se a entidade pertence à empresa do usuário
   */
  private async verifyEntityBelongsToCompany(entityId: string, companyId: string): Promise<void> {
    const entity = await this.prisma.entity.findFirst({
      where: {
        id: entityId,
        companyId,
        deletedAt: null,
      },
    });

    if (!entity) {
      throw new NotFoundException(`Entidade com ID ${entityId} não encontrada ou não pertence à sua empresa`);
    }
  }

  /**
   * Criar um endereço para uma entidade
   */
  async createEntityAddress(
    entityId: string,
    addressData: {
      addressTypeId?: string;
      type?: AddressTypeFrontend;
      street: string;
      number?: string;
      complement?: string;
      district: string;
      city: string;
      state: string;
      zipCode: string;
      isDefault?: boolean;
    },
    user: AuthenticatedUser,
  ): Promise<Address> {
    const { companyId } = user;

    try {
      console.log(`[EntityAddressService] Criando endereço para entidade ${entityId}:`, {
        addressData,
        companyId,
        userId: user.userId
      });

      // Verificar se a entidade pertence à empresa do usuário
      await this.verifyEntityBelongsToCompany(entityId, companyId);

      // Resolver addressTypeId se não fornecido mas tipo do frontend foi fornecido
      let finalAddressTypeId = addressData.addressTypeId;
      if (!finalAddressTypeId && addressData.type) {
        console.log(`[EntityAddressService] Convertendo tipo '${addressData.type}' para addressTypeId`);
        finalAddressTypeId = await this.convertFrontendTypeToAddressTypeId(addressData.type);
        console.log(`[EntityAddressService] AddressTypeId convertido: ${finalAddressTypeId}`);
      }

      // Se isDefault for true, desmarcar outros endereços padrão da entidade
      if (addressData.isDefault) {
        console.log(`[EntityAddressService] Desmarcando outros endereços padrão da entidade ${entityId}`);
        await this.prisma.address.updateMany({
          where: {
            entityId,
            isDefault: true,
            deletedAt: null,
          },
          data: {
            isDefault: false,
          },
        });
      }

      // Criar o endereço
      // Para endereços de entidade, não definimos companyId para evitar conflito com a constraint
      const createData = {
        entityId,
        addressTypeId: finalAddressTypeId, // Usar o addressTypeId resolvido
        street: addressData.street,
        number: addressData.number,
        complement: addressData.complement,
        district: addressData.district,
        city: addressData.city,
        state: addressData.state,
        zipCode: addressData.zipCode,
        isDefault: addressData.isDefault || false,
      };

      console.log(`[EntityAddressService] Dados para criação do endereço:`, createData);

      const createdAddress = await this.prisma.address.create({
        data: createData,
        include: {
          addressType: true, // Incluir informações do tipo de endereço
        },
      });

      console.log(`[EntityAddressService] Endereço criado com sucesso:`, {
        id: createdAddress.id,
        entityId: createdAddress.entityId,
        addressTypeId: createdAddress.addressTypeId,
        addressType: createdAddress.addressType?.name
      });

      return createdAddress;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error creating entity address:', error);
      throw new InternalServerErrorException('Não foi possível criar o endereço para a entidade');
    }
  }

  /**
   * Buscar todos os endereços de uma entidade
   */
  async findAddressesByEntityId(
    entityId: string,
    user: AuthenticatedUser,
  ): Promise<Address[]> {
    const { companyId } = user;

    try {
      console.log(`[EntityAddressService] Buscando endereços para entidade ${entityId}, empresa ${companyId}`);

      // Verificar se a entidade pertence à empresa do usuário
      await this.verifyEntityBelongsToCompany(entityId, companyId);

      // Buscar todos os endereços da entidade
      const addresses = await this.prisma.address.findMany({
        where: {
          entityId,
          deletedAt: null,
        },
        include: {
          addressType: true, // Incluir informações do tipo de endereço
        },
        orderBy: {
          isDefault: 'desc',
        },
      });



      return addresses;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error finding addresses for entity ${entityId}:`, error);
      throw new InternalServerErrorException('Não foi possível buscar os endereços da entidade');
    }
  }

  /**
   * Buscar um endereço específico de uma entidade
   */
  async findEntityAddressById(
    addressId: string,
    entityId: string,
    user: AuthenticatedUser,
  ): Promise<Address> {
    const { companyId } = user;

    try {
      // Verificar se a entidade pertence à empresa do usuário
      await this.verifyEntityBelongsToCompany(entityId, companyId);

      // Buscar o endereço
      const address = await this.prisma.address.findFirst({
        where: {
          id: addressId,
          entityId,
          deletedAt: null,
        },
        include: {
          addressType: true, // Incluir informações do tipo de endereço
        },
      });

      if (!address) {
        throw new NotFoundException(`Endereço com ID ${addressId} não encontrado para a entidade ${entityId}`);
      }

      return address;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error finding address ${addressId} for entity ${entityId}:`, error);
      throw new InternalServerErrorException('Não foi possível buscar o endereço da entidade');
    }
  }

  /**
   * Atualizar um endereço de uma entidade
   */
  async updateEntityAddress(
    addressId: string,
    entityId: string,
    addressData: {
      addressTypeId?: string;
      street?: string;
      number?: string;
      complement?: string;
      district?: string;
      city?: string;
      state?: string;
      zipCode?: string;
      isDefault?: boolean;
    },
    user: AuthenticatedUser,
  ): Promise<Address> {
    const { companyId } = user;

    try {
      // Verificar se a entidade pertence à empresa do usuário
      await this.verifyEntityBelongsToCompany(entityId, companyId);

      // Verificar se o endereço existe e pertence à entidade
      const existingAddress = await this.prisma.address.findFirst({
        where: {
          id: addressId,
          entityId,
          deletedAt: null,
        },
      });

      if (!existingAddress) {
        throw new NotFoundException(`Endereço com ID ${addressId} não encontrado para a entidade ${entityId}`);
      }

      // Se isDefault for true, desmarcar outros endereços padrão da entidade
      if (addressData.isDefault) {
        await this.prisma.address.updateMany({
          where: {
            entityId,
            id: { not: addressId },
            isDefault: true,
            deletedAt: null,
          },
          data: {
            isDefault: false,
          },
        });
      }

      // Atualizar o endereço
      return await this.prisma.address.update({
        where: { id: addressId },
        data: addressData,
        include: {
          addressType: true, // Incluir informações do tipo de endereço
        },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error updating address ${addressId} for entity ${entityId}:`, error);
      throw new InternalServerErrorException('Não foi possível atualizar o endereço da entidade');
    }
  }

  /**
   * Remover um endereço de uma entidade (soft delete)
   */
  async removeEntityAddress(
    addressId: string,
    entityId: string,
    user: AuthenticatedUser,
  ): Promise<void> {
    const { companyId } = user;

    try {
      // Verificar se a entidade pertence à empresa do usuário
      await this.verifyEntityBelongsToCompany(entityId, companyId);

      // Verificar se o endereço existe e pertence à entidade
      const existingAddress = await this.prisma.address.findFirst({
        where: {
          id: addressId,
          entityId,
          deletedAt: null,
        },
      });

      if (!existingAddress) {
        throw new NotFoundException(`Endereço com ID ${addressId} não encontrado para a entidade ${entityId}`);
      }

      // Remover o endereço (soft delete)
      await this.prisma.address.update({
        where: { id: addressId },
        data: { deletedAt: new Date() },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error removing address ${addressId} for entity ${entityId}:`, error);
      throw new InternalServerErrorException('Não foi possível remover o endereço da entidade');
    }
  }
}
