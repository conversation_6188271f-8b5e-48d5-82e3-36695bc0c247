// backend/src/routes/entities/dto/create-entity.dto.ts
import {
  IsString,
  IsEnum,
  IsOptional,
  IsEmail,
  IsBoolean,
  IsNumber,
  IsDecimal,
  IsInt,
  MaxLength,
  Matches,
  ValidateIf,
  Min,
  Max,
  Validate,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { EntityType, PersonType, EntityStatus, TaxRegime } from '../../../common/enums/entity.enum';
import { isValidCPF, isValidCNPJ } from '../../../common/validators/brazilian-documents.validator';
import { FlexibleUrlValidator } from '../../../common/validators/url.validator';

export class CreateEntityDto {
  @ApiProperty({
    description: 'Nome da entidade (Razão Social para PJ)',
    example: 'Empresa ABC Ltda',
    maxLength: 255
  })
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Tipo de relacionamento comercial',
    example: 'customer',
    enum: EntityType
  })
  @IsEnum(EntityType, { message: 'Tipo deve ser customer, supplier ou both' })
  entityType: EntityType;

  @ApiProperty({
    description: 'Tipo de pessoa',
    example: 'company',
    enum: PersonType
  })
  @IsEnum(PersonType, { message: 'Tipo de pessoa deve ser individual ou company' })
  personType: PersonType;

  @ApiPropertyOptional({
    description: 'Nome fantasia (apenas para PJ)',
    example: 'ABC Comércio',
    maxLength: 255
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  tradeName?: string;

  @ApiPropertyOptional({
    description: 'CNPJ da empresa (obrigatório para PJ)',
    example: '12.345.678/0001-90',
    maxLength: 18
  })
  @ValidateIf(o => o.personType === PersonType.COMPANY)
  @IsString()
  @MaxLength(18)
  @Matches(/^\d{2}\.\d{3}\.\d{3}\/\d{4}\-\d{2}$/, {
    message: 'CNPJ deve estar no formato XX.XXX.XXX/XXXX-XX',
  })
  @Transform(({ value }) => {
    if (!value) return value;
    // Validate CNPJ using our custom validator
    if (!isValidCNPJ(value)) {
      throw new Error('CNPJ inválido');
    }
    return value;
  })
  cnpj?: string;

  @ApiPropertyOptional({
    description: 'CPF da pessoa (obrigatório para PF)',
    example: '123.456.789-01',
    maxLength: 14
  })
  @ValidateIf(o => o.personType === PersonType.INDIVIDUAL)
  @IsString()
  @MaxLength(14)
  @Matches(/^\d{3}\.\d{3}\.\d{3}\-\d{2}$/, {
    message: 'CPF deve estar no formato XXX.XXX.XXX-XX',
  })
  @Transform(({ value }) => {
    if (!value) return value;
    // Validate CPF using our custom validator
    if (!isValidCPF(value)) {
      throw new Error('CPF inválido');
    }
    return value;
  })
  cpf?: string;

  @ApiPropertyOptional({
    description: 'Inscrição Estadual',
    example: '123.456.789.012',
    maxLength: 20
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  stateRegistration?: string;

  @ApiPropertyOptional({
    description: 'Inscrição Municipal',
    example: '12345678',
    maxLength: 20
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  municipalRegistration?: string;

  @ApiPropertyOptional({
    description: 'RG (para PF)',
    example: '12.345.678-9',
    maxLength: 20
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  rg?: string;

  @ApiPropertyOptional({
    description: 'Órgão emissor do RG',
    example: 'SSP-SP',
    maxLength: 10
  })
  @IsOptional()
  @IsString()
  @MaxLength(10)
  rgIssuer?: string;

  @ApiPropertyOptional({
    description: 'Regime tributário',
    example: 'simple_national',
    enum: TaxRegime
  })
  @IsOptional()
  @IsEnum(TaxRegime)
  taxRegime?: TaxRegime;

  @ApiPropertyOptional({
    description: 'Status da entidade',
    example: 'active',
    enum: EntityStatus,
    default: EntityStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(EntityStatus)
  status?: EntityStatus = EntityStatus.ACTIVE;

  // Contact Information
  @ApiPropertyOptional({
    description: 'Telefone principal',
    example: '(11) 3333-4444',
    maxLength: 20
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  @Matches(/^\(\d{2}\)\s\d{4,5}\-\d{4}$/, {
    message: 'Telefone deve estar no formato (XX) XXXXX-XXXX ou (XX) XXXX-XXXX',
  })
  phone?: string;

  @ApiPropertyOptional({
    description: 'Celular',
    example: '(11) 99999-9999',
    maxLength: 20
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  @Matches(/^\(\d{2}\)\s\d{4,5}\-\d{4}$/, {
    message: 'Celular deve estar no formato (XX) XXXXX-XXXX',
  })
  mobile?: string;

  @ApiPropertyOptional({
    description: 'Email principal',
    example: '<EMAIL>',
    maxLength: 255
  })
  @IsOptional()
  @IsEmail()
  @MaxLength(255)
  email?: string;

  @ApiPropertyOptional({
    description: 'Website',
    example: 'https://www.empresa.com.br',
    maxLength: 255
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  @Validate(FlexibleUrlValidator)
  website?: string;

  @ApiPropertyOptional({
    description: 'Nome do contato',
    example: 'João Silva',
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  contact?: string;

  // Commercial Information
  @ApiPropertyOptional({
    description: 'Limite de crédito',
    example: 10000.00,
    minimum: 0
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = parseFloat(value);
    return isNaN(num) ? undefined : num;
  })
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'creditLimit must be a number conforming to the specified constraints' })
  @Min(0, { message: 'creditLimit must not be less than 0' })
  creditLimit?: number;

  @ApiPropertyOptional({
    description: 'Prazo de pagamento padrão (dias)',
    example: 30,
    minimum: 1,
    maximum: 365
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = parseInt(value);
    return isNaN(num) ? undefined : num;
  })
  @IsInt({ message: 'paymentTermDays must be an integer number' })
  @Min(1, { message: 'paymentTermDays must not be less than 1' })
  @Max(365, { message: 'paymentTermDays must not be greater than 365' })
  paymentTermDays?: number;

  @ApiPropertyOptional({
    description: 'Percentual de desconto padrão',
    example: 5.0,
    minimum: 0,
    maximum: 100
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    const num = parseFloat(value);
    return isNaN(num) ? undefined : num;
  })
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'discountPercentage must be a number conforming to the specified constraints' })
  @Min(0, { message: 'discountPercentage must not be less than 0' })
  @Max(100, { message: 'discountPercentage must not be greater than 100' })
  discountPercentage?: number;

  // Tax Information
  @ApiPropertyOptional({
    description: 'Contribuinte de ICMS',
    example: true,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  icmsTaxpayer?: boolean = false;

  @ApiPropertyOptional({
    description: 'Optante pelo Simples Nacional',
    example: true,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  simpleNational?: boolean = false;

  @ApiPropertyOptional({
    description: 'Retém impostos',
    example: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  withholdTaxes?: boolean = false;

  @ApiPropertyOptional({
    description: 'Enviar NFe por email',
    example: true,
    default: true
  })
  @IsOptional()
  @IsBoolean()
  sendNfe?: boolean = true;

  @ApiPropertyOptional({
    description: 'Email para envio de NFe',
    example: '<EMAIL>',
    maxLength: 255
  })
  @IsOptional()
  @IsEmail()
  @MaxLength(255)
  nfeEmail?: string;

  // Banking Information
  @ApiPropertyOptional({
    description: 'Código do banco',
    example: '001',
    maxLength: 10
  })
  @IsOptional()
  @IsString()
  @MaxLength(10)
  bankCode?: string;

  @ApiPropertyOptional({
    description: 'Nome do banco',
    example: 'Banco do Brasil',
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  bankName?: string;

  @ApiPropertyOptional({
    description: 'Agência',
    example: '1234',
    maxLength: 10
  })
  @IsOptional()
  @IsString()
  @MaxLength(10)
  agency?: string;

  @ApiPropertyOptional({
    description: 'Conta',
    example: '56789',
    maxLength: 20
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  account?: string;

  @ApiPropertyOptional({
    description: 'Dígito da conta',
    example: '0',
    maxLength: 2
  })
  @IsOptional()
  @IsString()
  @MaxLength(2)
  accountDigit?: string;

  @ApiPropertyOptional({
    description: 'Chave PIX',
    example: '12.345.678/0001-90',
    maxLength: 255
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  pixKey?: string;

  @ApiPropertyOptional({
    description: 'Observações',
    example: 'Cliente preferencial'
  })
  @IsOptional()
  @IsString()
  notes?: string;

  // Legacy field for backward compatibility
  @ApiPropertyOptional({
    description: 'Tipo legado (para compatibilidade)',
    example: 'customer',
    enum: ['customer', 'supplier']
  })
  @IsOptional()
  @IsEnum(['customer', 'supplier'])
  type?: 'customer' | 'supplier';
}
