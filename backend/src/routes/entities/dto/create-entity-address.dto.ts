// backend/src/routes/entities/dto/create-entity-address.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  IsBoolean,
  Length,
  Matches,
  IsEnum,
} from 'class-validator';

// Enum para tipos de endereço do frontend
export enum AddressTypeFrontend {
  MAIN = 'main',
  BILLING = 'billing',
  SHIPPING = 'shipping',
}

export class CreateEntityAddressDto {
  @ApiProperty({
    description: 'ID do tipo de endereço (opcional)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID(4, { message: 'ID do tipo de endereço inválido' })
  @IsOptional()
  addressTypeId?: string;

  @ApiProperty({
    description: 'Tipo de endereço do frontend (opcional, alternativa ao addressTypeId)',
    example: 'main',
    enum: AddressTypeFrontend,
    required: false,
  })
  @IsEnum(AddressTypeFrontend, { message: 'Tipo de endereço inválido' })
  @IsOptional()
  type?: AddressTypeFrontend;

  @ApiProperty({ description: 'Rua/Logradouro', example: 'Avenida Paulista' })
  @IsString()
  @IsNotEmpty({ message: 'A rua é obrigatória' })
  street: string;

  @ApiProperty({ description: 'Número', example: '1000', required: false })
  @IsString()
  @IsOptional()
  number?: string;

  @ApiProperty({
    description: 'Complemento',
    example: 'Sala 123',
    required: false,
  })
  @IsString()
  @IsOptional()
  complement?: string;

  @ApiProperty({ description: 'Bairro', example: 'Bela Vista' })
  @IsString()
  @IsNotEmpty({ message: 'O bairro é obrigatório' })
  district: string;

  @ApiProperty({ description: 'Cidade', example: 'São Paulo' })
  @IsString()
  @IsNotEmpty({ message: 'A cidade é obrigatória' })
  city: string;

  @ApiProperty({ description: 'Estado (UF)', example: 'SP' })
  @IsString()
  @IsNotEmpty({ message: 'O estado é obrigatório' })
  @Length(2, 2, { message: 'O estado deve ter exatamente 2 caracteres (UF)' })
  state: string;

  @ApiProperty({ description: 'CEP', example: '01310-100' })
  @IsString()
  @IsNotEmpty({ message: 'O CEP é obrigatório' })
  @Matches(/^\d{5}-\d{3}$/, {
    message: 'CEP inválido. Use o formato: XXXXX-XXX',
  })
  zipCode: string;

  @ApiProperty({
    description: 'Endereço padrão',
    example: true,
    required: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;
}
