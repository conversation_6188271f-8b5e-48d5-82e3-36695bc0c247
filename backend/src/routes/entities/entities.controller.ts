// backend/src/routes/entities/entities.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Query,
  Put,
  Req,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import { EntitiesService } from './entities.service';
import { CreateEntityDto, UpdateEntityDto } from './dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import { EntitiesRolesGuard } from './entities-roles.guard';
import { Roles } from '../../decorators/roles.decorator';
import { Role } from '../../constants/roles.constant';
import { EntityType, PersonType, EntityStatus } from '../../common/enums/entity.enum';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { Entity } from '@prisma/client';
import { RequestWithUser } from '../../interfaces/request-with-user.interface';

@ApiTags('entities')
@Controller('entities')
@UseGuards(JwtAuthGuard, EntitiesRolesGuard) // Aplicar autenticação JWT e verificação de papéis
export class EntitiesController {
  constructor(private readonly entitiesService: EntitiesService) {}

  @Post()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER, Role.SALES_MANAGER, Role.PURCHASING_MANAGER)
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar uma nova entidade' })
  @ApiBody({ type: CreateEntityDto })
  @ApiResponse({
    status: 201,
    description: 'Entidade criada com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  create(
    @Body() createEntityDto: CreateEntityDto,
    @Req() req: RequestWithUser,
  ) {
    // Mapear os campos do token JWT para a interface AuthenticatedUser
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.entitiesService.create(createEntityDto, user);
  }

  @Get()
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.MEMBER, Role.OPERADOR, Role.VISUALIZADOR,
         Role.FINANCE_MANAGER, Role.FINANCE_ANALYST, Role.FINANCE_VIEWER,
         Role.SALES_MANAGER, Role.SALES_ANALYST, Role.SALES_VIEWER,
         Role.PURCHASING_MANAGER, Role.PURCHASING_ANALYST, Role.PURCHASING_VIEWER)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todas as entidades com filtros avançados' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Página atual (padrão: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página (padrão: 10)',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Filtrar por tipo legado (customer, supplier)',
  })
  @ApiQuery({
    name: 'entityType',
    required: false,
    description: 'Filtrar por tipo de entidade (customer, supplier, both)',
    enum: EntityType,
  })
  @ApiQuery({
    name: 'personType',
    required: false,
    description: 'Filtrar por tipo de pessoa (individual, company)',
    enum: PersonType,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filtrar por status (active, inactive, suspended, blocked)',
    enum: EntityStatus,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Buscar por nome, email, CNPJ ou CPF',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de entidades',
  })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  findAll(
    @Req() req: RequestWithUser,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('type') type?: string,
    @Query('entityType') entityType?: EntityType,
    @Query('personType') personType?: PersonType,
    @Query('status') status?: EntityStatus,
    @Query('search') search?: string,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId
    };
    return this.entitiesService.findAll(user, {
      page,
      limit,
      type,
      entityType,
      personType,
      status,
      search
    });
  }

  @Get('clients')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.MEMBER, Role.OPERADOR, Role.VISUALIZADOR,
         Role.FINANCE_MANAGER, Role.FINANCE_ANALYST, Role.FINANCE_VIEWER,
         Role.SALES_MANAGER, Role.SALES_ANALYST, Role.SALES_VIEWER)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todos os clientes' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Página atual (padrão: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página (padrão: 10)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Buscar por nome, email, CNPJ ou CPF',
  })
  @ApiQuery({
    name: 'personType',
    required: false,
    description: 'Filtrar por tipo de pessoa (individual, company)',
    enum: PersonType,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filtrar por status',
    enum: EntityStatus,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de clientes',
  })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  findClients(
    @Req() req: RequestWithUser,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('personType') personType?: PersonType,
    @Query('status') status?: EntityStatus,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId
    };
    return this.entitiesService.findClients(user, { page, limit, search, personType, status });
  }

  @Get('suppliers')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.MEMBER, Role.OPERADOR, Role.VISUALIZADOR,
         Role.FINANCE_MANAGER, Role.FINANCE_ANALYST, Role.FINANCE_VIEWER,
         Role.PURCHASING_MANAGER, Role.PURCHASING_ANALYST, Role.PURCHASING_VIEWER)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todos os fornecedores' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Página atual (padrão: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página (padrão: 10)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Buscar por nome, email, CNPJ ou CPF',
  })
  @ApiQuery({
    name: 'personType',
    required: false,
    description: 'Filtrar por tipo de pessoa (individual, company)',
    enum: PersonType,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filtrar por status',
    enum: EntityStatus,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de fornecedores',
  })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  findSuppliers(
    @Req() req: RequestWithUser,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('personType') personType?: PersonType,
    @Query('status') status?: EntityStatus,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId
    };
    return this.entitiesService.findSuppliers(user, { page, limit, search, personType, status });
  }

  @Get('mixed')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.MEMBER, Role.OPERADOR, Role.VISUALIZADOR,
         Role.FINANCE_MANAGER, Role.FINANCE_ANALYST, Role.FINANCE_VIEWER,
         Role.SALES_MANAGER, Role.SALES_ANALYST, Role.SALES_VIEWER,
         Role.PURCHASING_MANAGER, Role.PURCHASING_ANALYST, Role.PURCHASING_VIEWER)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar entidades mistas (cliente e fornecedor)' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Página atual (padrão: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página (padrão: 10)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Buscar por nome, email, CNPJ ou CPF',
  })
  @ApiQuery({
    name: 'personType',
    required: false,
    description: 'Filtrar por tipo de pessoa (individual, company)',
    enum: PersonType,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filtrar por status',
    enum: EntityStatus,
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de entidades mistas',
  })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  findMixed(
    @Req() req: RequestWithUser,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('personType') personType?: PersonType,
    @Query('status') status?: EntityStatus,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId
    };
    return this.entitiesService.findMixed(user, { page, limit, search, personType, status });
  }

  @Get('statistics')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.MEMBER, Role.OPERADOR, Role.VISUALIZADOR,
         Role.FINANCE_MANAGER, Role.FINANCE_ANALYST, Role.FINANCE_VIEWER,
         Role.SALES_MANAGER, Role.SALES_ANALYST, Role.SALES_VIEWER,
         Role.PURCHASING_MANAGER, Role.PURCHASING_ANALYST, Role.PURCHASING_VIEWER)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Obter estatísticas das entidades' })
  @ApiResponse({
    status: 200,
    description: 'Estatísticas das entidades',
  })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  getStatistics(@Req() req: RequestWithUser) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId
    };
    return this.entitiesService.getStatistics(user);
  }

  @Get(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.MEMBER, Role.OPERADOR, Role.VISUALIZADOR,
         Role.FINANCE_MANAGER, Role.FINANCE_ANALYST, Role.FINANCE_VIEWER,
         Role.SALES_MANAGER, Role.SALES_ANALYST, Role.SALES_VIEWER,
         Role.PURCHASING_MANAGER, Role.PURCHASING_ANALYST, Role.PURCHASING_VIEWER)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar uma entidade pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da entidade' })
  @ApiResponse({
    status: 200,
    description: 'Entidade encontrada',
  })
  @ApiResponse({ status: 404, description: 'Entidade não encontrada' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.entitiesService.findOne(id, user);
  }

  @Put(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE,
         Role.FINANCE_MANAGER, Role.SALES_MANAGER, Role.PURCHASING_MANAGER)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar uma entidade' })
  @ApiParam({ name: 'id', description: 'ID da entidade' })
  @ApiBody({ type: UpdateEntityDto })
  @ApiResponse({
    status: 200,
    description: 'Entidade atualizada com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Entidade não encontrada' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateEntityDto: UpdateEntityDto,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.entitiesService.update(id, updateEntityDto, user);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.ADMINISTRADOR, Role.GERENTE, Role.FINANCE_MANAGER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover uma entidade' })
  @ApiParam({ name: 'id', description: 'ID da entidade' })
  @ApiResponse({ status: 204, description: 'Entidade removida com sucesso' })
  @ApiResponse({ status: 404, description: 'Entidade não encontrada' })
  @ApiResponse({ status: 403, description: 'Acesso negado' })
  remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = {
      userId: req.user.userId,
      email: req.user.email,
      companyId: req.user.companyId,
      id: req.user.userId // Usar userId como id para compatibilidade
    };
    return this.entitiesService.remove(id, user);
  }
}
