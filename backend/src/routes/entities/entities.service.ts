// backend/src/routes/entities/entities.service.ts
import { Injectable, NotFoundException, InternalServerErrorException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../services/prisma.service';
import { CreateEntityDto, UpdateEntityDto } from './dto';
import { Prisma, Entity } from '@prisma/client';
import { AuthenticatedUser } from '../../interfaces/authenticated-user.interface';
import { EntityType, PersonType, EntityStatus, EntityEnumHelper } from '../../common/enums/entity.enum';
import { isValidCPF, isValidCNPJ, cleanDocument } from '../../common/validators/brazilian-documents.validator';

@Injectable()
export class EntitiesService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Validar CNPJ único por empresa
   */
  private async validateUniqueCnpj(cnpj: string, companyId: string, entityId?: string): Promise<void> {
    if (!cnpj) return;

    const where: Prisma.EntityWhereInput = {
      cnpj,
      companyId,
      deletedAt: null,
      ...(entityId && { id: { not: entityId } }),
    };

    const existingEntity = await this.prisma.entity.findFirst({ where });

    if (existingEntity) {
      throw new ConflictException(`Já existe uma entidade com o CNPJ ${cnpj} nesta empresa`);
    }
  }

  /**
   * Validar CPF único por empresa
   */
  private async validateUniqueCpf(cpf: string, companyId: string, entityId?: string): Promise<void> {
    if (!cpf) return;

    const where: Prisma.EntityWhereInput = {
      cpf,
      companyId,
      deletedAt: null,
      ...(entityId && { id: { not: entityId } }),
    };

    const existingEntity = await this.prisma.entity.findFirst({ where });

    if (existingEntity) {
      throw new ConflictException(`Já existe uma entidade com o CPF ${cpf} nesta empresa`);
    }
  }

  /**
   * Validar dados obrigatórios baseado no tipo de pessoa
   */
  private validateRequiredFieldsByPersonType(data: CreateEntityDto | UpdateEntityDto): void {
    const { personType, cnpj, cpf, stateRegistration } = data;

    if (personType === PersonType.COMPANY) {
      if (!cnpj) {
        throw new BadRequestException('CNPJ é obrigatório para pessoa jurídica');
      }
      if (!isValidCNPJ(cnpj)) {
        throw new BadRequestException('CNPJ inválido');
      }
    }

    if (personType === PersonType.INDIVIDUAL) {
      if (!cpf) {
        throw new BadRequestException('CPF é obrigatório para pessoa física');
      }
      if (!isValidCPF(cpf)) {
        throw new BadRequestException('CPF inválido');
      }
    }
  }

  /**
   * Validar tipo de entidade
   */
  private validateEntityType(type: string): void {
    if (!EntityEnumHelper.isValidEntityType(type)) {
      const validTypes = EntityEnumHelper.getEntityTypes();
      throw new BadRequestException(`Tipo de entidade inválido. Valores permitidos: ${validTypes.join(', ')}`);
    }
  }

  /**
   * Validar consistência dos dados
   */
  private validateDataConsistency(data: CreateEntityDto | UpdateEntityDto): void {
    const { personType, cnpj, cpf } = data;

    // Se é pessoa jurídica, não deve ter CPF
    if (personType === PersonType.COMPANY && cpf) {
      throw new BadRequestException('Pessoa jurídica não deve ter CPF');
    }

    // Se é pessoa física, não deve ter CNPJ
    if (personType === PersonType.INDIVIDUAL && cnpj) {
      throw new BadRequestException('Pessoa física não deve ter CNPJ');
    }
  }

  /**
   * Criar uma nova entidade
   */
  async create(createEntityDto: CreateEntityDto, user: AuthenticatedUser): Promise<Entity> {
    const { companyId } = user;
    const { cnpj, cpf, entityType, personType } = createEntityDto;

    try {
      // Validar dados obrigatórios baseado no tipo de pessoa
      this.validateRequiredFieldsByPersonType(createEntityDto);

      // Validar consistência dos dados
      this.validateDataConsistency(createEntityDto);

      // Validar tipo de entidade
      if (entityType) {
        this.validateEntityType(entityType);
      }

      // Validar CNPJ único por empresa
      if (cnpj) {
        await this.validateUniqueCnpj(cnpj, companyId);
      }

      // Validar CPF único por empresa
      if (cpf) {
        await this.validateUniqueCpf(cpf, companyId);
      }

      // Preparar dados para criação
      const entityData = {
        ...createEntityDto,
        // Manter compatibilidade com campo legado 'type'
        type: createEntityDto.type || createEntityDto.entityType,
        // Definir valores padrão apenas quando necessário
        status: createEntityDto.status || EntityStatus.ACTIVE,
        // Campos numéricos opcionais - usar valores do banco se undefined
        creditLimit: createEntityDto.creditLimit !== undefined ? createEntityDto.creditLimit : 0,
        paymentTermDays: createEntityDto.paymentTermDays !== undefined ? createEntityDto.paymentTermDays : 30,
        discountPercentage: createEntityDto.discountPercentage !== undefined ? createEntityDto.discountPercentage : 0,
        // Campos booleanos
        icmsTaxpayer: createEntityDto.icmsTaxpayer || false,
        simpleNational: createEntityDto.simpleNational || false,
        withholdTaxes: createEntityDto.withholdTaxes || false,
        sendNfe: createEntityDto.sendNfe !== undefined ? createEntityDto.sendNfe : true,
      };

      return await this.prisma.entity.create({
        data: {
          ...entityData,
          company: { connect: { id: companyId } },
        },
      });
    } catch (error) {
      if (error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }
      console.error('Error creating entity:', error);
      throw new InternalServerErrorException('Could not create entity');
    }
  }

  /**
   * Buscar todas as entidades com filtros e paginação
   */
  async findAll(
    user: AuthenticatedUser,
    filters: {
      page?: number;
      limit?: number;
      type?: string;
      entityType?: EntityType;
      personType?: PersonType;
      status?: EntityStatus;
      search?: string;
    } = {}
  ): Promise<{ data: Entity[]; total: number; page: number; limit: number }> {
    const { companyId } = user;
    const {
      page = 1,
      limit = 10,
      type,
      entityType,
      personType,
      status,
      search
    } = filters;

    const skip = (page - 1) * limit;

    const where: Prisma.EntityWhereInput = {
      companyId,
      deletedAt: null,
      // Suporte ao campo legado 'type'
      ...(type && { type }),
      // Novos filtros
      ...(entityType && { entityType }),
      ...(personType && { personType }),
      ...(status && { status }),
      // Busca por texto
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { tradeName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { cnpj: { contains: search.replace(/\D/g, '') } },
          { cpf: { contains: search.replace(/\D/g, '') } },
        ],
      }),
    };

    try {
      const [entities, total] = await Promise.all([
        this.prisma.entity.findMany({
          where,
          skip,
          take: limit,
          orderBy: { name: 'asc' },
          include: {
            addresses: {
              where: { isDefault: true },
              take: 1,
            },
          },
        }),
        this.prisma.entity.count({ where }),
      ]);

      return {
        data: entities,
        total,
        page,
        limit,
      };
    } catch (error) {
      console.error('Error fetching entities:', error);
      throw new InternalServerErrorException('Could not fetch entities');
    }
  }

  /**
   * Buscar uma entidade pelo ID
   */
  async findOne(id: string, user: AuthenticatedUser): Promise<Entity> {
    const { companyId } = user;

    try {
      const entity = await this.prisma.entity.findFirst({
        where: {
          id,
          companyId,
          deletedAt: null,
        },
      });

      if (!entity) {
        throw new NotFoundException(`Entity with ID ${id} not found`);
      }

      return entity;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`Error fetching entity with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not fetch entity');
    }
  }

  /**
   * Atualizar uma entidade
   */
  async update(id: string, updateEntityDto: UpdateEntityDto, user: AuthenticatedUser): Promise<Entity> {
    const { companyId } = user;
    const { cnpj, cpf, entityType, personType } = updateEntityDto;

    // Verificar se a entidade existe e pertence à empresa do usuário
    const existingEntity = await this.findOne(id, user);

    try {
      // Se o tipo de pessoa está sendo alterado, validar dados obrigatórios
      if (personType && personType !== existingEntity.personType) {
        this.validateRequiredFieldsByPersonType(updateEntityDto);
      }

      // Validar consistência dos dados
      if (personType || cnpj || cpf) {
        this.validateDataConsistency({
          ...existingEntity,
          ...updateEntityDto,
        } as any);
      }

      // Validar tipo de entidade
      if (entityType) {
        this.validateEntityType(entityType);
      }

      // Validar CNPJ único por empresa
      if (cnpj && cnpj !== existingEntity.cnpj) {
        await this.validateUniqueCnpj(cnpj, companyId, id);
      }

      // Validar CPF único por empresa
      if (cpf && cpf !== existingEntity.cpf) {
        await this.validateUniqueCpf(cpf, companyId, id);
      }

      // Preparar dados para atualização
      const updateData = {
        ...updateEntityDto,
        // Manter compatibilidade com campo legado 'type'
        ...(updateEntityDto.entityType && { type: updateEntityDto.entityType }),
      };

      return await this.prisma.entity.update({
        where: { id },
        data: updateData,
        include: {
          addresses: {
            where: { isDefault: true },
            take: 1,
          },
        },
      });
    } catch (error) {
      if (error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }
      console.error(`Error updating entity with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not update entity');
    }
  }

  /**
   * Remover uma entidade (soft delete)
   */
  async remove(id: string, user: AuthenticatedUser): Promise<void> {
    // Verificar se a entidade existe e pertence à empresa do usuário
    await this.findOne(id, user);

    try {
      await this.prisma.entity.update({
        where: { id },
        data: { deletedAt: new Date() },
      });
    } catch (error) {
      console.error(`Error removing entity with ID ${id}:`, error);
      throw new InternalServerErrorException('Could not remove entity');
    }
  }

  /**
   * Buscar clientes
   */
  async findClients(
    user: AuthenticatedUser,
    filters: { page?: number; limit?: number; search?: string; personType?: PersonType; status?: EntityStatus } = {}
  ): Promise<{ data: Entity[]; total: number; page: number; limit: number }> {
    return this.findAll(user, {
      ...filters,
      entityType: EntityType.CUSTOMER
    });
  }

  /**
   * Buscar fornecedores
   */
  async findSuppliers(
    user: AuthenticatedUser,
    filters: { page?: number; limit?: number; search?: string; personType?: PersonType; status?: EntityStatus } = {}
  ): Promise<{ data: Entity[]; total: number; page: number; limit: number }> {
    return this.findAll(user, {
      ...filters,
      entityType: EntityType.SUPPLIER
    });
  }

  /**
   * Buscar entidades mistas (cliente e fornecedor)
   */
  async findMixed(
    user: AuthenticatedUser,
    filters: { page?: number; limit?: number; search?: string; personType?: PersonType; status?: EntityStatus } = {}
  ): Promise<{ data: Entity[]; total: number; page: number; limit: number }> {
    return this.findAll(user, {
      ...filters,
      entityType: EntityType.BOTH
    });
  }

  /**
   * Buscar entidades por tipo de pessoa
   */
  async findByPersonType(
    user: AuthenticatedUser,
    personType: PersonType,
    filters: { page?: number; limit?: number; search?: string; entityType?: EntityType; status?: EntityStatus } = {}
  ): Promise<{ data: Entity[]; total: number; page: number; limit: number }> {
    return this.findAll(user, {
      ...filters,
      personType
    });
  }

  /**
   * Buscar estatísticas das entidades
   */
  async getStatistics(user: AuthenticatedUser): Promise<{
    total: number;
    customers: number;
    suppliers: number;
    mixed: number;
    individuals: number;
    companies: number;
    active: number;
    inactive: number;
  }> {
    const { companyId } = user;

    try {
      const [
        total,
        customers,
        suppliers,
        mixed,
        individuals,
        companies,
        active,
        inactive,
      ] = await Promise.all([
        this.prisma.entity.count({ where: { companyId, deletedAt: null } }),
        this.prisma.entity.count({ where: { companyId, deletedAt: null, entityType: EntityType.CUSTOMER } }),
        this.prisma.entity.count({ where: { companyId, deletedAt: null, entityType: EntityType.SUPPLIER } }),
        this.prisma.entity.count({ where: { companyId, deletedAt: null, entityType: EntityType.BOTH } }),
        this.prisma.entity.count({ where: { companyId, deletedAt: null, personType: PersonType.INDIVIDUAL } }),
        this.prisma.entity.count({ where: { companyId, deletedAt: null, personType: PersonType.COMPANY } }),
        this.prisma.entity.count({ where: { companyId, deletedAt: null, status: EntityStatus.ACTIVE } }),
        this.prisma.entity.count({ where: { companyId, deletedAt: null, status: EntityStatus.INACTIVE } }),
      ]);

      return {
        total,
        customers,
        suppliers,
        mixed,
        individuals,
        companies,
        active,
        inactive,
      };
    } catch (error) {
      console.error('Error fetching entity statistics:', error);
      throw new InternalServerErrorException('Could not fetch entity statistics');
    }
  }
}
