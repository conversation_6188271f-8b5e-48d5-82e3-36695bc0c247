import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  Put,
  Delete,
} from '@nestjs/common';
import { CompanyCodeService } from '../services/company-code.service';
import { JwtAuthGuard } from '../middlewares/jwt-auth.guard';
import { Request } from 'express';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import {
  CreateCompanyCodeDto,
  CompanyCodeDto,
  JoinCompanyByCodeDto,
  UpdateCompanyCodeDto,
  CompanyCodeValidationDto,
  CompanyCodeListDto,
} from '../models/company-code.model';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    userId: string;
    email: string;
    companyId?: string;
  };
}

@ApiTags('company-codes')
@Controller('company-codes')
export class CompanyCodeController {
  constructor(private readonly companyCodeService: CompanyCodeService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar código de empresa' })
  @ApiBody({ type: CreateCompanyCodeDto })
  @ApiResponse({
    status: 201,
    description: 'Código criado com sucesso',
    type: CompanyCodeDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Sem permissão para criar código' })
  @ApiResponse({ status: 404, description: 'Empresa ou papel não encontrado' })
  async create(
    @Body() createCodeDto: CreateCompanyCodeDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<CompanyCodeDto> {
    return this.companyCodeService.create(createCodeDto, req.user.userId);
  }

  @Get('validate/:code')
  @ApiOperation({ summary: 'Validar código de empresa' })
  @ApiParam({ name: 'code', description: 'Código da empresa' })
  @ApiResponse({
    status: 200,
    description: 'Validação do código',
    type: CompanyCodeValidationDto,
  })
  async validateCode(
    @Param('code') code: string,
  ): Promise<CompanyCodeValidationDto> {
    return this.companyCodeService.validateCode(code);
  }

  @Post('join')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Associar-se à empresa usando código' })
  @ApiBody({ type: JoinCompanyByCodeDto })
  @ApiResponse({
    status: 200,
    description: 'Usuário associado à empresa com sucesso',
  })
  @ApiResponse({ status: 400, description: 'Código inválido ou expirado' })
  @ApiResponse({ status: 404, description: 'Código não encontrado' })
  @ApiResponse({ status: 409, description: 'Usuário já associado à empresa' })
  async joinCompanyByCode(
    @Body() joinDto: JoinCompanyByCodeDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<{ message: string }> {
    await this.companyCodeService.joinCompanyByCode(joinDto, req.user.userId);
    return { message: 'Associado à empresa com sucesso' };
  }

  @Get('company/:companyId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar códigos de uma empresa' })
  @ApiParam({ name: 'companyId', description: 'ID da empresa' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Página atual (padrão: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Limite de itens por página (padrão: 10)',
  })
  @ApiResponse({
    status: 200,
    description: 'Lista de códigos',
    type: CompanyCodeListDto,
  })
  @ApiResponse({ status: 403, description: 'Sem permissão para visualizar códigos' })
  @ApiResponse({ status: 404, description: 'Empresa não encontrada' })
  async findByCompany(
    @Param('companyId') companyId: string,
    @Req() req: AuthenticatedRequest,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ): Promise<CompanyCodeListDto> {
    return this.companyCodeService.findByCompany(
      companyId,
      req.user.userId,
      page,
      limit,
    );
  }

  @Put(':codeId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar código de empresa' })
  @ApiParam({ name: 'codeId', description: 'ID do código' })
  @ApiBody({ type: UpdateCompanyCodeDto })
  @ApiResponse({
    status: 200,
    description: 'Código atualizado com sucesso',
    type: CompanyCodeDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 403, description: 'Sem permissão para atualizar código' })
  @ApiResponse({ status: 404, description: 'Código não encontrado' })
  async update(
    @Param('codeId') codeId: string,
    @Body() updateDto: UpdateCompanyCodeDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<CompanyCodeDto> {
    return this.companyCodeService.update(codeId, updateDto, req.user.userId);
  }

  @Delete(':codeId')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remover código de empresa' })
  @ApiParam({ name: 'codeId', description: 'ID do código' })
  @ApiResponse({
    status: 200,
    description: 'Código removido com sucesso',
  })
  @ApiResponse({ status: 403, description: 'Sem permissão para remover código' })
  @ApiResponse({ status: 404, description: 'Código não encontrado' })
  async remove(
    @Param('codeId') codeId: string,
    @Req() req: AuthenticatedRequest,
  ): Promise<{ message: string }> {
    await this.companyCodeService.remove(codeId, req.user.userId);
    return { message: 'Código removido com sucesso' };
  }
}
