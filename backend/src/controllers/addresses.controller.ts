import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { AddressesService } from '../services/addresses.service';
import { JwtAuthGuard } from '../middlewares/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import {
  AddressDto,
  CreateAddressDto,
  UpdateAddressDto,
} from '../models/address.model';

@ApiTags('addresses')
@Controller('addresses')
export class AddressesController {
  constructor(private addressesService: AddressesService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Listar todos os endereços com paginação' })
  @ApiQuery({ name: 'page', required: false, description: '<PERSON><PERSON><PERSON><PERSON> da página', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página', type: Number })
  @ApiQuery({ name: 'search', required: false, description: 'Termo de busca', type: String })
  @ApiResponse({ status: 200, description: 'Lista de endereços' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
  ) {
    const pageNumber = page ? parseInt(page, 10) : 1;
    const limitNumber = limit ? parseInt(limit, 10) : 10;

    // Validação básica para page e limit
    if (isNaN(pageNumber) || pageNumber < 1) {
      throw new BadRequestException('Número de página inválido');
    }
    if (isNaN(limitNumber) || limitNumber < 1) {
      throw new BadRequestException('Limite de itens por página inválido');
    }

    return this.addressesService.findAll({
      page: pageNumber,
      limit: limitNumber,
      search,
    });
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Criar um novo endereço' })
  @ApiBody({ type: CreateAddressDto })
  @ApiResponse({
    status: 201,
    description: 'Endereço criado com sucesso',
    type: AddressDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async create(
    @Body() createAddressDto: CreateAddressDto,
  ): Promise<AddressDto> {
    console.log('Dados recebidos para criar endereço:', JSON.stringify(createAddressDto, null, 2));
    return this.addressesService.create(createAddressDto);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Buscar um endereço pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do endereço' })
  @ApiResponse({
    status: 200,
    description: 'Endereço encontrado',
    type: AddressDto,
  })
  @ApiResponse({ status: 404, description: 'Endereço não encontrado' })
  async findOne(@Param('id') id: string): Promise<AddressDto> {
    return this.addressesService.findOne(id);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Atualizar um endereço' })
  @ApiParam({ name: 'id', description: 'ID do endereço' })
  @ApiBody({ type: UpdateAddressDto })
  @ApiResponse({
    status: 200,
    description: 'Endereço atualizado com sucesso',
    type: AddressDto,
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Endereço não encontrado' })
  async update(
    @Param('id') id: string,
    @Body() updateAddressDto: UpdateAddressDto,
  ): Promise<AddressDto> {
    return this.addressesService.update(id, updateAddressDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover um endereço' })
  @ApiParam({ name: 'id', description: 'ID do endereço' })
  @ApiResponse({ status: 204, description: 'Endereço removido com sucesso' })
  @ApiResponse({ status: 404, description: 'Endereço não encontrado' })
  async remove(@Param('id') id: string): Promise<void> {
    return this.addressesService.remove(id);
  }
}
