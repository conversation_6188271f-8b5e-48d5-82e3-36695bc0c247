import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  Matches,
} from 'class-validator';

export class AddressDto {
  @ApiProperty({
    description: 'ID único do endereço',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({ description: 'Rua/Logradouro', example: 'Avenida Paulista' })
  street: string;

  @ApiProperty({ description: 'Número', example: '1000', required: false })
  number?: string;

  @ApiProperty({
    description: 'Complemento',
    example: 'Sala 123',
    required: false,
  })
  complement?: string;

  @ApiProperty({ description: 'Bairro', example: 'Bela Vista' })
  district: string;

  @ApiProperty({ description: 'Cidade', example: 'São Paulo' })
  city: string;

  @ApiProperty({ description: 'Estado (UF)', example: 'SP' })
  state: string;

  @ApiProperty({ description: 'CEP', example: '01310-100' })
  zipCode: string;

  @ApiProperty({
    description: 'Indica se é o endereço padrão',
    example: false,
    required: false
  })
  isDefault?: boolean;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Data de exclusão',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  deletedAt?: Date;
}

export class CreateAddressDto {
  @ApiProperty({ description: 'Rua/Logradouro', example: 'Avenida Paulista' })
  @IsString()
  @IsNotEmpty()
  street: string;

  @ApiProperty({ description: 'Número', example: '1000', required: false })
  @IsString()
  @IsOptional()
  number?: string;

  @ApiProperty({
    description: 'Complemento',
    example: 'Sala 123',
    required: false,
  })
  @IsString()
  @IsOptional()
  complement?: string;

  @ApiProperty({ description: 'Bairro', example: 'Bela Vista' })
  @IsString()
  @IsNotEmpty()
  district: string;

  // Campo alternativo para compatibilidade com frontend
  @ApiProperty({ description: 'Bairro (alternativo)', example: 'Bela Vista', required: false })
  @IsString()
  @IsOptional()
  neighborhood?: string;

  @ApiProperty({ description: 'Cidade', example: 'São Paulo' })
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty({ description: 'Estado (UF)', example: 'SP' })
  @IsString()
  @IsNotEmpty()
  @Length(2, 2, { message: 'O estado deve ter exatamente 2 caracteres (UF)' })
  state: string;

  @ApiProperty({ description: 'CEP', example: '01310-100' })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{5}-?\d{3}$/, {
    message: 'CEP inválido. Use o formato: XXXXX-XXX ou XXXXXXXX',
  })
  zipCode: string;

  // Campos opcionais para compatibilidade com frontend
  @ApiProperty({ description: 'País', example: 'Brasil', required: false })
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty({ description: 'ID da entidade', required: false })
  @IsString()
  @IsOptional()
  entityId?: string;

  @ApiProperty({ description: 'Endereço padrão', required: false })
  @IsOptional()
  isDefault?: boolean;
}

export class UpdateAddressDto {
  @ApiProperty({
    description: 'Rua/Logradouro',
    example: 'Avenida Paulista',
    required: false,
  })
  @IsString()
  @IsOptional()
  street?: string;

  @ApiProperty({ description: 'Número', example: '1000', required: false })
  @IsString()
  @IsOptional()
  number?: string;

  @ApiProperty({
    description: 'Complemento',
    example: 'Sala 123',
    required: false,
  })
  @IsString()
  @IsOptional()
  complement?: string;

  @ApiProperty({
    description: 'Bairro',
    example: 'Bela Vista',
    required: false,
  })
  @IsString()
  @IsOptional()
  district?: string;

  @ApiProperty({ description: 'Cidade', example: 'São Paulo', required: false })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiProperty({ description: 'Estado (UF)', example: 'SP', required: false })
  @IsString()
  @IsOptional()
  @Length(2, 2, { message: 'O estado deve ter exatamente 2 caracteres (UF)' })
  state?: string;

  @ApiProperty({ description: 'CEP', example: '01310-100', required: false })
  @IsString()
  @IsOptional()
  @Matches(/^\d{5}-\d{3}$/, {
    message: 'CEP inválido. Use o formato: XXXXX-XXX',
  })
  zipCode?: string;

  @ApiProperty({
    description: 'Indica se é o endereço padrão',
    example: false,
    required: false
  })
  @IsOptional()
  isDefault?: boolean;
}
