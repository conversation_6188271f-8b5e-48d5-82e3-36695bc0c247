import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import {
  AddressDto,
  CreateAddressDto,
  UpdateAddressDto,
} from '../models/address.model';
import { Prisma } from '@prisma/client';

// Interface para resultados de consultas SQL nativas
interface AddressRawResult {
  id: string;
  street: string;
  number: string | null;
  complement: string | null;
  district: string;
  city: string;
  state: string;
  zip_code: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

// Função auxiliar para converter null para undefined
function nullToUndefined<T>(value: T | null): T | undefined {
  return value === null ? undefined : value;
}

@Injectable()
export class AddressesService {
  constructor(private prisma: PrismaService) {}

  async findAll(options: {
    page: number;
    limit: number;
    search?: string;
  }): Promise<{
    items: AddressDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page, limit, search } = options;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE deleted_at IS NULL';
    const params: any[] = [];

    if (search) {
      whereClause += ` AND (
        street ILIKE $${params.length + 1} OR
        district ILIKE $${params.length + 2} OR
        city ILIKE $${params.length + 3} OR
        state ILIKE $${params.length + 4} OR
        zip_code ILIKE $${params.length + 5}
      )`;
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // Buscar endereços com paginação
    const addresses = await this.prisma.$queryRawUnsafe<AddressRawResult[]>(`
      SELECT * FROM addresses
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `, ...params, limit, offset);

    // Contar total de registros
    const totalResult = await this.prisma.$queryRawUnsafe<[{ count: bigint }]>(`
      SELECT COUNT(*) as count FROM addresses ${whereClause}
    `, ...params);

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    const items = addresses.map(address => ({
      id: address.id,
      street: address.street,
      number: nullToUndefined(address.number),
      complement: nullToUndefined(address.complement),
      district: address.district,
      city: address.city,
      state: address.state,
      zipCode: address.zip_code,
      createdAt: address.created_at,
      updatedAt: address.updated_at,
      deletedAt: nullToUndefined(address.deleted_at),
    }));

    return {
      items,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async create(createAddressDto: CreateAddressDto): Promise<AddressDto> {
    try {
      console.log('Service - Dados recebidos:', JSON.stringify(createAddressDto, null, 2));

      // Mapear neighborhood para district se necessário
      const district = createAddressDto.district || createAddressDto.neighborhood;

      console.log('Service - District mapeado:', district);

      if (!district) {
        console.log('Service - Erro: Bairro é obrigatório');
        throw new Error('Bairro é obrigatório');
      }

      // Usar SQL nativo para criar o endereço
      const address = await this.prisma.$queryRaw<AddressRawResult[]>`
        INSERT INTO addresses (
          id,
          street,
          number,
          complement,
          district,
          city,
          state,
          zip_code,
          updated_at
        ) VALUES (
          gen_random_uuid(),
          ${createAddressDto.street},
          ${createAddressDto.number},
          ${createAddressDto.complement},
          ${district},
          ${createAddressDto.city},
          ${createAddressDto.state},
          ${createAddressDto.zipCode},
          NOW()
        )
        RETURNING *
      `;

      return {
        id: address[0].id,
        street: address[0].street,
        number: nullToUndefined(address[0].number),
        complement: nullToUndefined(address[0].complement),
        district: address[0].district,
        city: address[0].city,
        state: address[0].state,
        zipCode: address[0].zip_code,
        createdAt: address[0].created_at,
        updatedAt: address[0].updated_at,
        deletedAt: nullToUndefined(address[0].deleted_at),
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(id: string): Promise<AddressDto> {
    const address = await this.prisma.$queryRaw<AddressRawResult[]>`
      SELECT * FROM addresses WHERE id = ${id}::uuid AND deleted_at IS NULL
    `;

    if (address.length === 0) {
      throw new NotFoundException('Endereço não encontrado');
    }

    return {
      id: address[0].id,
      street: address[0].street,
      number: nullToUndefined(address[0].number),
      complement: nullToUndefined(address[0].complement),
      district: address[0].district,
      city: address[0].city,
      state: address[0].state,
      zipCode: address[0].zip_code,
      createdAt: address[0].created_at,
      updatedAt: address[0].updated_at,
      deletedAt: nullToUndefined(address[0].deleted_at),
    };
  }

  async update(
    id: string,
    updateAddressDto: UpdateAddressDto,
  ): Promise<AddressDto> {
    // Verificar se o endereço existe
    const existingAddress = await this.prisma.$queryRaw<AddressRawResult[]>`
      SELECT * FROM addresses WHERE id = ${id}::uuid AND deleted_at IS NULL
    `;

    if (existingAddress.length === 0) {
      throw new NotFoundException('Endereço não encontrado');
    }

    try {
      // Construir a consulta SQL de atualização dinamicamente
      let updateQuery = 'UPDATE addresses SET updated_at = NOW() ';
      const values: any[] = [];
      let paramIndex = 1;

      if (updateAddressDto.street !== undefined) {
        updateQuery += `, street = $${paramIndex++} `;
        values.push(updateAddressDto.street);
      }
      if (updateAddressDto.number !== undefined) {
        updateQuery += `, number = $${paramIndex++} `;
        values.push(updateAddressDto.number);
      }
      if (updateAddressDto.complement !== undefined) {
        updateQuery += `, complement = $${paramIndex++} `;
        values.push(updateAddressDto.complement);
      }
      if (updateAddressDto.district !== undefined) {
        updateQuery += `, district = $${paramIndex++} `;
        values.push(updateAddressDto.district);
      }
      if (updateAddressDto.city !== undefined) {
        updateQuery += `, city = $${paramIndex++} `;
        values.push(updateAddressDto.city);
      }
      if (updateAddressDto.state !== undefined) {
        updateQuery += `, state = $${paramIndex++} `;
        values.push(updateAddressDto.state);
      }
      if (updateAddressDto.zipCode !== undefined) {
        updateQuery += `, zip_code = $${paramIndex++} `;
        values.push(updateAddressDto.zipCode);
      }

      updateQuery += ` WHERE id = $${paramIndex++} RETURNING *`;
      values.push(id);

      // Executar a consulta de atualização
      const updatedAddress = await this.prisma.$queryRawUnsafe<
        AddressRawResult[]
      >(updateQuery, ...values);

      return {
        id: updatedAddress[0].id,
        street: updatedAddress[0].street,
        number: nullToUndefined(updatedAddress[0].number),
        complement: nullToUndefined(updatedAddress[0].complement),
        district: updatedAddress[0].district,
        city: updatedAddress[0].city,
        state: updatedAddress[0].state,
        zipCode: updatedAddress[0].zip_code,
        createdAt: updatedAddress[0].created_at,
        updatedAt: updatedAddress[0].updated_at,
        deletedAt: nullToUndefined(updatedAddress[0].deleted_at),
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    // Verificar se o endereço existe
    const existingAddress = await this.prisma.$queryRaw<AddressRawResult[]>`
      SELECT * FROM addresses WHERE id = ${id}::uuid AND deleted_at IS NULL
    `;

    if (existingAddress.length === 0) {
      throw new NotFoundException('Endereço não encontrado');
    }

    // Soft delete do endereço usando SQL nativo
    await this.prisma.$queryRaw`
      UPDATE addresses 
      SET deleted_at = NOW() 
      WHERE id = ${id}::uuid
    `;
  }
}
