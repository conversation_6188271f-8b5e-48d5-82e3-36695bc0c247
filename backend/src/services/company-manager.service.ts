import {
  Injectable,
  ConflictException,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from './prisma.service';
import {
  CompanyDto,
  CreateCompanyDto,
  UpdateCompanyDto,
} from '../models/company.model';
import { Prisma } from '@prisma/client';
import { AccessControlUtil } from '../utils/access-control.util';
import { QueryUtil } from '../utils/query.util';
import { mapToCompanyDto } from '../utils/mappers.util';

@Injectable()
export class CompanyManagerService {
  private readonly logger = new Logger(CompanyManagerService.name);

  constructor(
    private prisma: PrismaService,
    private accessControlUtil: AccessControlUtil,
    private queryUtil: QueryUtil,
  ) {}

  /**
   * Cria uma nova empresa
   * @param createCompanyDto Dados para criação da empresa
   * @param userId ID do usuário criador
   * @returns Empresa criada
   */
  async create(
    createCompanyDto: CreateCompanyDto,
    userId: string,
  ): Promise<CompanyDto> {
    try {
      // Verificar se já existe empresa com o mesmo CNPJ
      if (await this.queryUtil.checkCompanyExistsByCnpj(createCompanyDto.cnpj)) {
        throw new ConflictException('Já existe uma empresa com este CNPJ');
      }

      // Criar a empresa usando uma transação para garantir que tanto a empresa quanto
      // a associação do usuário com a empresa sejam criadas juntas
      return await this.prisma.$transaction(async (prisma) => {
        // Criar a empresa
        const company = await prisma.company.create({
          data: {
            name: createCompanyDto.name,
            cnpj: createCompanyDto.cnpj,
            phone: createCompanyDto.phone,
            email: createCompanyDto.email,
            address: createCompanyDto.addressId 
              ? { connect: { id: createCompanyDto.addressId } }
              : undefined,
            logo: createCompanyDto.logo,
            active: createCompanyDto.active ?? true,
            calendar_type: createCompanyDto.calendarType ?? 'standard',
          } as any,
        });

        // Buscar o papel de administrador ou criar se não existir
        let adminRole = await prisma.$queryRaw<
          { id: string; company_id: string; name: string }[]
        >`
          SELECT * FROM roles 
          WHERE company_id = ${company.id}::uuid 
          AND name = 'Administrador'
          LIMIT 1
        `;

        if (adminRole.length === 0) {
          adminRole = await prisma.$queryRaw<
            { id: string; company_id: string; name: string }[]
          >`
            INSERT INTO roles (id, company_id, name, description, is_admin, created_at, updated_at)
            VALUES (gen_random_uuid(), ${company.id}::uuid, 'Administrador', 'Acesso completo ao sistema', true, NOW(), NOW())
            RETURNING *
          `;
        }

        // Associar o usuário criador à empresa com o papel de administrador
        await prisma.$queryRaw`
          INSERT INTO user_company_roles (id, user_id, company_id, role_id, created_at, updated_at)
          VALUES (gen_random_uuid(), ${userId}::uuid, ${company.id}::uuid, ${adminRole[0].id}::uuid, NOW(), NOW())
        `;

        return mapToCompanyDto(company);
      });
    } catch (error) {
      this.logger.error(`Erro ao criar empresa: ${error.message}`, error.stack);
      if (error instanceof ConflictException) {
        throw error;
      }
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Já existe uma empresa com este CNPJ');
        }
      }
      throw new BadRequestException('Falha ao criar empresa');
    }
  }

  /**
   * Atualiza uma empresa existente
   * @param id ID da empresa
   * @param updateCompanyDto Dados para atualização da empresa
   * @param userId ID do usuário que está fazendo a atualização
   * @returns Empresa atualizada
   */
  async update(
    id: string,
    updateCompanyDto: UpdateCompanyDto,
    userId: string,
  ): Promise<CompanyDto> {
    try {
      // Verificar se o usuário tem acesso de administrador à empresa
      await this.accessControlUtil.validateUserIsCompanyAdmin(userId, id);

      // Verificar se a empresa existe
      const existingCompany = await this.queryUtil.findCompanyById(id);

      // Verificar se o CNPJ já está em uso por outra empresa (se foi fornecido)
      if (
        updateCompanyDto.cnpj &&
        updateCompanyDto.cnpj !== existingCompany.cnpj
      ) {
        const isDuplicate = await this.prisma.$queryRaw<{ count: string }[]>`
          SELECT COUNT(*) as count
          FROM companies
          WHERE cnpj = ${updateCompanyDto.cnpj}
          AND id != ${id}::uuid
          AND deleted_at IS NULL
        `;

        if (parseInt(isDuplicate[0].count, 10) > 0) {
          throw new ConflictException('CNPJ já está em uso por outra empresa');
        }
      }

      // Construir dados para atualização
      const updateData: any = {};

      if (updateCompanyDto.name !== undefined) updateData.name = updateCompanyDto.name;
      if (updateCompanyDto.cnpj !== undefined) updateData.cnpj = updateCompanyDto.cnpj;
      if (updateCompanyDto.phone !== undefined) updateData.phone = updateCompanyDto.phone;
      if (updateCompanyDto.email !== undefined) updateData.email = updateCompanyDto.email;
      if (updateCompanyDto.addressId !== undefined) {
        if (updateCompanyDto.addressId) {
          updateData.address = { connect: { id: updateCompanyDto.addressId } };
        } else {
          updateData.address = { disconnect: true };
        }
      }
      if (updateCompanyDto.logo !== undefined) updateData.logo = updateCompanyDto.logo;
      if (updateCompanyDto.active !== undefined) updateData.active = updateCompanyDto.active;
      if (updateCompanyDto.calendarType !== undefined) updateData.calendar_type = updateCompanyDto.calendarType;

      // Atualizar empresa
      const updatedCompany = await this.prisma.company.update({
        where: { id },
        data: updateData,
      });

      return mapToCompanyDto(updatedCompany);
    } catch (error) {
      this.logger.error(`Erro ao atualizar empresa: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Remove uma empresa (exclusão lógica)
   * @param id ID da empresa
   * @param userId ID do usuário que está fazendo a remoção
   */
  async remove(id: string, userId: string): Promise<void> {
    try {
      // Verificar se o usuário tem acesso de administrador à empresa
      await this.accessControlUtil.validateUserIsCompanyAdmin(userId, id);

      // Verificar se a empresa existe
      await this.queryUtil.findCompanyById(id);

      // Marcar a empresa como excluída
      await this.prisma.company.update({
        where: { id },
        data: { deleted_at: new Date() },
      });
    } catch (error) {
      this.logger.error(`Erro ao remover empresa: ${error.message}`, error.stack);
      throw error;
    }
  }
} 