import {
  Injectable,
  ConflictException,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from './prisma.service';
import {
  CompanyDto,
  CreateCompanyDto,
  UpdateCompanyDto,
  CompanySettingsDto,
} from '../models/company.model';
import { Prisma } from '@prisma/client';

// Função auxiliar para converter null para undefined
function nullToUndefined<T>(value: T | null): T | undefined {
  return value === null ? undefined : value;
}

// Interface para resultados de consultas SQL nativas
interface CompanyRawResult {
  id: string;
  name: string;
  cnpj: string;
  phone: string | null;
  email: string | null;
  address_id: string | null;
  logo: string | null;
  active: boolean;
  calendar_type: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

@Injectable()
export class CompaniesService {
  constructor(private prisma: PrismaService) {}

  async create(
    createCompanyDto: CreateCompanyDto,
    userId: string,
  ): Promise<CompanyDto> {
    try {
      // Verificar se já existe empresa com o mesmo CNPJ
      const existingCompany = await this.prisma.$queryRaw<CompanyRawResult[]>`
        SELECT * FROM "companies" WHERE cnpj = ${createCompanyDto.cnpj} AND deleted_at IS NULL
      `;

      if (existingCompany.length > 0) {
        throw new ConflictException('Já existe uma empresa com este CNPJ');
      }

      // Buscar o usuário atual para verificar seu status
      const currentUser = await this.prisma.$queryRaw<
        { id: string; status: string }[]
      >`
        SELECT id, status FROM users
        WHERE id = ${userId}::uuid
        AND deleted_at IS NULL
      `;

      if (currentUser.length === 0) {
        throw new NotFoundException('Usuário não encontrado');
      }

      // Criar a empresa usando uma transação para garantir que tanto a empresa quanto
      // a associação do usuário com a empresa sejam criadas juntas
      return await this.prisma.$transaction(async (prisma) => {
        // Criar a empresa
        const company = await prisma.company.create({
          data: {
            name: createCompanyDto.name,
            cnpj: createCompanyDto.cnpj,
            phone: createCompanyDto.phone,
            email: createCompanyDto.email,
            address_id: createCompanyDto.addressId,
            logo: createCompanyDto.logo,
            active: createCompanyDto.active ?? true,
            calendar_type: createCompanyDto.calendarType ?? 'standard',
          },
        });

        // Buscar o papel de administrador ou criar se não existir
        let adminRole = await prisma.$queryRaw<
          { id: string; company_id: string; name: string }[]
        >`
          SELECT * FROM roles
          WHERE company_id = ${company.id}::uuid
          AND name = 'Administrador'
          LIMIT 1
        `;

        if (adminRole.length === 0) {
          adminRole = await prisma.$queryRaw<
            { id: string; company_id: string; name: string }[]
          >`
            INSERT INTO roles (id, company_id, name, description, is_admin, created_at, updated_at)
            VALUES (gen_random_uuid(), ${company.id}::uuid, 'Administrador', 'Acesso completo ao sistema', true, NOW(), NOW())
            RETURNING *
          `;
        }

        // Associar o usuário criador à empresa com o papel de administrador
        await prisma.$queryRaw`
          INSERT INTO user_company_roles (id, user_id, company_id, role_id, created_at, updated_at)
          VALUES (gen_random_uuid(), ${userId}::uuid, ${company.id}::uuid, ${adminRole[0].id}::uuid, NOW(), NOW())
        `;

        // Atualizar status do usuário se estava sem empresa
        if (currentUser[0].status === 'no_company') {
          await prisma.user.update({
            where: { id: userId },
            data: { status: 'active' },
          });
        }

        return {
          id: company.id,
          name: company.name,
          cnpj: company.cnpj,
          phone: nullToUndefined(company.phone),
          email: nullToUndefined(company.email),
          addressId: nullToUndefined(company.address_id),
          logo: nullToUndefined(company.logo),
          active: company.active || false,
          calendarType: company.calendar_type || '',
          createdAt: company.created_at || new Date(),
          updatedAt: company.updated_at || new Date(),
          deletedAt: nullToUndefined(company.deleted_at),
        };
      });
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Já existe uma empresa com este CNPJ');
        }
      }
      throw error;
    }
  }

  async findAll(
    page = 1,
    limit = 10,
    userId: string,
  ): Promise<{
    data: CompanyDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    const skip = (page - 1) * limit;

    // Buscar empresas às quais o usuário tem acesso
    const userCompanies = await this.prisma.$queryRaw<{ company_id: string }[]>`
      SELECT DISTINCT ucr.company_id 
      FROM user_company_roles ucr
      WHERE ucr.user_id = ${userId}::uuid AND ucr.deleted_at IS NULL
    `;

    if (userCompanies.length === 0) {
      return { data: [], total: 0, page, limit };
    }

    const companyIds = userCompanies.map((uc) => uc.company_id);
    const placeholders = companyIds.map((_, i) => `$${i + 1}::uuid`).join(',');

    // Contar total de empresas
    const countQuery = `
      SELECT COUNT(*) as total
      FROM companies c
      WHERE c.id IN (${placeholders})
      AND c.deleted_at IS NULL
    `;

    const totalResult = await this.prisma.$queryRawUnsafe<[{ total: string }]>(
      countQuery,
      ...companyIds,
    );

    const total = parseInt(totalResult[0].total, 10);

    // Buscar empresas com paginação
    const companiesQuery = `
      SELECT c.*
      FROM companies c
      WHERE c.id IN (${placeholders})
      AND c.deleted_at IS NULL
      ORDER BY c.name
      LIMIT ${limit} OFFSET ${skip}
    `;

    const companies = await this.prisma.$queryRawUnsafe<CompanyRawResult[]>(
      companiesQuery,
      ...companyIds,
    );

    const data = companies.map((company) => ({
      id: company.id,
      name: company.name,
      cnpj: company.cnpj,
      phone: nullToUndefined(company.phone),
      email: nullToUndefined(company.email),
      addressId: nullToUndefined(company.address_id),
      logo: nullToUndefined(company.logo),
      active: company.active || false,
      calendarType: company.calendar_type || '',
      createdAt: company.created_at || new Date(),
      updatedAt: company.updated_at || new Date(),
      deletedAt: nullToUndefined(company.deleted_at),
    }));

    return { data, total, page, limit };
  }

  async findOne(id: string, userId: string): Promise<CompanyDto> {
    // Verificar se o usuário tem acesso à empresa
    const hasAccess = await this.prisma.$queryRaw<{ count: string }[]>`
      SELECT COUNT(*) as count
      FROM user_company_roles ucr
      WHERE ucr.user_id = ${userId}::uuid
      AND ucr.company_id = ${id}::uuid
      AND ucr.deleted_at IS NULL
    `;

    if (parseInt(hasAccess[0].count, 10) === 0) {
      throw new ForbiddenException('Você não tem acesso a esta empresa');
    }

    const company = await this.prisma.$queryRaw<CompanyRawResult[]>`
      SELECT * FROM companies WHERE id = ${id}::uuid AND deleted_at IS NULL
    `;

    if (company.length === 0) {
      throw new NotFoundException('Empresa não encontrada');
    }

    return {
      id: company[0].id,
      name: company[0].name,
      cnpj: company[0].cnpj,
      phone: nullToUndefined(company[0].phone),
      email: nullToUndefined(company[0].email),
      addressId: nullToUndefined(company[0].address_id),
      logo: nullToUndefined(company[0].logo),
      active: company[0].active || false,
      calendarType: company[0].calendar_type || '',
      createdAt: company[0].created_at || new Date(),
      updatedAt: company[0].updated_at || new Date(),
      deletedAt: nullToUndefined(company[0].deleted_at),
    };
  }

  async update(
    id: string,
    updateCompanyDto: UpdateCompanyDto,
    userId: string,
  ): Promise<CompanyDto> {
    // Verificar se o usuário tem acesso de administrador à empresa
    const hasAdminAccess = await this.prisma.$queryRaw<{ count: string }[]>`
      SELECT COUNT(*) as count
      FROM user_company_roles ucr
      JOIN roles r ON ucr.role_id = r.id
      WHERE ucr.user_id = ${userId}::uuid
      AND ucr.company_id = ${id}::uuid
      AND r.name = 'Administrador'
      AND ucr.deleted_at IS NULL
      AND r.deleted_at IS NULL
    `;

    if (parseInt(hasAdminAccess[0].count, 10) === 0) {
      throw new ForbiddenException(
        'Você não tem permissão para atualizar esta empresa',
      );
    }

    // Verificar se a empresa existe
    const existingCompany = await this.prisma.$queryRaw<CompanyRawResult[]>`
      SELECT * FROM companies WHERE id = ${id}::uuid AND deleted_at IS NULL
    `;

    if (existingCompany.length === 0) {
      throw new NotFoundException('Empresa não encontrada');
    }

    // Verificar se está tentando alterar o CNPJ para um que já existe
    if (
      updateCompanyDto.cnpj &&
      updateCompanyDto.cnpj !== existingCompany[0].cnpj
    ) {
      const companyWithSameCnpj = await this.prisma.$queryRaw<
        CompanyRawResult[]
      >`
        SELECT * FROM companies WHERE cnpj = ${updateCompanyDto.cnpj} AND id != ${id}::uuid AND deleted_at IS NULL
      `;

      if (companyWithSameCnpj.length > 0) {
        throw new ConflictException('Já existe uma empresa com este CNPJ');
      }
    }

    try {
      // Construir objeto de atualização apenas com campos fornecidos
      const updateData: any = {};
      if (updateCompanyDto.name !== undefined)
        updateData.name = updateCompanyDto.name;
      if (updateCompanyDto.cnpj !== undefined)
        updateData.cnpj = updateCompanyDto.cnpj;
      if (updateCompanyDto.phone !== undefined)
        updateData.phone = updateCompanyDto.phone;
      if (updateCompanyDto.email !== undefined)
        updateData.email = updateCompanyDto.email;
      if (updateCompanyDto.addressId !== undefined)
        updateData.address_id = updateCompanyDto.addressId;
      if (updateCompanyDto.logo !== undefined)
        updateData.logo = updateCompanyDto.logo;
      if (updateCompanyDto.active !== undefined)
        updateData.active = updateCompanyDto.active;
      if (updateCompanyDto.calendarType !== undefined)
        updateData.calendar_type = updateCompanyDto.calendarType;

      // Atualizar a empresa
      const company = await this.prisma.company.update({
        where: { id },
        data: updateData,
      });

      return {
        id: company.id,
        name: company.name,
        cnpj: company.cnpj,
        phone: nullToUndefined(company.phone),
        email: nullToUndefined(company.email),
        addressId: nullToUndefined(company.address_id),
        logo: nullToUndefined(company.logo),
        active: company.active || false,
        calendarType: company.calendar_type || '',
        createdAt: company.created_at || new Date(),
        updatedAt: company.updated_at || new Date(),
        deletedAt: company.deleted_at ? company.deleted_at : undefined,
      };
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Já existe uma empresa com este CNPJ');
        }
      }
      throw error;
    }
  }

  async remove(id: string, userId: string): Promise<void> {
    // Verificar se o usuário tem acesso de administrador à empresa
    const hasAdminAccess = await this.prisma.$queryRaw<{ count: string }[]>`
      SELECT COUNT(*) as count
      FROM user_company_roles ucr
      JOIN roles r ON ucr.role_id = r.id
      WHERE ucr.user_id = ${userId}::uuid
      AND ucr.company_id = ${id}::uuid
      AND r.name = 'Administrador'
      AND ucr.deleted_at IS NULL
      AND r.deleted_at IS NULL
    `;

    if (parseInt(hasAdminAccess[0].count, 10) === 0) {
      throw new ForbiddenException(
        'Você não tem permissão para remover esta empresa',
      );
    }

    // Verificar se a empresa existe
    const existingCompany = await this.prisma.$queryRaw<CompanyRawResult[]>`
      SELECT * FROM companies WHERE id = ${id}::uuid AND deleted_at IS NULL
    `;

    if (existingCompany.length === 0) {
      throw new NotFoundException('Empresa não encontrada');
    }

    // Soft delete da empresa
    await this.prisma.company.update({
      where: { id },
      data: { deleted_at: new Date() },
    });
  }

  async getSettings(id: string, userId: string): Promise<CompanySettingsDto> {
    // Verificar se o usuário tem acesso à empresa
    const hasAccess = await this.prisma.$queryRaw<{ count: string }[]>`
      SELECT COUNT(*) as count
      FROM user_company_roles ucr
      WHERE ucr.user_id = ${userId}::uuid
      AND ucr.company_id = ${id}::uuid
      AND ucr.deleted_at IS NULL
    `;

    if (parseInt(hasAccess[0].count, 10) === 0) {
      throw new ForbiddenException('Você não tem acesso a esta empresa');
    }

    const company = await this.prisma.$queryRaw<CompanyRawResult[]>`
      SELECT * FROM companies WHERE id = ${id}::uuid AND deleted_at IS NULL
    `;

    if (company.length === 0) {
      throw new NotFoundException('Empresa não encontrada');
    }

    return {
      id: company[0].id,
      name: company[0].name,
      calendarType: company[0].calendar_type || '',
      logo: nullToUndefined(company[0].logo),
    };
  }

  async checkUserHasAccessToCompany(
    userId: string,
    companyId: string,
  ): Promise<boolean> {
    const hasAccess = await this.prisma.$queryRaw<{ count: string }[]>`
      SELECT COUNT(*) as count
      FROM user_company_roles ucr
      WHERE ucr.user_id = ${userId}::uuid
      AND ucr.company_id = ${companyId}::uuid
      AND ucr.deleted_at IS NULL
    `;

    return parseInt(hasAccess[0].count, 10) > 0;
  }

  async checkUserIsCompanyAdmin(
    userId: string,
    companyId: string,
  ): Promise<boolean> {
    const hasAdminAccess = await this.prisma.$queryRaw<{ count: string }[]>`
      SELECT COUNT(*) as count
      FROM user_company_roles ucr
      JOIN roles r ON ucr.role_id = r.id
      WHERE ucr.user_id = ${userId}::uuid
      AND ucr.company_id = ${companyId}::uuid
      AND r.name = 'Administrador'
      AND ucr.deleted_at IS NULL
      AND r.deleted_at IS NULL
    `;

    return parseInt(hasAdminAccess[0].count, 10) > 0;
  }
}
