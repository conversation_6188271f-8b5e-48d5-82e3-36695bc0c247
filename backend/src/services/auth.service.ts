import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from './prisma.service';
import { UsersService } from './users.service';
import { LoginDto } from '../models/user.model';
import {
  AuthTokens,
  TokenPayload,
  RefreshTokenDto,
} from '../models/auth.model';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import { QueryUtil } from '../utils/query.util';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
    private usersService: UsersService,
    private prisma: PrismaService,
    private queryUtil: QueryUtil,
  ) {}

  /**
   * Valida as credenciais de um usuário
   * @param email Email do usuário
   * @param password Senha do usuário
   * @returns ID do usuário se autenticado com sucesso
   * @throws UnauthorizedException se as credenciais forem inválidas
   */
  async validateUser(email: string, password: string): Promise<string> {
    try {
      // Buscar usuário com senha
      const userResult = await this.prisma.$queryRaw<
        { id: string; password: string }[]
      >`
        SELECT id, password 
        FROM users 
        WHERE email = ${email} 
        AND deleted_at IS NULL
      `;

      if (userResult.length === 0) {
        throw new UnauthorizedException('Credenciais inválidas');
      }

      const user = userResult[0];

      // Verificar se a senha está correta
      const passwordMatch = await bcrypt.compare(password, user.password);
      if (!passwordMatch) {
        throw new UnauthorizedException('Credenciais inválidas');
      }

      return user.id;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      this.logger.error(`Erro na validação do usuário: ${error.message}`, error.stack);
      throw new BadRequestException('Erro ao validar usuário');
    }
  }

  async login(loginDto: LoginDto): Promise<AuthTokens> {
    const user = await this.validateUser(loginDto.email, loginDto.password);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    return this.generateTokens(String(user), loginDto.email);
  }

  async refreshToken(refreshTokenDto: RefreshTokenDto): Promise<AuthTokens> {
    // Buscar o token no banco de dados
    const refreshTokenRecord = await this.prisma.refreshToken.findUnique({
      where: { token: refreshTokenDto.refreshToken },
      include: { user: true },
    });

    if (!refreshTokenRecord) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    // Verificar se o token expirou
    if (new Date() > refreshTokenRecord.expiresAt) {
      // Remover o token expirado
      await this.prisma.refreshToken.delete({
        where: { id: refreshTokenRecord.id },
      });
      throw new UnauthorizedException('Refresh token expired');
    }

    // Remover o token antigo
    await this.prisma.refreshToken.delete({
      where: { id: refreshTokenRecord.id },
    });

    // Gerar novos tokens
    const user = refreshTokenRecord.user;
    return this.generateTokens(String(user.id), user.email);
  }

  async logout(refreshToken: string): Promise<void> {
    await this.prisma.refreshToken.deleteMany({
      where: { token: refreshToken },
    });
  }

  private async generateTokens(
    userId: string,
    email: string,
  ): Promise<AuthTokens> {
    this.logger.debug(`Iniciando geração de tokens para usuário: ${userId}`);

    // Buscar a empresa padrão do usuário
    const userCompanyRole = await this.usersService.getUserDefaultCompany(userId);

    this.logger.debug(`Resultado getUserDefaultCompany: ${JSON.stringify(userCompanyRole)}`);

    // Criar o payload do token com o companyId (se disponível)
    // Para usuários sem empresa, companyId será undefined
    const payload: TokenPayload = {
      sub: userId,
      email,
      companyId: userCompanyRole?.companyId
    };

    this.logger.debug(`Payload do token JWT: ${JSON.stringify(payload)}`);
    this.logger.debug(`Gerando token JWT para usuário ${userId} com companyId: ${userCompanyRole?.companyId || 'sem empresa'}`);

    const accessToken = this.jwtService.sign(payload, {
      secret: this.configService.get('JWT_SECRET'),
      expiresIn: this.configService.get('JWT_EXPIRATION'),
    });

    const refreshToken = uuidv4();
    const refreshTokenExpiresIn = this.configService.get(
      'JWT_REFRESH_EXPIRATION',
    );

    // Calcular a data de expiração do refresh token
    const expiresAt = new Date();
    // Converter '7d' para milissegundos (exemplo: 7 dias * 24 horas * 60 minutos * 60 segundos * 1000 ms)
    expiresAt.setTime(
      expiresAt.getTime() + this.parseDuration(refreshTokenExpiresIn),
    );

    // Salvar o refresh token no banco de dados
    await this.prisma.refreshToken.create({
      data: {
        token: refreshToken,
        user: { connect: { id: userId } },
        expiresAt,
      },
    });

    return {
      accessToken,
      refreshToken,
    };
  }

  // Helper para converter durações como '7d', '15m', etc. para milissegundos
  private parseDuration(duration: string): number {
    const unit = duration.slice(-1);
    const value = parseInt(duration.slice(0, -1), 10);

    switch (unit) {
      case 's':
        return value * 1000;
      case 'm':
        return value * 60 * 1000;
      case 'h':
        return value * 60 * 60 * 1000;
      case 'd':
        return value * 24 * 60 * 60 * 1000;
      default:
        return 0;
    }
  }

  /**
   * Altera a senha de um usuário
   * @param userId ID do usuário
   * @param currentPassword Senha atual
   * @param newPassword Nova senha
   * @returns true se a senha foi alterada com sucesso
   * @throws UnauthorizedException se a senha atual for inválida
   */
  async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string,
  ): Promise<boolean> {
    try {
      // Buscar senha atual do usuário
      const userResult = await this.prisma.$queryRaw<
        { password: string }[]
      >`
        SELECT password 
        FROM users 
        WHERE id = ${userId}::uuid 
        AND deleted_at IS NULL
      `;

      if (userResult.length === 0) {
        throw new UnauthorizedException('Usuário não encontrado');
      }

      // Verificar se a senha atual está correta
      const passwordMatch = await bcrypt.compare(
        currentPassword,
        userResult[0].password,
      );
      if (!passwordMatch) {
        throw new UnauthorizedException('Senha atual incorreta');
      }

      // Criptografar nova senha
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Atualizar senha
      await this.prisma.$executeRaw`
        UPDATE users 
        SET password = ${hashedPassword}, updated_at = NOW() 
        WHERE id = ${userId}::uuid
      `;

      return true;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      this.logger.error(`Erro na alteração de senha: ${error.message}`, error.stack);
      throw new BadRequestException('Erro ao alterar senha');
    }
  }
}
