import { ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments } from 'class-validator';

/**
 * Validador flexível de URL que aceita diferentes formatos
 * Aceita URLs com ou sem protocolo, com ou sem www
 */
@ValidatorConstraint({ name: 'flexibleUrl', async: false })
export class FlexibleUrlValidator implements ValidatorConstraintInterface {
  validate(url: string, args: ValidationArguments) {
    if (!url || url.trim() === '') return true; // Campo opcional
    
    try {
      // Remove espaços em branco
      const cleanUrl = url.trim();
      
      // Verifica se contém espaços (inválido)
      if (cleanUrl.includes(' ')) return false;
      
      // Verifica se é apenas texto sem estrutura de domínio
      if (!cleanUrl.includes('.')) return false;
      
      // Tenta criar URL com protocolo se não tiver
      let testUrl = cleanUrl;
      if (!cleanUrl.match(/^[a-zA-Z][a-zA-Z0-9+.-]*:/)) {
        testUrl = 'https://' + cleanUrl;
      }
      
      const urlObj = new URL(testUrl);
      
      // Verifica se tem domínio válido
      const domain = urlObj.hostname;
      if (!domain || domain.length === 0) return false;
      
      // Verifica se tem pelo menos um ponto (domínio.extensão)
      if (!domain.includes('.')) return false;
      
      // Verifica se o domínio não é apenas pontos
      if (domain.replace(/\./g, '').length === 0) return false;
      
      // Verifica se não termina ou começa com ponto
      if (domain.startsWith('.') || domain.endsWith('.')) return false;
      
      // Verifica se não tem pontos consecutivos
      if (domain.includes('..')) return false;
      
      return true;
    } catch {
      return false;
    }
  }

  defaultMessage(args: ValidationArguments) {
    return 'URL inválida. Use formato como: www.exemplo.com ou https://www.exemplo.com';
  }
}

/**
 * Função utilitária para validar URL de forma flexível
 * @param url - URL para validar
 * @returns boolean indicando se a URL é válida
 */
export function isValidFlexibleUrl(url: string): boolean {
  const validator = new FlexibleUrlValidator();
  return validator.validate(url, {} as ValidationArguments);
}
