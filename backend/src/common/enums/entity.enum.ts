/**
 * Enums for Brazilian Entity Management
 * Defines all entity-related enumerations for the FluxoMax system
 */

/**
 * Entity Type - Defines the commercial relationship
 */
export enum EntityType {
  CUSTOMER = 'customer',           // Cliente apenas
  SUPPLIER = 'supplier',           // Fornecedor apenas
  BOTH = 'both'                   // Cliente e fornecedor
}

/**
 * Person Type - Defines if entity is individual or company
 */
export enum PersonType {
  INDIVIDUAL = 'individual',       // Pessoa Física
  COMPANY = 'company'             // Pessoa Jurídica
}

/**
 * Entity Status - Defines the current status of the entity
 */
export enum EntityStatus {
  ACTIVE = 'active',              // Ativo
  INACTIVE = 'inactive',          // Inativo
  SUSPENDED = 'suspended',        // Suspenso
  BLOCKED = 'blocked'             // Bloqueado
}

/**
 * Tax Regime - Brazilian tax regimes
 */
export enum TaxRegime {
  SIMPLE_NATIONAL = 'simple_national',     // Simples Nacional
  PRESUMED_PROFIT = 'presumed_profit',     // Lucro Presumido
  REAL_PROFIT = 'real_profit',             // Lucro Real
  INDIVIDUAL = 'individual'                // Pessoa Física
}

/**
 * Brazilian States - All valid UF codes
 */
export enum BrazilianState {
  AC = 'AC', // Acre
  AL = 'AL', // Alagoas
  AP = 'AP', // Amapá
  AM = 'AM', // Amazonas
  BA = 'BA', // Bahia
  CE = 'CE', // Ceará
  DF = 'DF', // Distrito Federal
  ES = 'ES', // Espírito Santo
  GO = 'GO', // Goiás
  MA = 'MA', // Maranhão
  MT = 'MT', // Mato Grosso
  MS = 'MS', // Mato Grosso do Sul
  MG = 'MG', // Minas Gerais
  PA = 'PA', // Pará
  PB = 'PB', // Paraíba
  PR = 'PR', // Paraná
  PE = 'PE', // Pernambuco
  PI = 'PI', // Piauí
  RJ = 'RJ', // Rio de Janeiro
  RN = 'RN', // Rio Grande do Norte
  RS = 'RS', // Rio Grande do Sul
  RO = 'RO', // Rondônia
  RR = 'RR', // Roraima
  SC = 'SC', // Santa Catarina
  SP = 'SP', // São Paulo
  SE = 'SE', // Sergipe
  TO = 'TO'  // Tocantins
}

/**
 * Helper functions for enum validation
 */
export class EntityEnumHelper {
  /**
   * Get all valid entity types
   */
  static getEntityTypes(): string[] {
    return Object.values(EntityType);
  }

  /**
   * Get all valid person types
   */
  static getPersonTypes(): string[] {
    return Object.values(PersonType);
  }

  /**
   * Get all valid entity statuses
   */
  static getEntityStatuses(): string[] {
    return Object.values(EntityStatus);
  }

  /**
   * Get all valid tax regimes
   */
  static getTaxRegimes(): string[] {
    return Object.values(TaxRegime);
  }

  /**
   * Get all valid Brazilian states
   */
  static getBrazilianStates(): string[] {
    return Object.values(BrazilianState);
  }

  /**
   * Check if entity type is valid
   */
  static isValidEntityType(type: string): boolean {
    return Object.values(EntityType).includes(type as EntityType);
  }

  /**
   * Check if person type is valid
   */
  static isValidPersonType(type: string): boolean {
    return Object.values(PersonType).includes(type as PersonType);
  }

  /**
   * Check if entity status is valid
   */
  static isValidEntityStatus(status: string): boolean {
    return Object.values(EntityStatus).includes(status as EntityStatus);
  }

  /**
   * Check if tax regime is valid
   */
  static isValidTaxRegime(regime: string): boolean {
    return Object.values(TaxRegime).includes(regime as TaxRegime);
  }

  /**
   * Check if Brazilian state is valid
   */
  static isValidBrazilianState(state: string): boolean {
    return Object.values(BrazilianState).includes(state.toUpperCase() as BrazilianState);
  }

  /**
   * Get default tax regime for person type
   */
  static getDefaultTaxRegime(personType: PersonType): TaxRegime {
    return personType === PersonType.INDIVIDUAL ? TaxRegime.INDIVIDUAL : TaxRegime.SIMPLE_NATIONAL;
  }

  /**
   * Check if entity can be both customer and supplier
   */
  static canBeBoth(entityType: EntityType): boolean {
    return entityType === EntityType.BOTH;
  }

  /**
   * Get entity type display name
   */
  static getEntityTypeDisplayName(entityType: EntityType): string {
    const displayNames = {
      [EntityType.CUSTOMER]: 'Cliente',
      [EntityType.SUPPLIER]: 'Fornecedor',
      [EntityType.BOTH]: 'Cliente e Fornecedor'
    };
    return displayNames[entityType];
  }

  /**
   * Get person type display name
   */
  static getPersonTypeDisplayName(personType: PersonType): string {
    const displayNames = {
      [PersonType.INDIVIDUAL]: 'Pessoa Física',
      [PersonType.COMPANY]: 'Pessoa Jurídica'
    };
    return displayNames[personType];
  }

  /**
   * Get entity status display name
   */
  static getEntityStatusDisplayName(status: EntityStatus): string {
    const displayNames = {
      [EntityStatus.ACTIVE]: 'Ativo',
      [EntityStatus.INACTIVE]: 'Inativo',
      [EntityStatus.SUSPENDED]: 'Suspenso',
      [EntityStatus.BLOCKED]: 'Bloqueado'
    };
    return displayNames[status];
  }

  /**
   * Get tax regime display name
   */
  static getTaxRegimeDisplayName(regime: TaxRegime): string {
    const displayNames = {
      [TaxRegime.SIMPLE_NATIONAL]: 'Simples Nacional',
      [TaxRegime.PRESUMED_PROFIT]: 'Lucro Presumido',
      [TaxRegime.REAL_PROFIT]: 'Lucro Real',
      [TaxRegime.INDIVIDUAL]: 'Pessoa Física'
    };
    return displayNames[regime];
  }
}
