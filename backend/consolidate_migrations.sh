#!/bin/bash
set -e

echo "=== Consolidando migrações do Prisma ==="

# Verificar se o diretório de migrações existe
if [ ! -d "prisma/migrations" ]; then
  echo "Diretório de migrações não encontrado."
  exit 1
fi

# Criar backup das migrações atuais
echo "Criando backup das migrações atuais..."
mkdir -p prisma/migrations_backup
cp -r prisma/migrations/* prisma/migrations_backup/ 2>/dev/null || true

# Limpar diretório de migrações
echo "Limpando diretório de migrações..."
rm -rf prisma/migrations/*

# Gerar uma nova migração consolidada
echo "Gerando migração consolidada..."

# Criar timestamp para o nome da migração
TIMESTAMP=$(date +%Y%m%d%H%M%S)
MIGRATION_NAME="${TIMESTAMP}_consolidated_schema"

# Criar diretório para a nova migração
mkdir -p "prisma/migrations/${MIGRATION_NAME}"

# Gerar SQL da migração
echo "Gerando SQL da migração..."
npx prisma migrate diff \
  --from-empty \
  --to-schema-datamodel prisma/schema.prisma \
  --script > "prisma/migrations/${MIGRATION_NAME}/migration.sql"

# Adicionar correções de valores padrão ao final da migração consolidada
echo "Adicionando correções de valores padrão..."
cat >> "prisma/migrations/${MIGRATION_NAME}/migration.sql" << 'DEFAULTS_EOF'

-- Add missing DEFAULT values for UUID and timestamp fields
-- This ensures that all required fields have proper default values

-- Fix roles table
ALTER TABLE roles ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE roles ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

-- Fix user_company_roles table
ALTER TABLE user_company_roles ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE user_company_roles ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

-- Fix other critical tables
ALTER TABLE users ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE users ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE companies ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE companies ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE permissions ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE permissions ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE system_permissions ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE system_permissions ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;
DEFAULTS_EOF

echo "Migração consolidada criada com sucesso em prisma/migrations/${MIGRATION_NAME}/migration.sql"
echo "=== Consolidação de migrações concluída ==="
