// Script de teste para verificar a criação de endereços de entidades
// Execute com: node test-address-creation.js

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

// Configuração de teste
const testConfig = {
  email: '<EMAIL>',
  password: 'Admin123',
  entityId: null, // Será preenchido após login
  token: null
};

async function login() {
  try {
    console.log('🔐 Fazendo login...');
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: testConfig.email,
      password: testConfig.password
    });
    
    testConfig.token = response.data.access_token;
    console.log('✅ Login realizado com sucesso');
    return response.data;
  } catch (error) {
    console.error('❌ Erro no login:', error.response?.data || error.message);
    throw error;
  }
}

async function getFirstEntity() {
  try {
    console.log('📋 Buscando primeira entidade...');
    const response = await axios.get(`${API_BASE_URL}/entities?limit=1`, {
      headers: { Authorization: `Bearer ${testConfig.token}` }
    });
    
    if (response.data.items && response.data.items.length > 0) {
      testConfig.entityId = response.data.items[0].id;
      console.log(`✅ Entidade encontrada: ${response.data.items[0].name} (${testConfig.entityId})`);
      return response.data.items[0];
    } else {
      throw new Error('Nenhuma entidade encontrada');
    }
  } catch (error) {
    console.error('❌ Erro ao buscar entidades:', error.response?.data || error.message);
    throw error;
  }
}

async function getAddressTypes() {
  try {
    console.log('🏷️ Buscando tipos de endereço...');
    const response = await axios.get(`${API_BASE_URL}/address-types?limit=100`, {
      headers: { Authorization: `Bearer ${testConfig.token}` }
    });
    
    console.log('✅ Tipos de endereço encontrados:', response.data.data?.map(t => `${t.name} (${t.id})`));
    return response.data.data || [];
  } catch (error) {
    console.error('❌ Erro ao buscar tipos de endereço:', error.response?.data || error.message);
    return [];
  }
}

async function createTestAddress() {
  try {
    console.log('🏠 Criando endereço de teste...');
    
    const addressData = {
      street: 'Rua de Teste',
      number: '123',
      complement: 'Sala 456',
      district: 'Bairro Teste',
      city: 'São Paulo',
      state: 'SP',
      zipCode: '01234-567',
      isDefault: true
    };
    
    console.log('📤 Dados do endereço:', addressData);
    
    const response = await axios.post(
      `${API_BASE_URL}/entities/${testConfig.entityId}/addresses`,
      addressData,
      {
        headers: { Authorization: `Bearer ${testConfig.token}` }
      }
    );
    
    console.log('✅ Endereço criado com sucesso:', {
      id: response.data.id,
      entityId: response.data.entityId,
      addressTypeId: response.data.addressTypeId,
      street: response.data.street,
      isDefault: response.data.isDefault
    });
    
    return response.data;
  } catch (error) {
    console.error('❌ Erro ao criar endereço:', error.response?.data || error.message);
    throw error;
  }
}

async function getEntityAddresses() {
  try {
    console.log('📍 Buscando endereços da entidade...');
    
    const response = await axios.get(
      `${API_BASE_URL}/entities/${testConfig.entityId}/addresses`,
      {
        headers: { Authorization: `Bearer ${testConfig.token}` }
      }
    );
    
    console.log(`✅ Encontrados ${response.data.length} endereços:`);
    response.data.forEach((addr, index) => {
      console.log(`  ${index + 1}. ${addr.street}, ${addr.number} - ${addr.district}, ${addr.city}/${addr.state}`);
      console.log(`     ID: ${addr.id}, EntityID: ${addr.entityId}, AddressTypeID: ${addr.addressTypeId}`);
      console.log(`     Padrão: ${addr.isDefault}, Criado: ${addr.createdAt}`);
    });
    
    return response.data;
  } catch (error) {
    console.error('❌ Erro ao buscar endereços:', error.response?.data || error.message);
    throw error;
  }
}

async function runTest() {
  try {
    console.log('🚀 Iniciando teste de criação de endereços...\n');
    
    // 1. Login
    await login();
    
    // 2. Buscar primeira entidade
    await getFirstEntity();
    
    // 3. Buscar tipos de endereço
    await getAddressTypes();
    
    // 4. Buscar endereços existentes
    console.log('\n--- ANTES DA CRIAÇÃO ---');
    const addressesBefore = await getEntityAddresses();
    
    // 5. Criar novo endereço
    console.log('\n--- CRIANDO ENDEREÇO ---');
    const newAddress = await createTestAddress();
    
    // 6. Buscar endereços novamente
    console.log('\n--- DEPOIS DA CRIAÇÃO ---');
    const addressesAfter = await getEntityAddresses();
    
    // 7. Verificar se o endereço foi persistido corretamente
    console.log('\n--- VERIFICAÇÃO ---');
    const foundAddress = addressesAfter.find(addr => addr.id === newAddress.id);
    
    if (foundAddress) {
      console.log('✅ Endereço encontrado na consulta posterior!');
      console.log('✅ EntityID persistido:', foundAddress.entityId ? '✓' : '✗');
      console.log('✅ AddressTypeID persistido:', foundAddress.addressTypeId ? '✓' : '✗');
      console.log('✅ Dados completos:', foundAddress.street && foundAddress.city ? '✓' : '✗');
    } else {
      console.log('❌ Endereço NÃO encontrado na consulta posterior!');
    }
    
    console.log('\n🎉 Teste concluído!');
    
  } catch (error) {
    console.error('\n💥 Teste falhou:', error.message);
    process.exit(1);
  }
}

// Executar o teste
runTest();
