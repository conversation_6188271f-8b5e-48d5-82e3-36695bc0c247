# Correção de Campos Obrigatórios - Sistema de Entidades

## 📋 Resumo das Correções

Este documento detalha as correções implementadas para resolver o problema do botão "Salvar" desabilitado nos formulários de edição de entidades (clientes/fornecedores).

## 🔍 Problema Identificado

### Sintomas
- Bot<PERSON> "Salvar" permanecia desabilitado durante a edição de entidades
- Formulários não permitiam salvar mesmo com dados válidos preenchidos
- Validação excessivamente restritiva impedindo operações normais

### Causa Raiz
1. **Schema de validação muito restritivo**: Campos como `email`, `stateRegistration` eram obrigatórios no frontend
2. **Inconsistência backend vs frontend**: Backend permitia campos opcionais que o frontend exigia
3. **Validação dupla conflitante**: `BrazilianEntityHelper.validateEntityData` forçava campos como obrigatórios independente do schema Zod

## ✅ Soluções Implementadas

### 1. **Correção Imediata - Schema de Validação**

#### Arquivo: `frontend/src/schemas/entitySchemas.ts`

**Antes:**
```typescript
// Campos forçados como obrigatórios
email: z.string().email({ message: "Email inválido" }).optional().or(z.literal("")),
stateRegistration: z.string().min(1, { message: "Inscrição Estadual é obrigatória" }),
```

**Depois:**
```typescript
// Configuração flexível baseada em RequiredFieldsConfig
email: requiredFields.email 
  ? z.string().min(1, { message: "Email é obrigatório" }).email({ message: "Email inválido" })
  : z.string().email({ message: "Email inválido" }).optional().or(z.literal("")),

stateRegistration: requiredFields.stateRegistration
  ? z.string().min(1, { message: "Inscrição Estadual é obrigatória" })
  : z.string().optional(),
```

#### Novos Schemas Criados:
- `createMinimalEntitySchema()`: Apenas campos essenciais obrigatórios
- `createCompleteEntitySchema()`: Configuração mais rigorosa
- `createEntitySchemaFromConfig()`: Baseado em configuração dinâmica

### 2. **Correção da Validação Helper**

#### Arquivo: `frontend/src/constants/brazilianEntities.ts`

**Antes:**
```typescript
static getRequiredFields(personType: PersonType): string[] {
  const baseFields = ['name', 'entityType', 'personType', 'email']; // ❌ Email forçado
  
  if (personType === 'individual') {
    return [...baseFields, 'cpf'];
  } else {
    return [...baseFields, 'cnpj', 'stateRegistration']; // ❌ IE forçada
  }
}
```

**Depois:**
```typescript
static getRequiredFields(personType: PersonType): string[] {
  const baseFields = ['name', 'entityType', 'personType']; // ✅ Apenas essenciais
  
  if (personType === 'individual') {
    return [...baseFields, 'cpf'];
  } else {
    return [...baseFields, 'cnpj']; // ✅ IE opcional
  }
}
```

#### Novos Métodos:
- `validateEntityDataMinimal()`: Validação flexível
- `validateEntityDataComplete()`: Validação rigorosa
- `getRequiredFieldsWithConfig()`: Configuração dinâmica

### 3. **Atualização dos Formulários**

#### Arquivos Atualizados:
- `frontend/src/pages/customers/CustomerFormPage.tsx`
- `frontend/src/pages/suppliers/SupplierFormPage.tsx`
- `frontend/src/components/entities/BrazilianEntityModal.tsx`

**Mudanças:**
```typescript
// ❌ Antes - validação restritiva
const validation = BrazilianEntityHelper.validateEntityData(data);

// ✅ Depois - validação flexível
const validation = BrazilianEntityHelper.validateEntityDataMinimal(data);

// ❌ Antes - schema restritivo
resolver: zodResolver(createEntitySchema(selectedPersonType))

// ✅ Depois - schema mínimo
resolver: zodResolver(createMinimalEntitySchema(selectedPersonType))
```

## 🚀 Sistema de Configuração Dinâmica (Funcionalidade Futura)

### Arquitetura Implementada

#### 1. **Tipos e Interfaces**
- `frontend/src/types/entityConfig.ts`: Definições de tipos
- `EntityValidationConfig`: Configuração completa
- `EntityFieldConfig`: Configuração por campo
- `RequiredFieldsConfig`: Interface de campos obrigatórios

#### 2. **Serviços API**
- `frontend/src/services/api/entityConfigService.ts`: Comunicação com backend
- CRUD completo para configurações
- Validação de configurações
- Estatísticas de uso

#### 3. **Hooks React Query**
- `frontend/src/hooks/api/useEntityConfig.ts`: Hooks para gerenciar estado
- Cache inteligente com React Query
- Invalidação automática de cache
- Tratamento de erros

#### 4. **Interface Administrativa**
- `frontend/src/pages/admin/EntityConfigPage.tsx`: Página de gerenciamento
- Listagem de configurações
- Ativação/desativação
- Duplicação e exclusão
- Estatísticas de uso

### Campos Configuráveis

#### Categorias Disponíveis:
1. **Dados Básicos**: Nome, Nome Fantasia
2. **Contato**: Email, Telefone, Celular, Website, Pessoa de Contato
3. **Documentos**: CPF, RG, CNPJ, IE, IM
4. **Comercial**: Limite de Crédito, Prazo de Pagamento, Desconto
5. **Fiscal**: Regime Tributário, ICMS, Simples Nacional, NFe
6. **Bancário**: Dados bancários e PIX

## 📊 Configuração Atual vs Nova

### Campos Obrigatórios - ANTES
```
✅ Nome/Razão Social
✅ Email (forçado)
✅ Celular (forçado)
✅ CNPJ (PJ)
✅ CPF (PF)
✅ Inscrição Estadual (forçado para PJ)
```

### Campos Obrigatórios - DEPOIS (Configuração Mínima)
```
✅ Nome/Razão Social
❌ Email (opcional)
❌ Celular (opcional)
✅ CNPJ (PJ)
✅ CPF (PF)
❌ Inscrição Estadual (opcional)
```

### Campos Obrigatórios - FUTURO (Configurável)
```
✅ Nome/Razão Social (sempre)
🔧 Email (configurável)
🔧 Celular (configurável)
✅ CNPJ (PJ - sempre)
✅ CPF (PF - sempre)
🔧 Inscrição Estadual (configurável)
🔧 + 20 outros campos configuráveis
```

## 🔧 Como Usar

### Para Desenvolvedores

#### 1. **Usar Schema Mínimo (Recomendado)**
```typescript
import { createMinimalEntitySchema } from '@/schemas/entitySchemas';

const form = useForm({
  resolver: zodResolver(createMinimalEntitySchema(personType))
});
```

#### 2. **Usar Schema Completo**
```typescript
import { createCompleteEntitySchema } from '@/schemas/entitySchemas';

const form = useForm({
  resolver: zodResolver(createCompleteEntitySchema(personType))
});
```

#### 3. **Usar Configuração Dinâmica (Futuro)**
```typescript
import { useActiveEntityConfig } from '@/hooks/api/useEntityConfig';
import { createEntitySchemaFromConfig } from '@/schemas/entitySchemas';

const { data: config } = useActiveEntityConfig();
const schema = createEntitySchemaFromConfig(personType, config);
```

### Para Administradores

#### 1. **Acessar Configurações**
- Menu: Administração → Configurações de Campos
- URL: `/admin/entity-config`

#### 2. **Criar Nova Configuração**
- Clique em "Nova Configuração"
- Defina nome e descrição
- Selecione campos obrigatórios por categoria
- Ative a configuração

#### 3. **Gerenciar Configurações**
- Visualizar configurações existentes
- Ativar/desativar configurações
- Duplicar configurações
- Ver estatísticas de uso

## 🎯 Benefícios

### Imediatos
- ✅ Botão "Salvar" funciona corretamente
- ✅ Formulários mais flexíveis
- ✅ Consistência backend/frontend
- ✅ Melhor experiência do usuário

### Futuros
- 🔧 Configuração dinâmica por empresa
- 🔧 Diferentes perfis de validação
- 🔧 Auditoria de mudanças
- 🔧 Estatísticas de uso
- 🔧 Validação contextual

## 🚨 Pontos de Atenção

### Para Desenvolvedores
1. **Sempre usar schemas tipados**: Evitar `any` nos formulários
2. **Testar com diferentes configurações**: Mínima e completa
3. **Validar no backend**: Frontend é apenas UX, backend é segurança

### Para Administradores
1. **Testar configurações**: Sempre testar antes de ativar
2. **Backup de configurações**: Duplicar antes de modificar
3. **Comunicar mudanças**: Avisar usuários sobre novos campos obrigatórios

## 📝 Próximos Passos

### Backend (Necessário para configuração dinâmica)
1. Criar tabelas de configuração
2. Implementar endpoints CRUD
3. Validação de configurações
4. Sistema de auditoria

### Frontend (Melhorias)
1. Interface de criação/edição de configurações
2. Preview de formulários
3. Importação/exportação de configurações
4. Validação em tempo real

### Testes
1. Testes unitários para schemas
2. Testes de integração para formulários
3. Testes E2E para fluxos completos
4. Testes de performance para configurações dinâmicas
