#!/bin/bash

# Script de teste para verificar o sistema de categorias hierárquicas
API_BASE="http://localhost:3000/api"

echo "👤 Usuário já existe e está associado à empresa..."

echo "🔐 Fazendo login..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin123"}')

TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
  echo "❌ Erro no login. Resposta:"
  echo $LOGIN_RESPONSE
  exit 1
fi

echo "✅ Login realizado com sucesso"

echo ""
echo "📋 Estado inicial das categorias:"
curl -s -X GET "$API_BASE/categories/tree" \
  -H "Authorization: Bearer $TOKEN" | jq '.'

echo ""
echo "📝 Criando categoria pai..."
PARENT_RESPONSE=$(curl -s -X POST "$API_BASE/categories" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"name":"Categoria Pai Teste","transactionType":"payable"}')

PARENT_ID=$(echo $PARENT_RESPONSE | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

if [ -z "$PARENT_ID" ]; then
  echo "❌ Erro ao criar categoria pai. Resposta:"
  echo $PARENT_RESPONSE
  exit 1
fi

echo "✅ Categoria pai criada com ID: $PARENT_ID"

echo ""
echo "📝 Criando categoria filha..."
CHILD_RESPONSE=$(curl -s -X POST "$API_BASE/categories" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"name\":\"Categoria Filha Teste\",\"transactionType\":\"payable\",\"parentCategoryId\":\"$PARENT_ID\"}")

CHILD_ID=$(echo $CHILD_RESPONSE | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

if [ -z "$CHILD_ID" ]; then
  echo "❌ Erro ao criar categoria filha. Resposta:"
  echo $CHILD_RESPONSE
  exit 1
fi

echo "✅ Categoria filha criada com ID: $CHILD_ID"

echo ""
echo "📋 Estado após criação das categorias:"
TREE_RESPONSE=$(curl -s -X GET "$API_BASE/categories/tree" \
  -H "Authorization: Bearer $TOKEN")

echo $TREE_RESPONSE | jq '.'

# Verificar se a categoria pai existe na árvore
PARENT_EXISTS=$(echo $TREE_RESPONSE | jq --arg id "$PARENT_ID" '.[] | select(.id == $id)')

if [ -n "$PARENT_EXISTS" ]; then
  echo ""
  echo "✅ Categoria pai encontrada na árvore"
  
  # Verificar se a categoria filha está aninhada na categoria pai
  CHILD_EXISTS=$(echo $TREE_RESPONSE | jq --arg parent_id "$PARENT_ID" --arg child_id "$CHILD_ID" '.[] | select(.id == $parent_id) | .children[]? | select(.id == $child_id)')
  
  if [ -n "$CHILD_EXISTS" ]; then
    echo "✅ Categoria filha encontrada corretamente aninhada na categoria pai"
    echo "🎉 TESTE PASSOU: Hierarquia de categorias funcionando corretamente!"
  else
    echo "❌ TESTE FALHOU: Categoria filha não encontrada na categoria pai"
    echo "Categoria pai:"
    echo $PARENT_EXISTS | jq '.'
  fi
else
  echo "❌ TESTE FALHOU: Categoria pai não encontrada na árvore"
fi

echo ""
echo "🧹 Limpando categorias de teste..."

echo "🗑️ Deletando categoria filha..."
curl -s -X DELETE "$API_BASE/categories/$CHILD_ID" \
  -H "Authorization: Bearer $TOKEN"

echo "🗑️ Deletando categoria pai..."
curl -s -X DELETE "$API_BASE/categories/$PARENT_ID" \
  -H "Authorization: Bearer $TOKEN"

echo ""
echo "📋 Estado final das categorias:"
curl -s -X GET "$API_BASE/categories/tree" \
  -H "Authorization: Bearer $TOKEN" | jq '.'

echo ""
echo "✅ Teste concluído!"
