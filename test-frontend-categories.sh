#!/bin/bash

# Script para testar o frontend das categorias
API_BASE="http://localhost:3000/api"

echo "🧪 Testando frontend de categorias..."

# Fazer login
echo "🔐 Fazendo login..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin123"}')

TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
  echo "❌ Erro no login"
  exit 1
fi

echo "✅ Login realizado com sucesso"

# Criar categoria pai para teste do frontend
echo ""
echo "📝 Criando categoria pai para teste do frontend..."
PARENT_RESPONSE=$(curl -s -X POST "$API_BASE/categories" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"name":"Frontend Categoria Pai","transactionType":"payable"}')

PARENT_ID=$(echo $PARENT_RESPONSE | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

if [ -z "$PARENT_ID" ]; then
  echo "❌ Erro ao criar categoria pai"
  exit 1
fi

echo "✅ Categoria pai criada: $PARENT_ID"

# Criar categoria filha para teste do frontend
echo ""
echo "📝 Criando categoria filha para teste do frontend..."
CHILD_RESPONSE=$(curl -s -X POST "$API_BASE/categories" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"name\":\"Frontend Categoria Filha\",\"transactionType\":\"payable\",\"parentCategoryId\":\"$PARENT_ID\"}")

CHILD_ID=$(echo $CHILD_RESPONSE | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

if [ -z "$CHILD_ID" ]; then
  echo "❌ Erro ao criar categoria filha"
  exit 1
fi

echo "✅ Categoria filha criada: $CHILD_ID"

# Verificar se as categorias aparecem na árvore
echo ""
echo "📋 Verificando árvore de categorias..."
TREE_RESPONSE=$(curl -s -X GET "$API_BASE/categories/tree" \
  -H "Authorization: Bearer $TOKEN")

# Verificar se a categoria pai existe
PARENT_EXISTS=$(echo $TREE_RESPONSE | jq --arg id "$PARENT_ID" '.[] | select(.id == $id)')

if [ -n "$PARENT_EXISTS" ]; then
  echo "✅ Categoria pai encontrada na árvore"
  
  # Verificar se a categoria filha está aninhada
  CHILD_EXISTS=$(echo $TREE_RESPONSE | jq --arg parent_id "$PARENT_ID" --arg child_id "$CHILD_ID" '.[] | select(.id == $parent_id) | .children[]? | select(.id == $child_id)')
  
  if [ -n "$CHILD_EXISTS" ]; then
    echo "✅ Categoria filha encontrada aninhada na categoria pai"
    echo "🎉 TESTE DO FRONTEND PASSOU!"
  else
    echo "❌ Categoria filha não encontrada na categoria pai"
  fi
else
  echo "❌ Categoria pai não encontrada na árvore"
fi

echo ""
echo "📊 Estrutura atual das categorias:"
echo $TREE_RESPONSE | jq '.'

echo ""
echo "🌐 Acesse http://localhost:3001/categories para testar a interface"
echo "📧 Login: <EMAIL>"
echo "🔑 Senha: Admin123"

echo ""
echo "🧹 Para limpar as categorias de teste, execute:"
echo "curl -X DELETE \"$API_BASE/categories/$CHILD_ID\" -H \"Authorization: Bearer $TOKEN\""
echo "curl -X DELETE \"$API_BASE/categories/$PARENT_ID\" -H \"Authorization: Bearer $TOKEN\""
