
import { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Plus, Search, Mail, Phone, Edit, Trash2 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { useToast } from "@/hooks/use-toast";
import { useSuppliers, useDeleteEntity } from "@/hooks/api/useEntities";
import { Entity } from "@/types/api";
import { toast } from 'sonner';

const Suppliers = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast: uiToast } = useToast();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState("");

  // Usar hooks de API reais
  const { data: suppliersData, isLoading: loading, error } = useSuppliers(1, 50, searchQuery);
  const deleteEntityMutation = useDeleteEntity();

  const suppliers = suppliersData?.data || [];
  const filteredSuppliers = suppliers.filter(supplier =>
    supplier.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (supplier.email && supplier.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (supplier.phone && supplier.phone.includes(searchQuery))
  );

  const handleAddSupplier = () => {
    navigate('/suppliers/novo');
  };

  const handleEditSupplier = (supplier: any) => {
    navigate(`/suppliers/editar/${supplier.id}`);
  };

  const handleViewSupplier = (supplier: any) => {
    navigate(`/suppliers/visualizar/${supplier.id}`);
  };



  const handleDeleteSupplier = async (supplierId: string) => {
    try {
      await deleteEntityMutation.mutateAsync(supplierId);
      toast.success("Fornecedor removido com sucesso!");
    } catch (error: any) {
      console.error('Erro ao remover fornecedor:', error);
      toast.error(error.response?.data?.message || 'Erro ao remover fornecedor. Tente novamente.');
    }
  };

  return (
    <Layout location={location}>
      <div className="container p-4 mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Fornecedores</h1>
          <Button onClick={handleAddSupplier} className="gap-2">
            <Plus size={18} />
            Novo Fornecedor
          </Button>
        </div>

        <Card className="mb-6">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle>Todos os Fornecedores</CardTitle>
              <div className="relative w-72">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar fornecedores..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Contato</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Telefone</TableHead>
                  <TableHead>Data de Cadastro</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                      Carregando fornecedores...
                    </TableCell>
                  </TableRow>
                ) : error ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-6 text-destructive">
                      Erro ao carregar fornecedores: {error.message || 'Erro desconhecido'}
                    </TableCell>
                  </TableRow>
                ) : filteredSuppliers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                      {suppliers.length === 0
                        ? "Nenhum fornecedor cadastrado. Comece criando seu primeiro fornecedor."
                        : "Nenhum fornecedor encontrado com os filtros aplicados."
                      }
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredSuppliers.map((supplier) => (
                    <TableRow key={supplier.id}>
                      <TableCell className="font-medium">
                        <button
                          onClick={() => handleViewSupplier(supplier)}
                          className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer text-left"
                        >
                          {supplier.name}
                        </button>
                      </TableCell>
                      <TableCell>{supplier.contact || '-'}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Mail size={16} className="text-muted-foreground" />
                          {supplier.email || '-'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Phone size={16} className="text-muted-foreground" />
                          {supplier.phone || '-'}
                        </div>
                      </TableCell>
                      <TableCell>{new Date(supplier.createdAt).toLocaleDateString('pt-BR')}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            onClick={() => handleEditSupplier(supplier)}
                          >
                            <Edit size={18} className="text-muted-foreground" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="icon"
                            onClick={() => handleDeleteSupplier(supplier.id)}
                          >
                            <Trash2 size={18} className="text-destructive" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>


    </Layout>
  );
};

export default Suppliers;
