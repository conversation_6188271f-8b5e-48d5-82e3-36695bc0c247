import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Grid3X3 } from "lucide-react";
import { Layout } from "@/components/layout/Layout";
import CategoriesTable from "@/components/categories/CategoriesTable";
import CategoryTreeView from "@/components/categories/CategoryTreeView";
import CategoryDrilldownView from "@/components/categories/CategoryDrilldownView";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import CategoryModal from "@/components/categories/CategoryModal";
import CategoryStats from "@/components/categories/CategoryStats";
import CategoryHelpDialog from "@/components/categories/CategoryHelpDialog";
import { Category, CategoryFormValues } from "@/types/category";
import { useCategoryManagement } from "@/hooks/useCategoryManagement";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>readcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const CategoriesPage = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [parentCategoryForNew, setParentCategoryForNew] = useState<Category | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<string | null>(null);

  const {
    categories,
    categoriesTree,
    createCategory,
    updateCategory,
    deleteCategory,
    viewMode,
    toggleViewMode,
    isLoading
  } = useCategoryManagement();

  const handleAddCategory = () => {
    setSelectedCategory(null);
    setParentCategoryForNew(null);
    setIsCreateModalOpen(true);
  };

  const handleAddSubcategory = (parentCategory: Category | null) => {
    setSelectedCategory(null);
    setParentCategoryForNew(parentCategory);
    setIsCreateModalOpen(true);
  };

  const handleEditCategory = (category: Category) => {
    setSelectedCategory(category);
    setParentCategoryForNew(null);
    setIsCreateModalOpen(true);
  };

  const handleDeleteCategory = async (categoryId: string) => {
    try {
      await deleteCategory(categoryId);
    } catch (error) {
      console.error("Erro ao excluir categoria:", error);
    }
  };

  const handleSaveCategory = async (categoryData: CategoryFormValues) => {
    try {
      if (selectedCategory) {
        await updateCategory(selectedCategory.id, categoryData);
      } else {
        await createCategory(categoryData);
      }
      setIsCreateModalOpen(false);
      setSelectedCategory(null);
      setParentCategoryForNew(null);
    } catch (error) {
      console.error("Error saving category:", error);
    }
  };

  const handleCloseModal = () => {
    setIsCreateModalOpen(false);
    setSelectedCategory(null);
    setParentCategoryForNew(null);
  };

  return (
    <Layout>
      <div className="container py-6 space-y-6">
        {/* Breadcrumbs */}
        <div className="mb-6">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Início</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Gerenciamento</BreadcrumbPage>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Categorias</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Categorias</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Gerencie categorias de transações para sua empresa
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <CategoryHelpDialog />
            <Button
              onClick={handleAddCategory}
              className="bg-[#007FFF] hover:bg-[#0066CC] text-white font-medium px-4 py-2 rounded-lg flex items-center gap-2"
            >
              <PlusCircle className="h-4 w-4" />
              Adicionar Categoria
            </Button>
          </div>
        </div>

        {/* Estatísticas das categorias */}
        <CategoryStats categories={categoriesTree} className="mb-6" />

        {/* Controles de pesquisa, filtro e visualização */}
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="flex-1 max-w-md">
              <Input
                placeholder="Pesquisar categorias..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-[#007FFF] focus:border-transparent"
              />
            </div>

            <Select
              value={filterType || "all"}
              onValueChange={(value) => setFilterType(value === "all" ? null : value)}
            >
              <SelectTrigger className="w-full sm:w-[180px] h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
                <SelectValue placeholder="Todos os Tipos" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os Tipos</SelectItem>
                <SelectItem value="payable">Despesa</SelectItem>
                <SelectItem value="receivable">Receita</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Toggle de visualização */}
          <div className="flex items-center space-x-4 bg-gray-50 dark:bg-gray-800 rounded-lg p-2">
            <div className="flex items-center space-x-2">
              <TreePine className={`h-4 w-4 ${viewMode === 'tree' ? 'text-[#007FFF]' : 'text-gray-500'}`} />
              <Label htmlFor="view-mode" className="text-sm font-medium">
                Árvore
              </Label>
              <Switch
                id="view-mode"
                checked={viewMode === 'drilldown'}
                onCheckedChange={toggleViewMode}
              />
              <Label htmlFor="view-mode" className="text-sm font-medium">
                Navegação
              </Label>
              <Grid3X3 className={`h-4 w-4 ${viewMode === 'drilldown' ? 'text-[#007FFF]' : 'text-gray-500'}`} />
            </div>
          </div>
        </div>

        {/* Visualização das categorias */}
        {viewMode === 'tree' ? (
          <CategoryTreeView
            categories={categoriesTree}
            onEdit={handleEditCategory}
            onDelete={handleDeleteCategory}
            onAddSubcategory={handleAddSubcategory}
            searchTerm={searchTerm}
            filterType={filterType}
            isLoading={isLoading}
          />
        ) : (
          <CategoryDrilldownView
            categories={categoriesTree}
            onEdit={handleEditCategory}
            onDelete={handleDeleteCategory}
            onAddSubcategory={handleAddSubcategory}
            searchTerm={searchTerm}
            filterType={filterType}
            isLoading={isLoading}
          />
        )}

        <CategoryModal
          category={selectedCategory || undefined}
          isOpen={isCreateModalOpen}
          onClose={handleCloseModal}
          onSave={handleSaveCategory}
          allCategories={categories}
          parentCategory={parentCategoryForNew}
        />
      </div>
    </Layout>
  );
};

export default CategoriesPage;
