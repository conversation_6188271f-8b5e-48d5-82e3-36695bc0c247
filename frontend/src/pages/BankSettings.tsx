
import { Layout } from "@/components/layout/Layout";
import { BankModal } from "@/components/settings/BankModal";
import { BankHeader } from "@/components/banks/BankHeader";
import { BankSearchBar } from "@/components/banks/BankSearchBar";
import { BankList } from "@/components/banks/BankList";
import { BankDeleteDialog } from "@/components/banks/BankDeleteDialog";
import { useBankManagement } from "@/hooks/useBankManagement";
import { Loader2, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const BankSettings = () => {
  const {
    banks,
    loading,
    error,
    searchTerm,
    handleSearchChange,
    currentPage,
    totalPages,
    setCurrentPage,
    isModalOpen,
    setIsModalOpen,
    currentBank,
    handleEditBank,
    handleAddNewBank,
    handleDeleteBank,
    isDeleteDialogOpen,
    setIsDeleteDialogOpen,
    bankToDelete,
    confirmDelete,
    handleSaveBank
  } = useBankManagement();
  
  return (
    <Layout>
      <div className="container mx-auto py-6">
        {/* Breadcrumbs */}
        <div className="mb-6">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Início</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Gerenciamento</BreadcrumbPage>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Bancos</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        <BankHeader onAddNew={handleAddNewBank} />

        <BankSearchBar
          searchTerm={searchTerm}
          onSearchChange={handleSearchChange}
          onAddNew={handleAddNewBank}
        />

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Carregando bancos...</span>
          </div>
        ) : error ? (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Erro ao carregar bancos: {error.message || 'Erro desconhecido'}
            </AlertDescription>
          </Alert>
        ) : (
          <BankList
            banks={banks}
            onEditBank={handleEditBank}
            onDeleteBank={handleDeleteBank}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
            searchTerm={searchTerm}
          />
        )}
      </div>
      
      <BankModal 
        open={isModalOpen} 
        onOpenChange={setIsModalOpen}
        onSave={handleSaveBank}
        bank={currentBank}
      />

      <BankDeleteDialog 
        isOpen={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        bank={bankToDelete}
        onConfirmDelete={confirmDelete}
      />
    </Layout>
  );
};

export default BankSettings;
