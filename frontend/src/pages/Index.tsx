import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import Navbar from '@/components/layout/Navbar';
import Sidebar from '@/components/layout/Sidebar';
import FinancialSummary from '@/components/dashboard/FinancialSummary';
import RecentTransactions from '@/components/dashboard/RecentTransactions';
import ChartSection from '@/components/dashboard/ChartSection';
import { Calendar, CreditCard, Filter, Users, Landmark, ImageOff } from 'lucide-react';
import IconButton from '@/components/ui-custom/IconButton';
import GlassCard from '@/components/ui-custom/GlassCard';
import PeriodFilter from '@/components/dashboard/PeriodFilter';
import AccountFilter from '@/components/dashboard/AccountFilter';
import { Link } from 'react-router-dom';

// Removido dados mockados de contas bancárias

const Index = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('current-month');
  const [selectedAccountId, setSelectedAccountId] = useState('all');
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  // Removido imageErrorStatus - não é mais necessário

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
    // Aqui você pode implementar a lógica para atualizar os dados com base no período selecionado
  };

  const handleAccountChange = (accountId: string) => {
    setSelectedAccountId(accountId);
    // Aqui você pode implementar a lógica para atualizar os dados com base na conta selecionada
  };

  const toggleFilters = () => {
    setIsFiltersOpen(!isFiltersOpen);
  };

  // Removido handleImageError - não é mais necessário

  return (
    <div className="min-h-screen flex bg-background dark:bg-dark-bg">
      <Sidebar isOpen={sidebarOpen} />
      
      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300", 
        sidebarOpen ? "lg:ml-64" : "lg:ml-20"
      )}>
        <Navbar onToggleSidebar={toggleSidebar} />
        
        <main className="flex-1 p-6 overflow-y-auto">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row md:items-center justify-between mb-8 animate-fade-in">
              <div>
                <h1 className="text-2xl font-display font-semibold mb-1">Painel Financeiro</h1>
              </div>
              
              <div className="flex items-center gap-3 mt-4 md:mt-0">
                <PeriodFilter 
                  selectedPeriod={selectedPeriod} 
                  onPeriodChange={handlePeriodChange} 
                />
                
                <AccountFilter
                  selectedAccountId={selectedAccountId}
                  accounts={[]} // Removido bankAccounts mockados
                  onAccountChange={handleAccountChange}
                />
                
                <button className="bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-2 rounded-lg transition-colors text-sm font-medium">
                  + Adicionar Transação
                </button>
              </div>
            </div>
            
            <FinancialSummary 
              selectedPeriod={selectedPeriod}
              selectedAccountId={selectedAccountId}
            />
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-5 mt-5">
              <div className="lg:col-span-2 animate-fade-in" style={{ animationDelay: "0.2s" }}>
                <ChartSection 
                  selectedPeriod={selectedPeriod}
                  selectedAccountId={selectedAccountId}
                />
              </div>
              <div className="animate-fade-in" style={{ animationDelay: "0.3s" }}>
                <RecentTransactions 
                  selectedPeriod={selectedPeriod}
                  selectedAccountId={selectedAccountId}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 mt-5">
              {/* Upcoming Payments Card with improved dark mode styling */}
              <GlassCard 
                className="animate-fade-in shadow-md hover:shadow-lg transition-all relative overflow-hidden" 
                style={{ 
                  animationDelay: "0.4s",
                  background: "linear-gradient(to right, rgb(254, 249, 255) 0%, rgb(245, 243, 255) 100%)",
                  backgroundImage: "var(--tw-gradient-stops)",
                }}
              >
                <div className="absolute top-0 right-0 w-24 h-24 rounded-full bg-blue-50 dark:bg-blue-900/10 -translate-y-8 translate-x-8"></div>
                <div className="absolute bottom-0 left-0 w-32 h-32 rounded-full bg-indigo-50 dark:bg-indigo-900/10 translate-y-10 -translate-x-10"></div>
                
                <div className="flex justify-between items-center mb-4 relative">
                  <h3 className="font-medium text-indigo-800 dark:text-indigo-300 text-lg">Pagamentos Próximos</h3>
                  <IconButton 
                    icon={<Calendar className="h-4 w-4 text-indigo-600 dark:text-indigo-300" />} 
                    variant="ghost" 
                    size="sm" 
                    className="bg-indigo-50 dark:bg-indigo-900/20 hover:bg-indigo-100 dark:hover:bg-indigo-800/40"
                  />
                </div>
                
                <div className="space-y-3 relative">
                  <div className="text-center py-8 text-indigo-600/60 dark:text-indigo-400/60">
                    <div className="flex flex-col items-center gap-2">
                      <Calendar className="h-8 w-8 text-indigo-600/40 dark:text-indigo-400/40" />
                      <p className="text-sm">Nenhum pagamento próximo</p>
                      <p className="text-xs">Os pagamentos aparecerão aqui quando forem cadastrados</p>
                    </div>
                  </div>
                </div>
                
                <Link 
                  to="/accounts-payable" 
                  className="block w-full mt-4 py-2 text-sm text-center text-indigo-600 dark:text-indigo-300 font-medium hover:bg-indigo-50/80 dark:hover:bg-indigo-900/20 rounded-lg transition-colors relative"
                >
                  Ver Todos
                </Link>
              </GlassCard>
              
              {/* Top Clients Card with improved dark mode styling */}
              <GlassCard 
                className="animate-fade-in shadow-md hover:shadow-lg transition-all relative overflow-hidden" 
                style={{ 
                  animationDelay: "0.5s",
                  background: "linear-gradient(to right, rgb(255, 251, 245) 0%, rgb(255, 247, 240) 100%)" 
                }}
              >
                <div className="absolute top-0 right-0 w-24 h-24 rounded-full bg-orange-50 dark:bg-orange-900/10 -translate-y-8 translate-x-8"></div>
                <div className="absolute bottom-0 left-0 w-32 h-32 rounded-full bg-amber-50 dark:bg-amber-900/10 translate-y-10 -translate-x-10"></div>
                
                <div className="flex justify-between items-center mb-4 relative">
                  <h3 className="font-medium text-amber-800 dark:text-amber-300 text-lg">Principais Clientes</h3>
                  <IconButton 
                    icon={<Users className="h-4 w-4 text-amber-600 dark:text-amber-300" />} 
                    variant="ghost" 
                    size="sm"
                    className="bg-amber-50 dark:bg-amber-900/20 hover:bg-amber-100 dark:hover:bg-amber-800/40"
                  />
                </div>
                
                <div className="space-y-3 relative">
                  <div className="text-center py-8 text-amber-600/60 dark:text-amber-400/60">
                    <div className="flex flex-col items-center gap-2">
                      <Users className="h-8 w-8 text-amber-600/40 dark:text-amber-400/40" />
                      <p className="text-sm">Nenhum cliente cadastrado</p>
                      <p className="text-xs">Os clientes aparecerão aqui quando forem cadastrados</p>
                    </div>
                  </div>
                </div>
                
                <Link 
                  to="/customers" 
                  className="block w-full mt-4 py-2 text-sm text-center text-amber-600 dark:text-amber-300 font-medium hover:bg-amber-50/80 dark:hover:bg-amber-900/20 rounded-lg transition-colors relative"
                >
                  Ver Todos os Clientes
                </Link>
              </GlassCard>
              
              {/* Bank Accounts Card with improved dark mode styling */}
              <GlassCard 
                className="animate-fade-in shadow-md hover:shadow-lg transition-all relative overflow-hidden" 
                style={{ 
                  animationDelay: "0.6s",
                  background: "linear-gradient(to right, rgb(245, 251, 255) 0%, rgb(240, 247, 255) 100%)"
                }}
              >
                <div className="absolute top-0 right-0 w-24 h-24 rounded-full bg-cyan-50 dark:bg-cyan-900/10 -translate-y-8 translate-x-8"></div>
                <div className="absolute bottom-0 left-0 w-32 h-32 rounded-full bg-sky-50 dark:bg-sky-900/10 translate-y-10 -translate-x-10"></div>
                
                <div className="flex justify-between items-center mb-4 relative">
                  <h3 className="font-medium text-sky-800 dark:text-sky-300 text-lg">Contas Bancárias</h3>
                  <IconButton 
                    icon={<CreditCard className="h-4 w-4 text-sky-600 dark:text-sky-300" />} 
                    variant="ghost" 
                    size="sm" 
                    className="bg-sky-50 dark:bg-sky-900/20 hover:bg-sky-100 dark:hover:bg-sky-800/40"
                  />
                </div>
                
                <div className="space-y-3 relative">
                  <div className="text-center py-8 text-sky-600/60 dark:text-sky-400/60">
                    <div className="flex flex-col items-center gap-2">
                      <CreditCard className="h-8 w-8 text-sky-600/40 dark:text-sky-400/40" />
                      <p className="text-sm">Nenhuma conta bancária cadastrada</p>
                      <p className="text-xs">As contas bancárias aparecerão aqui quando forem cadastradas</p>
                    </div>
                  </div>
                </div>
                
                <Link 
                  to="/bank-accounts" 
                  className="block w-full mt-4 py-2 text-sm text-center text-sky-600 dark:text-sky-300 font-medium hover:bg-sky-50/80 dark:hover:bg-sky-900/20 rounded-lg transition-colors relative"
                >
                  Ver Todas as Contas
                </Link>
              </GlassCard>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Index;
