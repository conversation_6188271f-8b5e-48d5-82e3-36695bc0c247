
import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Plus, Search, Mail, Phone, Edit, Trash2 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { useToast } from "@/hooks/use-toast";
import { useClients, useDeleteEntity } from "@/hooks/api/useEntities";
import { Entity } from "@/types/api";
import { toast } from 'sonner';

const Customers = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast: uiToast } = useToast();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState("");

  // Usar hooks de API reais
  const { data: clientsData, isLoading: loading, error } = useClients(1, 50, searchQuery);
  const deleteEntityMutation = useDeleteEntity();

  const customers = clientsData?.data || [];
  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (customer.email && customer.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (customer.phone && customer.phone.includes(searchQuery))
  );

  const handleAddCustomer = () => {
    navigate('/customers/novo');
  };

  const handleEditCustomer = (customer: any) => {
    navigate(`/customers/editar/${customer.id}`);
  };

  const handleViewCustomer = (customer: any) => {
    navigate(`/customers/visualizar/${customer.id}`);
  };







  const handleDeleteCustomer = async (customerId: string) => {
    try {
      await deleteEntityMutation.mutateAsync(customerId);
      toast.success("Cliente removido com sucesso!");
    } catch (error: any) {
      console.error('Erro ao remover cliente:', error);
      toast.error(error.response?.data?.message || 'Erro ao remover cliente. Tente novamente.');
    }
  };

  return (
    <Layout location={location}>
      <div className="container p-4 mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Clientes</h1>
          <Button onClick={handleAddCustomer} className="gap-2">
            <Plus size={18} />
            Novo Cliente
          </Button>
        </div>

        <Card className="mb-6">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle>Todos os Clientes</CardTitle>
              <div className="relative w-72">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar clientes..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Contato</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Telefone</TableHead>
                  <TableHead>Data de Cadastro</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                      Carregando clientes...
                    </TableCell>
                  </TableRow>
                ) : error ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-6 text-destructive">
                      Erro ao carregar clientes: {error.message || 'Erro desconhecido'}
                    </TableCell>
                  </TableRow>
                ) : filteredCustomers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                      {customers.length === 0
                        ? "Nenhum cliente cadastrado. Comece criando seu primeiro cliente."
                        : "Nenhum cliente encontrado com os filtros aplicados."
                      }
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredCustomers.map((customer) => (
                    <TableRow key={customer.id}>
                      <TableCell className="font-medium">
                        <button
                          onClick={() => handleViewCustomer(customer)}
                          className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer text-left"
                        >
                          {customer.name}
                        </button>
                      </TableCell>
                      <TableCell>{customer.contact || '-'}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Mail size={16} className="text-muted-foreground" />
                          {customer.email || '-'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Phone size={16} className="text-muted-foreground" />
                          {customer.phone || '-'}
                        </div>
                      </TableCell>
                      <TableCell>{new Date(customer.createdAt).toLocaleDateString('pt-BR')}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            onClick={() => handleEditCustomer(customer)}
                          >
                            <Edit size={18} className="text-muted-foreground" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="icon"
                            onClick={() => handleDeleteCustomer(customer.id)}
                          >
                            <Trash2 size={18} className="text-destructive" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>


    </Layout>
  );
};

export default Customers;
