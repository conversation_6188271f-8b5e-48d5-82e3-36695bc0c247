import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Loader2 } from 'lucide-react';
import { LoginRequest } from '@/types/api';
import monitoringService from '@/services/api/monitoringService';

const Login = () => {
  const { login, loading, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [formData, setFormData] = useState<LoginRequest>({
    email: '',
    password: '',
  });
  const [formErrors, setFormErrors] = useState({
    email: '',
    password: '',
  });

  // Registrar visualização da página
  useEffect(() => {
    monitoringService.recordEvent('login_page_view');
  }, []);

  // Verificar se há um redirecionamento pendente após login bem-sucedido
  useEffect(() => {
    // Verificar se o usuário está autenticado e não estamos carregando
    if (isAuthenticated && !loading) {
      // Obter o caminho de redirecionamento e armazená-lo em uma variável local
      const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/';
      sessionStorage.removeItem('redirectAfterLogin');

      monitoringService.recordEvent('login_redirect', {
        to: redirectPath,
        from: location.pathname
      });

      // Usar um atraso maior para garantir que o estado seja atualizado
      const timeoutId = setTimeout(() => {
        navigate(redirectPath, { replace: true });
      }, 500);

      // Limpar o timeout se o componente for desmontado
      return () => clearTimeout(timeoutId);
    }
  }, [isAuthenticated, navigate, location, loading]);

  const validateForm = (): boolean => {
    let isValid = true;
    const errors = {
      email: '',
      password: '',
    };

    if (!formData.email) {
      errors.email = 'E-mail é obrigatório';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'E-mail inválido';
      isValid = false;
    }

    if (!formData.password) {
      errors.password = 'Senha é obrigatória';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      monitoringService.recordEvent('login_attempt', { email: formData.email });
      await login(formData);
      // Redirecionamento é feito pelo useEffect acima
    } catch (error) {
      // Erro já tratado no contexto de autenticação
      monitoringService.recordEvent('login_failure', { email: formData.email });
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">FluxoMax</CardTitle>
          <CardDescription className="text-center">
            Entre com seu e-mail e senha para acessar o sistema
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">E-mail</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleChange}
                disabled={loading}
                autoComplete="username email"
              />
              {formErrors.email && (
                <p className="text-sm text-red-500">{formErrors.email}</p>
              )}
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">Senha</Label>
                <Link to="/forgot-password" className="text-sm text-blue-600 hover:underline">
                  Esqueceu a senha?
                </Link>
              </div>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="••••••••"
                value={formData.password}
                onChange={handleChange}
                disabled={loading}
                autoComplete="current-password"
              />
              {formErrors.password && (
                <p className="text-sm text-red-500">{formErrors.password}</p>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Entrando...
                </>
              ) : (
                'Entrar'
              )}
            </Button>
            <p className="text-center text-sm">
              Não tem uma conta?{' '}
              <Link to="/register" className="text-blue-600 hover:underline">
                Registre-se
              </Link>
            </p>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
};

export default Login;
