import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';

import { EntityFormLayout } from '@/components/forms/EntityFormLayout';
import { Form } from '@/components/ui/form';
import { EntityAddressSection } from '@/components/address/EntityAddressSection';
import { 
  useCreateEntity, 
  useUpdateEntity, 
  useEntity 
} from '@/hooks/api/useEntities';
import { useEntityAddresses } from '@/hooks/api/useEntityAddresses';
import { entityAddressService } from '@/services/api/entityAddressService';
import { 
  CreateEntityRequest, 
  UpdateEntityRequest, 
  EntityType, 
  PersonType, 
  Address, 
  AddressType 
} from '@/types/api';
import { BrazilianEntityHelper } from '@/constants/brazilianEntities';
import { createMinimalEntitySchema, EntityFormValues } from '@/schemas/entitySchemas';
import { 
  formatCPF, 
  formatCNPJ, 
  formatBrazilianPhone, 
  cleanDocument 
} from '@/utils/brazilianDocuments';

// Importar os componentes de formulário do modal existente
import { BrazilianEntityFormFields } from '@/components/entities/BrazilianEntityFormFields';

const SupplierFormPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  
  const isEditMode = Boolean(id);
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedPersonType, setSelectedPersonType] = useState<PersonType>('company');

  // Hooks de API
  const { data: entity, isLoading: isLoadingEntity, error: entityError } = useEntity(id || '');
  const { data: entityAddresses, isLoading: isLoadingAddresses } = useEntityAddresses(id || '');
  const createEntityMutation = useCreateEntity();
  const updateEntityMutation = useUpdateEntity();



  // Configuração do formulário com resolver dinâmico - usando schema mínimo
  const formConfig = useMemo(() => ({
    resolver: zodResolver(createMinimalEntitySchema(selectedPersonType)),
    mode: 'onChange' as const, // Validar em tempo real
    defaultValues: {
      name: "",
      entityType: 'supplier' as const,
      personType: 'company' as const,
      email: "",
      phone: "",
      mobile: "",
      website: "",
      contact: "",
      status: 'active' as const,
      creditLimit: undefined,
      paymentTermDays: undefined,
      discountPercentage: undefined,
      notes: "",
      // Campos para pessoa física
      cpf: "",
      rg: "",
      rgIssuer: "",
      // Campos para pessoa jurídica
      tradeName: "",
      cnpj: "",
      stateRegistration: "",
      municipalRegistration: "",
      taxRegime: 'simple_national' as const,
      icmsTaxpayer: false,
      simpleNational: false,
      withholdTaxes: false,
      sendNfe: true,
      nfeEmail: "",
      // Dados bancários
      bankCode: "",
      bankName: "",
      agency: "",
      account: "",
      accountDigit: "",
      pixKey: "",
    },
  }), [selectedPersonType]);

  const form = useForm<EntityFormValues>(formConfig);

  // Watch person type changes
  const watchedPersonType = form.watch('personType');

  useEffect(() => {
    if (watchedPersonType !== selectedPersonType) {
      setSelectedPersonType(watchedPersonType);
      const currentValues = form.getValues();
      form.reset(currentValues);

      // Forçar revalidação após mudança de tipo de pessoa
      setTimeout(async () => {
        await form.trigger();
      }, 100);
    }
  }, [watchedPersonType, selectedPersonType, form]);

  // Carregar dados da entidade para edição
  useEffect(() => {
    if (isEditMode && entity) {
      const personType = entity.personType || (entity.cnpj ? 'company' : 'individual');
      setSelectedPersonType(personType);

      form.reset({
        name: entity.name || "",
        entityType: 'supplier',
        personType: personType,
        tradeName: entity.tradeName || "",
        cnpj: entity.cnpj || "",
        cpf: entity.cpf || "",
        stateRegistration: entity.stateRegistration || "",
        municipalRegistration: entity.municipalRegistration || "",
        rg: entity.rg || "",
        rgIssuer: entity.rgIssuer || "",
        taxRegime: entity.taxRegime || BrazilianEntityHelper.getDefaultTaxRegime(personType),
        email: entity.email || "",
        phone: entity.phone || "",
        mobile: entity.mobile || "",
        website: entity.website || "",
        contact: entity.contact || "",
        status: entity.status || 'active',
        creditLimit: entity.creditLimit || undefined,
        paymentTermDays: entity.paymentTermDays || undefined,
        discountPercentage: entity.discountPercentage || undefined,
        icmsTaxpayer: entity.icmsTaxpayer || false,
        simpleNational: entity.simpleNational || false,
        withholdTaxes: entity.withholdTaxes || false,
        sendNfe: entity.sendNfe !== undefined ? entity.sendNfe : true,
        nfeEmail: entity.nfeEmail || "",
        bankCode: entity.bankCode || "",
        bankName: entity.bankName || "",
        agency: entity.agency || "",
        account: entity.account || "",
        accountDigit: entity.accountDigit || "",
        pixKey: entity.pixKey || "",
        notes: entity.notes || "",
      });

      // Forçar revalidação do formulário após reset para atualizar form.formState.isValid
      setTimeout(async () => {
        await form.trigger();
      }, 100);
    }
  }, [isEditMode, entity, form]);

  // Carregar endereços da entidade (apenas para modo de criação)
  useEffect(() => {
    if (isEditMode && entityAddresses) {
      setAddresses(entityAddresses);
    }
  }, [isEditMode, entityAddresses]);

  // Handler para salvar
  const handleSave = async () => {
    const isValid = await form.trigger();
    if (!isValid) {
      toast.error('Por favor, corrija os erros no formulário antes de continuar.');
      return;
    }

    const data = form.getValues();
    setIsSaving(true);

    try {
      // Validate data consistency - usando validação mínima para permitir mais flexibilidade
      const validation = BrazilianEntityHelper.validateEntityDataMinimal(data);
      if (!validation.isValid) {
        validation.errors.forEach(error => toast.error(error));
        setIsSaving(false);
        return;
      }

      // Clean and format documents
      const cleanedData = {
        ...data,
        cpf: data.cpf ? formatCPF(cleanDocument(data.cpf)) : undefined,
        cnpj: data.cnpj ? formatCNPJ(cleanDocument(data.cnpj)) : undefined,
        phone: data.phone ? formatBrazilianPhone(cleanDocument(data.phone)) : undefined,
        mobile: data.mobile ? formatBrazilianPhone(cleanDocument(data.mobile)) : undefined,
        // REMOVIDO: addresses - não deve ser enviado na criação da entidade
      };

      // Remover campos undefined e strings vazias para evitar problemas de validação no backend
      const filteredData = Object.fromEntries(
        Object.entries(cleanedData).filter(([key, value]) => {
          // Remover valores undefined
          if (value === undefined) return false;

          // Para campos de email, remover strings vazias para evitar erro de validação
          if ((key === 'email' || key === 'nfeEmail') && value === '') return false;

          return true;
        })
      );

      if (isEditMode && id) {
        // Atualizar fornecedor existente
        await updateEntityMutation.mutateAsync({
          id,
          data: filteredData as UpdateEntityRequest
        });

        // Endereços são gerenciados automaticamente pelo EntityAddressSection
        queryClient.invalidateQueries({ queryKey: ['entity-addresses', id] });
        toast.success(`Fornecedor ${filteredData.name || 'Fornecedor'} atualizado com sucesso!`);
      } else {
        // Criar novo fornecedor
        const entityRequest: CreateEntityRequest = {
          ...filteredData as CreateEntityRequest,
          entityType: 'supplier' as EntityType,
          // REMOVIDO: type: 'supplier' - campo duplicado, entityType já define o tipo
        };

        const newEntity = await createEntityMutation.mutateAsync(entityRequest);

        if (!newEntity || !newEntity.id) {
          throw new Error('Resposta inválida do servidor: entidade criada sem ID');
        }

        // Criar endereços se existirem
        if (addresses && addresses.length > 0) {
          await createEntityAddresses(newEntity.id, addresses);
        }

        toast.success(`Fornecedor ${filteredData.name || 'Fornecedor'} criado com sucesso!`);
      }

      // Navegar de volta para a lista
      navigate('/suppliers');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Erro ao salvar fornecedor. Tente novamente.');
    } finally {
      setIsSaving(false);
    }
  };

  // Função para criar endereços de uma entidade
  const createEntityAddresses = async (entityId: string, addresses: (Address & { type?: AddressType })[]) => {
    if (!addresses || addresses.length === 0) return;

    for (const address of addresses) {
      try {
        if (!address.street || !address.city || !address.state || !address.zipCode) {
          toast.error(`Endereço incompleto: ${address.street || 'Rua não informada'}`);
          continue;
        }

        if (!address.district || address.district.trim() === '') {
          toast.error(`Bairro é obrigatório para o endereço: ${address.street}`);
          continue;
        }

        const addressData = {
          street: address.street,
          number: address.number || '',
          complement: address.complement || '',
          district: address.district,
          neighborhood: address.neighborhood || address.district,
          city: address.city,
          state: address.state,
          zipCode: address.zipCode,
          country: address.country || 'Brasil',
          isDefault: address.isDefault || false,
          type: address.type || 'main',
        };

        await entityAddressService.createEntityAddress(entityId, addressData);
      } catch (error: any) {
        const errorMessage = error.response?.data?.message || error.message || 'Erro desconhecido ao criar endereço';
        toast.error(`Erro ao criar endereço ${address.street}: ${errorMessage}`);
      }
    }
  };



  const isLoading = isLoadingEntity || isLoadingAddresses;
  const isDirty = form.formState.isDirty || addresses.length > 0;

  return (
    <EntityFormLayout
      title={isEditMode ? 'Editar Fornecedor' : 'Novo Fornecedor'}
      entityType="fornecedor"
      mode={isEditMode ? 'edit' : 'create'}
      entityName={entity?.name}
      isLoading={isLoading}
      isSaving={isSaving}
      isDirty={isDirty}
      onSave={handleSave}
      canSave={form.formState.isValid}
    >
      <Form {...form}>
        <form className="space-y-6">
          {/* Campos do formulário brasileiro de entidades */}
          <BrazilianEntityFormFields
            form={form}
            selectedPersonType={selectedPersonType}
            defaultEntityType="supplier"
          />

          {/* Seção de endereços */}
          <EntityAddressSection
            entityId={id}
            addresses={addresses}
            onAddressesChange={setAddresses}
            isEditMode={isEditMode}
          />
        </form>
      </Form>
    </EntityFormLayout>
  );
};

export default SupplierFormPage;
