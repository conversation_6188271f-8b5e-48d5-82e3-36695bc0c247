import React, { useState } from 'react';
import { Plus, <PERSON>ting<PERSON>, Eye, Edit, Copy, Trash2, Power, PowerOff } from 'lucide-react';
import { Layout } from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { useDebounce } from '@/hooks/useDebounce';
import {
  useEntityConfigs,
  useDeleteEntityConfig,
  useActivateEntityConfig,
  useDeactivateEntityConfig,
  useDuplicateEntityConfig,
  useEntityConfigStats
} from '@/hooks/api/useEntityConfig';
import { EntityValidationConfig } from '@/types/entityConfig';
import { formatDate } from '@/utils/dateUtils';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { toast } from 'sonner';

export const EntityConfigPage: React.FC = () => {
  const [search, setSearch] = useState('');
  const [selectedConfig, setSelectedConfig] = useState<EntityValidationConfig | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showDuplicateDialog, setShowDuplicateDialog] = useState(false);
  const [duplicateName, setDuplicateName] = useState('');

  const debouncedSearch = useDebounce(search, 300);

  // Queries
  const { data: configsData, isLoading } = useEntityConfigs(1, 50, debouncedSearch);
  const { data: stats } = useEntityConfigStats();

  // Mutations
  const deleteConfigMutation = useDeleteEntityConfig();
  const activateConfigMutation = useActivateEntityConfig();
  const deactivateConfigMutation = useDeactivateEntityConfig();
  const duplicateConfigMutation = useDuplicateEntityConfig();

  const configs = configsData?.data || [];

  const handleDelete = async () => {
    if (!selectedConfig) return;
    
    try {
      await deleteConfigMutation.mutateAsync(selectedConfig.id);
      setShowDeleteDialog(false);
      setSelectedConfig(null);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleActivate = async (config: EntityValidationConfig) => {
    try {
      await activateConfigMutation.mutateAsync(config.id);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleDeactivate = async (config: EntityValidationConfig) => {
    try {
      await deactivateConfigMutation.mutateAsync(config.id);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleDuplicate = async () => {
    if (!selectedConfig || !duplicateName.trim()) return;

    try {
      await duplicateConfigMutation.mutateAsync({
        id: selectedConfig.id,
        name: duplicateName.trim()
      });
      setShowDuplicateDialog(false);
      setSelectedConfig(null);
      setDuplicateName('');
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const getStatusBadge = (config: EntityValidationConfig) => {
    if (config.isActive) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Ativa</Badge>;
    }
    return <Badge variant="secondary">Inativa</Badge>;
  };

  const getRequiredFieldsCount = (config: EntityValidationConfig) => {
    return config.fields.filter(field => field.isRequired).length;
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size={32} />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Configurações de Campos Obrigatórios</h1>
            <p className="text-muted-foreground">
              Gerencie quais campos são obrigatórios no cadastro de clientes e fornecedores
            </p>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Nova Configuração
          </Button>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total de Configurações</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalConfigs}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Configurações Ativas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.activeConfigs}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Campo Mais Usado</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm font-medium">
                  {stats.mostUsedFields[0]?.fieldName || 'N/A'}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Categorias</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.configsByCategory.length}</div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Search */}
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Buscar configurações..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="max-w-sm"
          />
        </div>

        {/* Configurations Table */}
        <Card>
          <CardHeader>
            <CardTitle>Configurações</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Descrição</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Campos Obrigatórios</TableHead>
                  <TableHead>Criado em</TableHead>
                  <TableHead className="w-[100px]">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {configs.map((config) => (
                  <TableRow key={config.id}>
                    <TableCell className="font-medium">{config.name}</TableCell>
                    <TableCell className="text-muted-foreground">
                      {config.description}
                    </TableCell>
                    <TableCell>{getStatusBadge(config)}</TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {getRequiredFieldsCount(config)} campos
                      </Badge>
                    </TableCell>
                    <TableCell>{formatDate(config.createdAt)}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Settings className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="h-4 w-4 mr-2" />
                            Visualizar
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="h-4 w-4 mr-2" />
                            Editar
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedConfig(config);
                              setDuplicateName(`${config.name} (Cópia)`);
                              setShowDuplicateDialog(true);
                            }}
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            Duplicar
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {config.isActive ? (
                            <DropdownMenuItem
                              onClick={() => handleDeactivate(config)}
                              className="text-orange-600"
                            >
                              <PowerOff className="h-4 w-4 mr-2" />
                              Desativar
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem
                              onClick={() => handleActivate(config)}
                              className="text-green-600"
                            >
                              <Power className="h-4 w-4 mr-2" />
                              Ativar
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedConfig(config);
                              setShowDeleteDialog(true);
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Excluir
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {configs.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                Nenhuma configuração encontrada
              </div>
            )}
          </CardContent>
        </Card>

        {/* Delete Dialog */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Excluir Configuração</AlertDialogTitle>
              <AlertDialogDescription>
                Tem certeza que deseja excluir a configuração "{selectedConfig?.name}"?
                Esta ação não pode ser desfeita.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-red-600 hover:bg-red-700"
              >
                Excluir
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Duplicate Dialog */}
        <AlertDialog open={showDuplicateDialog} onOpenChange={setShowDuplicateDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Duplicar Configuração</AlertDialogTitle>
              <AlertDialogDescription>
                Digite um nome para a nova configuração:
              </AlertDialogDescription>
            </AlertDialogHeader>
            <div className="py-4">
              <Input
                value={duplicateName}
                onChange={(e) => setDuplicateName(e.target.value)}
                placeholder="Nome da nova configuração"
              />
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDuplicate}
                disabled={!duplicateName.trim()}
              >
                Duplicar
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </Layout>
  );
};
