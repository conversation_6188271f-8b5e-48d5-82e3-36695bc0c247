import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';

import { EntityFormLayout } from '@/components/forms/EntityFormLayout';
import { Form } from '@/components/ui/form';
import { EntityAddressSection } from '@/components/address/EntityAddressSection';
import {
  useCreateEntity,
  useUpdateEntity,
  useEntity
} from '@/hooks/api/useEntities';
import { useEntityAddresses } from '@/hooks/api/useEntityAddresses';
import { entityAddressService } from '@/services/api/entityAddressService';
import { 
  CreateEntityRequest, 
  UpdateEntityRequest, 
  EntityType, 
  PersonType, 
  Address, 
  AddressType 
} from '@/types/api';
import { BrazilianEntityHelper } from '@/constants/brazilianEntities';
import { createMinimalEntitySchema, EntityFormValues } from '@/schemas/entitySchemas';
import { 
  formatCPF, 
  formatCNPJ, 
  formatBrazilianPhone, 
  cleanDocument 
} from '@/utils/brazilianDocuments';

// Importar os componentes de formulário do modal existente
import { BrazilianEntityFormFields } from '@/components/entities/BrazilianEntityFormFields';

const CustomerFormPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  
  const isEditMode = Boolean(id);
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedPersonType, setSelectedPersonType] = useState<PersonType>('company');

  // Hooks de API
  const { data: entity, isLoading: isLoadingEntity, error: entityError } = useEntity(id || '');
  const { data: entityAddresses, isLoading: isLoadingAddresses } = useEntityAddresses(id || '');
  const createEntityMutation = useCreateEntity();
  const updateEntityMutation = useUpdateEntity();



  // Configuração do formulário com resolver dinâmico - usando schema mínimo
  const formConfig = useMemo(() => ({
    resolver: zodResolver(createMinimalEntitySchema(selectedPersonType)),
    mode: 'onChange' as const, // Validar em tempo real
    defaultValues: {
      name: "",
      entityType: 'customer' as const,
      personType: 'company' as const,
      email: "",
      phone: "",
      mobile: "",
      website: "",
      contact: "",
      status: 'active' as const,
      creditLimit: undefined,
      paymentTermDays: undefined,
      discountPercentage: undefined,
      notes: "",
      // Campos para pessoa física
      cpf: "",
      rg: "",
      rgIssuer: "",
      // Campos para pessoa jurídica
      tradeName: "",
      cnpj: "",
      stateRegistration: "",
      municipalRegistration: "",
      taxRegime: 'simple_national' as const,
      icmsTaxpayer: false,
      simpleNational: false,
      withholdTaxes: false,
      sendNfe: true,
      nfeEmail: "",
      bankCode: "",
      bankName: "",
      agency: "",
      account: "",
      accountDigit: "",
      pixKey: "",
    },
  }), [selectedPersonType]);

  const form = useForm<EntityFormValues>(formConfig);

  // Watch person type changes
  const watchedPersonType = form.watch('personType');

  useEffect(() => {
    if (watchedPersonType !== selectedPersonType) {
      setSelectedPersonType(watchedPersonType);
      const currentValues = form.getValues();
      form.reset(currentValues);

      // Forçar revalidação após mudança de tipo de pessoa
      setTimeout(async () => {
        await form.trigger();
      }, 100);
    }
  }, [watchedPersonType, selectedPersonType, form]);

  // Carregar dados da entidade para edição
  useEffect(() => {
    if (isEditMode && entity) {
      const personType = entity.personType || (entity.cnpj ? 'company' : 'individual');
      setSelectedPersonType(personType);

      form.reset({
        name: entity.name || "",
        entityType: 'customer',
        personType: personType,
        tradeName: entity.tradeName || "",
        cnpj: entity.cnpj || "",
        cpf: entity.cpf || "",
        stateRegistration: entity.stateRegistration || "",
        municipalRegistration: entity.municipalRegistration || "",
        rg: entity.rg || "",
        rgIssuer: entity.rgIssuer || "",
        taxRegime: entity.taxRegime || BrazilianEntityHelper.getDefaultTaxRegime(personType),
        email: entity.email || "",
        phone: entity.phone || "",
        mobile: entity.mobile || "",
        website: entity.website || "",
        contact: entity.contact || "",
        status: entity.status || 'active',
        creditLimit: entity.creditLimit || undefined,
        paymentTermDays: entity.paymentTermDays || undefined,
        discountPercentage: entity.discountPercentage || undefined,
        icmsTaxpayer: entity.icmsTaxpayer || false,
        simpleNational: entity.simpleNational || false,
        withholdTaxes: entity.withholdTaxes || false,
        sendNfe: entity.sendNfe !== undefined ? entity.sendNfe : true,
        nfeEmail: entity.nfeEmail || "",
        bankCode: entity.bankCode || "",
        bankName: entity.bankName || "",
        agency: entity.agency || "",
        account: entity.account || "",
        accountDigit: entity.accountDigit || "",
        pixKey: entity.pixKey || "",
        notes: entity.notes || "",
      });

      // Forçar revalidação do formulário após reset para atualizar form.formState.isValid
      setTimeout(async () => {
        await form.trigger();
      }, 100);
    }
  }, [isEditMode, entity, form]);

  // Carregar endereços da entidade (apenas para modo de criação)
  useEffect(() => {
    if (isEditMode && entityAddresses) {
      setAddresses(entityAddresses);
    }
  }, [isEditMode, entityAddresses]);

  // Handler para salvar
  const handleSave = async () => {
    const isValid = await form.trigger();
    if (!isValid) {
      toast.error('Por favor, corrija os erros no formulário antes de continuar.');
      return;
    }

    const data = form.getValues();
    setIsSaving(true);

    try {
      // Validate data consistency - usando validação mínima para permitir mais flexibilidade
      const validation = BrazilianEntityHelper.validateEntityDataMinimal(data);
      if (!validation.isValid) {
        validation.errors.forEach(error => toast.error(error));
        setIsSaving(false);
        return;
      }

      // Clean and format documents
      const cleanedData = {
        ...data,
        cpf: data.cpf && data.cpf.trim() ? formatCPF(cleanDocument(data.cpf)) : undefined,
        cnpj: data.cnpj && data.cnpj.trim() ? formatCNPJ(cleanDocument(data.cnpj)) : undefined,
        phone: data.phone && data.phone.trim() ? formatBrazilianPhone(cleanDocument(data.phone)) : undefined,
        mobile: data.mobile && data.mobile.trim() ? formatBrazilianPhone(cleanDocument(data.mobile)) : undefined,
        stateRegistration: data.stateRegistration && data.stateRegistration.trim() ? data.stateRegistration.trim() :
          (data.personType === 'company' ? 'ISENTO' : undefined), // Para PJ, usar ISENTO se não informado
        municipalRegistration: data.municipalRegistration && data.municipalRegistration.trim() ? data.municipalRegistration.trim() : undefined,
        rg: data.rg && data.rg.trim() ? data.rg.trim() : undefined,
        rgIssuer: data.rgIssuer && data.rgIssuer.trim() ? data.rgIssuer.trim() : undefined,
        tradeName: data.tradeName && data.tradeName.trim() ? data.tradeName.trim() : undefined,
        email: data.email && data.email.trim() ? data.email.trim() : undefined,
        website: data.website && data.website.trim() ? data.website.trim() : undefined,
        contact: data.contact && data.contact.trim() ? data.contact.trim() : undefined,
        nfeEmail: data.nfeEmail && data.nfeEmail.trim() ? data.nfeEmail.trim() : undefined,
        bankCode: data.bankCode && data.bankCode.trim() ? data.bankCode.trim() : undefined,
        bankName: data.bankName && data.bankName.trim() ? data.bankName.trim() : undefined,
        agency: data.agency && data.agency.trim() ? data.agency.trim() : undefined,
        account: data.account && data.account.trim() ? data.account.trim() : undefined,
        accountDigit: data.accountDigit && data.accountDigit.trim() ? data.accountDigit.trim() : undefined,
        pixKey: data.pixKey && data.pixKey.trim() ? data.pixKey.trim() : undefined,
        notes: data.notes && data.notes.trim() ? data.notes.trim() : undefined,
        // REMOVIDO: addresses - não deve ser enviado na criação da entidade
      };

      // Remover campos undefined e strings vazias para evitar problemas de validação no backend
      const filteredData = Object.fromEntries(
        Object.entries(cleanedData).filter(([key, value]) => {
          // Remover valores undefined
          if (value === undefined) return false;

          // Para campos de email, remover strings vazias para evitar erro de validação
          if ((key === 'email' || key === 'nfeEmail') && value === '') return false;

          return true;
        })
      );



      if (isEditMode && id) {
        // Atualizar cliente existente
        await updateEntityMutation.mutateAsync({
          id,
          data: filteredData as UpdateEntityRequest
        });

        // Endereços são gerenciados automaticamente pelo EntityAddressSection
        queryClient.invalidateQueries({ queryKey: ['entity-addresses', id] });
        toast.success(`Cliente ${filteredData.name || 'Cliente'} atualizado com sucesso!`);
      } else {
        // Criar novo cliente
        const entityRequest: CreateEntityRequest = {
          ...filteredData as CreateEntityRequest,
          entityType: 'customer',
          // REMOVIDO: type: 'customer' - campo duplicado, entityType já define o tipo
        };

        const newEntity = await createEntityMutation.mutateAsync(entityRequest);

        if (!newEntity || !newEntity.id) {
          throw new Error('Resposta inválida do servidor: entidade criada sem ID');
        }

        // Criar endereços se existirem
        if (addresses && addresses.length > 0) {
          await createEntityAddresses(newEntity.id, addresses);
        }

        toast.success(`Cliente ${filteredData.name || 'Cliente'} criado com sucesso!`);
      }

      // Navegar de volta para a lista
      navigate('/customers');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Erro ao salvar cliente. Tente novamente.');
    } finally {
      setIsSaving(false);
    }
  };

  // Função para criar endereços de uma entidade
  const createEntityAddresses = async (entityId: string, addresses: (Address & { type?: AddressType })[]) => {
    if (!addresses || addresses.length === 0) return;

    for (const address of addresses) {
      try {
        if (!address.street || !address.city || !address.state || !address.zipCode) {
          console.error('Dados obrigatórios do endereço estão faltando:', address);
          toast.error(`Endereço incompleto: ${address.street || 'Rua não informada'}`);
          continue;
        }

        if (!address.district || address.district.trim() === '') {
          console.error('Distrito/bairro é obrigatório:', address);
          toast.error(`Bairro é obrigatório para o endereço: ${address.street}`);
          continue;
        }

        const addressData = {
          street: address.street,
          number: address.number || '',
          complement: address.complement || '',
          district: address.district,
          neighborhood: address.neighborhood || address.district,
          city: address.city,
          state: address.state,
          zipCode: address.zipCode,
          country: address.country || 'Brasil',
          isDefault: address.isDefault || false,
          type: address.type || 'main',
        };

        await entityAddressService.createEntityAddress(entityId, addressData);
      } catch (error: any) {
        console.error('Erro ao criar endereço:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Erro desconhecido ao criar endereço';
        toast.error(`Erro ao criar endereço ${address.street}: ${errorMessage}`);
      }
    }
  };

  // Função para sincronizar endereços durante a edição
  const syncEntityAddresses = async (entityId: string, newAddresses: (Address & { type?: AddressType })[]) => {
    if (!entityId) {
      console.error('ID da entidade é obrigatório para sincronizar endereços');
      return;
    }

    try {
      let existingAddresses: Address[] = [];
      try {
        existingAddresses = await entityAddressService.getEntityAddresses(entityId);
      } catch (error) {
        console.warn('Erro ao buscar endereços existentes, assumindo lista vazia:', error);
        existingAddresses = [];
      }

      const addressesToSync = Array.isArray(newAddresses) ? newAddresses : [];

      // Identificar endereços para criar, atualizar e remover
      const addressesToCreate = addressesToSync.filter(addr => addr.id && addr.id.startsWith('temp-'));
      const addressesToUpdate = addressesToSync.filter(addr => addr.id && !addr.id.startsWith('temp-') &&
        existingAddresses.some(existing => existing.id === addr.id));
      const addressesToRemove = existingAddresses.filter(existing =>
        !addressesToSync.some(newAddr => newAddr.id === existing.id));

      // Remover endereços
      for (const addressToRemove of addressesToRemove) {
        try {
          await entityAddressService.deleteEntityAddress(entityId, addressToRemove.id);
        } catch (error) {
          console.error('Erro ao remover endereço:', error);
          toast.error(`Erro ao remover endereço: ${addressToRemove.street}`);
        }
      }

      // Criar novos endereços
      for (const addressToCreate of addressesToCreate) {
        try {
          const addressData = {
            street: addressToCreate.street,
            number: addressToCreate.number || '',
            complement: addressToCreate.complement || '',
            district: addressToCreate.district || '',
            city: addressToCreate.city,
            state: addressToCreate.state,
            zipCode: addressToCreate.zipCode,
            country: addressToCreate.country || 'Brasil',
            isDefault: addressToCreate.isDefault || false,
            type: addressToCreate.type || 'main',
          };

          await entityAddressService.createEntityAddress(entityId, addressData);
        } catch (error: any) {
          console.error('Erro ao criar endereço:', error);
          toast.error(`Erro ao criar endereço ${addressToCreate.street}: ${error.response?.data?.message || error.message}`);
        }
      }

      // Atualizar endereços existentes
      for (const addressToUpdate of addressesToUpdate) {
        try {
          const addressData = {
            street: addressToUpdate.street,
            number: addressToUpdate.number || '',
            complement: addressToUpdate.complement || '',
            district: addressToUpdate.district || '',
            city: addressToUpdate.city,
            state: addressToUpdate.state,
            zipCode: addressToUpdate.zipCode,
            country: addressToUpdate.country || 'Brasil',
            isDefault: addressToUpdate.isDefault || false,
            type: addressToUpdate.type || 'main',
          };

          await entityAddressService.updateEntityAddress(entityId, addressToUpdate.id, addressData);
        } catch (error: any) {
          console.error('Erro ao atualizar endereço:', error);
          toast.error(`Erro ao atualizar endereço ${addressToUpdate.street}: ${error.response?.data?.message || error.message}`);
        }
      }

    } catch (error: any) {
      console.error('Erro ao sincronizar endereços:', error);
      toast.error('Erro ao sincronizar endereços. Alguns endereços podem não ter sido salvos.');
    }
  };

  const isLoading = isLoadingEntity || isLoadingAddresses;
  const isDirty = form.formState.isDirty || addresses.length > 0;

  return (
    <EntityFormLayout
      title={isEditMode ? 'Editar Cliente' : 'Novo Cliente'}
      entityType="cliente"
      mode={isEditMode ? 'edit' : 'create'}
      entityName={entity?.name}
      isLoading={isLoading}
      isSaving={isSaving}
      isDirty={isDirty}
      onSave={handleSave}
      canSave={form.formState.isValid}
    >
      <Form {...form}>
        <form className="space-y-6">
          {/* Campos do formulário brasileiro de entidades */}
          <BrazilianEntityFormFields
            form={form}
            selectedPersonType={selectedPersonType}
            defaultEntityType="customer"
          />

          {/* Seção de endereços */}
          <EntityAddressSection
            entityId={id}
            addresses={addresses}
            onAddressesChange={setAddresses}
            isEditMode={isEditMode}
          />
        </form>
      </Form>
    </EntityFormLayout>
  );
};

export default CustomerFormPage;
