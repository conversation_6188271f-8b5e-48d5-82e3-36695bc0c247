import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { EntityFormLayout } from '@/components/forms/EntityFormLayout';
import { BrazilianEntityViewFields } from '@/components/entities/BrazilianEntityViewFields';
import { EntityAddressViewSection } from '@/components/address/EntityAddressViewSection';
import { useEntity } from '@/hooks/api/useEntities';
import { toast } from 'sonner';

const CustomerViewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  // Hook de API para buscar dados da entidade
  const { data: entity, isLoading: isLoadingEntity, error: entityError } = useEntity(id || '');

  const handleEdit = () => {
    if (id) {
      navigate(`/customers/editar/${id}`);
    }
  };

  const handleCancel = () => {
    navigate('/customers');
  };

  // Verificar se há erro na busca da entidade
  if (entityError) {
    toast.error('Erro ao carregar dados do cliente');
    navigate('/customers');
    return null;
  }

  // Verificar se o ID é válido
  if (!id) {
    toast.error('ID do cliente não fornecido');
    navigate('/customers');
    return null;
  }

  return (
    <EntityFormLayout
      title="Visualizar Cliente"
      entityType="cliente"
      mode="view"
      entityName={entity?.name}
      isLoading={isLoadingEntity}
      onEdit={handleEdit}
      onCancel={handleCancel}
    >
      {entity && (
        <div className="space-y-6">
          {/* Campos do formulário brasileiro de entidades em modo visualização */}
          <BrazilianEntityViewFields entity={entity} />

          {/* Seção de endereços em modo visualização */}
          <EntityAddressViewSection entityId={id} />
        </div>
      )}
    </EntityFormLayout>
  );
};

export default CustomerViewPage;
