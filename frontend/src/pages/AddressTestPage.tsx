import React, { useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { EntityAddressManager } from '@/components/address/EntityAddressManager';
import { AddressSyncDebugPanel } from '@/components/debug/AddressSyncDebugPanel';
import { useEntities } from '@/hooks/api/useEntities';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';

const AddressTestPage: React.FC = () => {
  const [selectedEntityId, setSelectedEntityId] = useState<string>('');
  
  // Carregar entidades para teste
  const { data: entitiesData, isLoading: loadingEntities } = useEntities(1, 50);
  const entities = entitiesData?.data || [];

  return (
    <Layout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Teste - Gerenciamento de Endereços de Entidades</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Seleção de Entidade</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="entity-select">Selecione uma entidade para testar os endereços:</Label>
              <Select value={selectedEntityId} onValueChange={setSelectedEntityId}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma entidade..." />
                </SelectTrigger>
                <SelectContent>
                  {loadingEntities ? (
                    <SelectItem value="" disabled>Carregando entidades...</SelectItem>
                  ) : entities.length === 0 ? (
                    <SelectItem value="" disabled>Nenhuma entidade encontrada</SelectItem>
                  ) : (
                    entities.map((entity) => (
                      <SelectItem key={entity.id} value={entity.id}>
                        {entity.name} ({entity.type})
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            {entities.length === 0 && !loadingEntities && (
              <div className="p-4 border border-dashed rounded-md text-center">
                <p className="text-sm text-muted-foreground">
                  Nenhuma entidade encontrada. Crie clientes ou fornecedores primeiro para testar o gerenciamento de endereços.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {selectedEntityId && (
          <>
            <Card>
              <CardHeader>
                <CardTitle>
                  Gerenciamento de Endereços - {entities.find(e => e.id === selectedEntityId)?.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <EntityAddressManager entityId={selectedEntityId} />
              </CardContent>
            </Card>

            <AddressSyncDebugPanel
              entityId={selectedEntityId}
              entityName={entities.find(e => e.id === selectedEntityId)?.name}
            />
          </>
        )}

        {!selectedEntityId && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center p-6 border border-dashed rounded-md">
                <p className="text-sm text-muted-foreground">
                  Selecione uma entidade acima para gerenciar seus endereços.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Instruções de Teste</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Cenários para testar:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li><strong>Problema 1 - Carregamento na edição:</strong> Selecione uma entidade que já possui endereços cadastrados. Os endereços devem aparecer automaticamente.</li>
                <li><strong>Problema 2 - Adição posterior:</strong> Selecione uma entidade existente e tente adicionar um novo endereço. O endereço deve ser salvo no banco de dados.</li>
                <li><strong>Teste de sincronização (CORRIGIDO):</strong> Adicione um endereço, saia da página e retorne. O endereço deve aparecer imediatamente sem necessidade de refresh (F5).</li>
                <li><strong>Teste de CRUD completo:</strong> Teste criar, visualizar, editar e excluir endereços.</li>
                <li><strong>Teste de endereço padrão:</strong> Defina diferentes endereços como padrão e verifique se apenas um fica marcado.</li>
                <li><strong>Teste de navegação:</strong> Navegue entre diferentes entidades e verifique se os endereços corretos são exibidos para cada uma.</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Funcionalidades implementadas:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>✅ Serviço específico para endereços de entidade (entityAddressService)</li>
                <li>✅ Hooks para gerenciar endereços de entidade (useEntityAddresses)</li>
                <li>✅ Carregamento automático de endereços na edição</li>
                <li>✅ Componente dedicado para gerenciar endereços (EntityAddressManager)</li>
                <li>✅ Correção na constraint do banco de dados</li>
                <li>✅ Integração com formulários de cliente e fornecedor</li>
                <li>🆕 <strong>Correção de sincronização de dados:</strong> Cache otimizado, invalidação melhorada, atualizações otimistas</li>
                <li>🆕 <strong>Refresh automático:</strong> Dados são atualizados automaticamente ao navegar entre páginas</li>
                <li>🆕 <strong>Configuração de cache melhorada:</strong> staleTime reduzido, refetch automático habilitado</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default AddressTestPage;
