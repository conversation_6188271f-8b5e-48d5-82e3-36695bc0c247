import React, { useState } from 'react';
import { Layout } from "@/components/layout/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AddressSection } from "@/components/address";
import { Address, AddressType, CreateAddressRequest } from "@/types/api";
import { toast } from 'sonner';

const AddressDemoPage: React.FC = () => {
  const [addresses, setAddresses] = useState<(Address & { type?: AddressType })[]>([
    {
      id: '1',
      street: 'Avenida Paulista',
      number: '1000',
      complement: 'Conjunto 101',
      district: 'Bela Vista',
      city: 'São Paulo',
      state: 'SP',
      zipCode: '01310-100',
      country: 'Brasil',
      type: 'main',
      isDefault: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      street: 'R<PERSON>',
      number: '500',
      complement: '',
      district: 'Jardins',
      city: 'São Paulo',
      state: 'SP',
      zipCode: '01426-001',
      country: 'Brasil',
      type: 'billing',
      isDefault: false,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ]);

  const [entityData, setEntityData] = useState({
    name: 'Empresa Exemplo Ltda',
    email: '<EMAIL>',
    phone: '(11) 99999-9999',
    contact: 'João Silva'
  });

  // Funções para gerenciar endereços
  const handleAddAddress = (addressData: CreateAddressRequest) => {
    const newAddress: Address & { type?: AddressType } = {
      id: `temp-${Date.now()}`,
      street: addressData.street,
      number: addressData.number,
      complement: addressData.complement,
      district: addressData.district,
      city: addressData.city,
      state: addressData.state,
      zipCode: addressData.zipCode,
      country: addressData.country || 'Brasil',
      type: addressData.type,
      isDefault: addressData.isDefault || false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Se é padrão, remove padrão dos outros
    if (newAddress.isDefault) {
      setAddresses(prev => prev.map(addr => ({ ...addr, isDefault: false })));
    }

    setAddresses(prev => [...prev, newAddress]);
    toast.success('Endereço adicionado com sucesso!');
  };

  const handleUpdateAddress = (id: string, updates: Partial<Address>) => {
    setAddresses(prev => prev.map(addr => 
      addr.id === id ? { ...addr, ...updates } : addr
    ));
    toast.success('Endereço atualizado com sucesso!');
  };

  const handleDeleteAddress = (id: string) => {
    setAddresses(prev => prev.filter(addr => addr.id !== id));
    toast.success('Endereço removido com sucesso!');
  };

  const handleSetDefaultAddress = (id: string) => {
    setAddresses(prev => prev.map(addr => ({
      ...addr,
      isDefault: addr.id === id
    })));
    toast.success('Endereço padrão atualizado!');
  };

  const handleSave = () => {
    console.log('Dados da entidade:', entityData);
    console.log('Endereços:', addresses);
    toast.success('Dados salvos com sucesso!');
  };

  return (
    <Layout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Demonstração - Nova Arquitetura de Endereços</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Cadastro de Entidade</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Campos básicos da entidade */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Nome da Empresa</Label>
                <Input
                  id="name"
                  value={entityData.name}
                  onChange={(e) => setEntityData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Nome da empresa"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={entityData.email}
                    onChange={(e) => setEntityData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <Label htmlFor="phone">Telefone</Label>
                  <Input
                    id="phone"
                    value={entityData.phone}
                    onChange={(e) => setEntityData(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="(11) 99999-9999"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="contact">Nome do Contato</Label>
                <Input
                  id="contact"
                  value={entityData.contact}
                  onChange={(e) => setEntityData(prev => ({ ...prev, contact: e.target.value }))}
                  placeholder="Nome da pessoa de contato"
                />
              </div>
            </div>

            {/* Seção de Endereços */}
            <AddressSection
              addresses={addresses}
              onAddAddress={handleAddAddress}
              onUpdateAddress={handleUpdateAddress}
              onDeleteAddress={handleDeleteAddress}
              onSetDefaultAddress={handleSetDefaultAddress}
            />

            {/* Botões de ação */}
            <div className="flex justify-end gap-3 pt-6">
              <Button variant="outline">
                Cancelar
              </Button>
              <Button onClick={handleSave}>
                Salvar Entidade
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Debug Info */}
        <Card>
          <CardHeader>
            <CardTitle>Debug - Estado Atual</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Dados da Entidade:</h4>
                <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
                  {JSON.stringify(entityData, null, 2)}
                </pre>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Endereços ({addresses.length}):</h4>
                <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm overflow-auto">
                  {JSON.stringify(addresses, null, 2)}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default AddressDemoPage;
