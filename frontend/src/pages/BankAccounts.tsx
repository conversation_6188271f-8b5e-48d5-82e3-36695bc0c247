import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Layout } from '@/components/layout/Layout';
import { useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Plus, Search, CreditCard, Trash2, Filter, ArrowDownUp, Edit, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import GlassCard from '@/components/ui-custom/GlassCard';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import BankAccountModal from '@/components/bank-accounts/BankAccountModal';
import { useBankAccounts, useDeleteBankAccount } from '@/hooks/api/useBankAccounts';
import { useAuth } from '@/contexts/AuthContext';
import { formatCurrency, formatDate } from '@/lib/formatters';
import { bankAccountService } from '@/services/api';
import { toast } from 'sonner';
import { invalidateBankAccountsCache, refetchBankAccounts } from '@/utils/cacheUtils';


const BankAccounts = () => {
  const location = useLocation();
  const { activeCompanyId } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentAccount, setCurrentAccount] = useState<any>(null);
  const [page, setPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const limit = 10;

  // Fetch bank accounts from API
  const { data: bankAccountsData, isLoading, isError, refetch } = useBankAccounts(page, limit, {
    search: searchTerm,
    companyId: activeCompanyId // Usar apenas o activeCompanyId do contexto de autenticação
  });

  // Não precisamos mais armazenar o activeCompanyId como currentCompanyId
  // Agora estamos usando o header X-Company-ID no interceptor do Axios

  // Logs removidos por segurança

  // Forçar revalidação quando o activeCompanyId mudar
  useEffect(() => {
    if (activeCompanyId) {
      // Aguardar um pouco mais para garantir estabilidade
      const timeoutId = setTimeout(() => {
        refetch();
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [activeCompanyId, refetch]);

  // Remover revalidação periódica para evitar conflitos
  // useEffect(() => {
  //   // Revalidar a cada 30 segundos para garantir dados atualizados
  //   const intervalId = setInterval(() => {
  //     console.log('BankAccounts - revalidando dados periodicamente');
  //     refetch();
  //   }, 30000); // Aumentado para 30 segundos

  //   // Limpar o intervalo quando o componente for desmontado
  //   return () => clearInterval(intervalId);
  // }, [refetch]);

  const deleteAccountMutation = useDeleteBankAccount();

  const handleAddAccount = () => {
    setCurrentAccount(null);
    setIsModalOpen(true);
  };

  const handleEditAccount = (account: any) => {
    setCurrentAccount(account);
    setIsModalOpen(true);
  };

  const handleDeleteAccount = async (id: string) => {
    if (confirm('Tem certeza que deseja excluir esta conta bancária?')) {
      try {
        // Usar diretamente o serviço para garantir que a exclusão seja realizada
        await bankAccountService.deleteBankAccount(id);

        // Mostrar mensagem de sucesso
        toast.success('Conta bancária excluída com sucesso!');

        // Invalidar o cache e forçar revalidação dos dados
        invalidateBankAccountsCache();

        // Forçar revalidação dos dados após um pequeno atraso
        setTimeout(() => {
          refetchBankAccounts();
          refetch();
        }, 500);
      } catch (error) {
        console.error('Erro ao excluir conta bancária:', error);
        toast.error('Erro ao excluir conta bancária. Por favor, tente novamente.');
      }
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <Layout location={location}>
      <div className="container p-4 mx-auto">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold mb-1">Contas Bancárias</h1>
            <p className="text-muted-foreground">Gerencie suas contas bancárias e monitore seus saldos.</p>
          </div>

          <div className="flex items-center gap-2 mt-4 md:mt-0">
            <Button variant="outline" className="flex items-center gap-2" onClick={() => setShowFilters(!showFilters)}>
              <Filter className="h-4 w-4" />
              <span className="text-sm font-medium">Filtros</span>
            </Button>

            <Button onClick={handleAddAccount} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              <span className="text-sm font-medium">Nova Conta</span>
            </Button>
          </div>
        </div>

        {/* Seção de Filtros Condicional */}
        {showFilters && (
          <GlassCard className="mb-8 p-4">
            <h3 className="text-lg font-semibold mb-4">Filtros Avançados</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Exemplo de Input de Filtro (Substituir/Adicionar conforme necessário) */}
              <div>
                <label htmlFor="filter-type" className="block text-sm font-medium text-muted-foreground mb-1">Tipo de Conta</label>
                <Input id="filter-type" placeholder="Ex: Corrente" />
              </div>
              <div>
                <label htmlFor="filter-bank" className="block text-sm font-medium text-muted-foreground mb-1">Banco</label>
                <Input id="filter-bank" placeholder="Ex: Banco do Brasil" />
              </div>
              {/* Adicionar mais campos de filtro aqui */}
            </div>
            <div className="flex justify-end mt-4">
              <Button variant="ghost" onClick={() => setShowFilters(false)}>Limpar</Button>
              <Button className="ml-2">Aplicar Filtros</Button>
            </div>
          </GlassCard>
        )}

        <GlassCard>
          <Tabs defaultValue="all">
            <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
              <TabsList>
                <TabsTrigger value="all">Todas as Contas</TabsTrigger>
                <TabsTrigger value="checking">Corrente</TabsTrigger>
                <TabsTrigger value="savings">Poupança</TabsTrigger>
                <TabsTrigger value="investment">Investimento</TabsTrigger>
              </TabsList>

              <div className="relative mt-4 md:mt-0">
                <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Buscar contas..."
                  className="pl-9 w-full md:w-64"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <TabsContent value="all" className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Banco / Conta</TableHead>
                    <TableHead>Número</TableHead>
                    <TableHead>Tipo</TableHead>
                    <TableHead className="text-right">Saldo Atual</TableHead>
                    <TableHead className="text-right">Saldo Inicial</TableHead>
                    <TableHead className="text-center">Atualizado em</TableHead>
                    <TableHead className="text-center">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        <div className="flex justify-center items-center">
                          <Loader2 className="h-6 w-6 animate-spin text-primary" />
                          <span className="ml-2">Carregando contas...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : isError ? (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center text-destructive">
                        Erro ao carregar contas bancárias. Tente novamente mais tarde.
                      </TableCell>
                    </TableRow>
                  ) : bankAccountsData?.data?.length ? (
                    bankAccountsData.data.map((account) => (
                      <TableRow key={account.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <div className="flex-shrink-0 h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                              {account.bank?.code?.charAt(0) || account.name.charAt(0)}
                            </div>
                            <span>{account.bank?.name || account.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{account.accountNumber}</TableCell>
                        <TableCell>
                          <span className={cn(
                            "px-2 py-1 rounded-full text-xs font-medium",
                            account.type === "checking" && "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
                            account.type === "savings" && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
                            account.type === "investment" && "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
                          )}>
                            {account.type === "checking" ? "Corrente" :
                             account.type === "savings" ? "Poupança" :
                             account.type === "investment" ? "Investimento" :
                             account.type === "cash" ? "Dinheiro" :
                             account.type === "other" ? "Outro" : account.type}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(account.currentBalance || 0)}
                        </TableCell>
                        <TableCell className="text-right">
                          {account.initialBalance !== null && account.initialBalance !== undefined ? formatCurrency(account.initialBalance) : '-'}
                        </TableCell>
                        <TableCell className="text-center text-sm text-muted-foreground">
                          {formatDate(new Date(account.updatedAt || new Date()))}
                        </TableCell>
                        <TableCell>
                          <div className="flex justify-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditAccount(account)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteAccount(account.id)}
                              className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        Nenhuma conta bancária encontrada.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TabsContent>

            <TabsContent value="checking">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Banco / Conta</TableHead>
                    <TableHead>Número</TableHead>
                    <TableHead>Tipo</TableHead>
                    <TableHead className="text-right">Saldo Atual</TableHead>
                    <TableHead className="text-right">Saldo Inicial</TableHead>
                    <TableHead className="text-center">Atualizado em</TableHead>
                    <TableHead className="text-center">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        <div className="flex justify-center items-center">
                          <Loader2 className="h-6 w-6 animate-spin text-primary" />
                          <span className="ml-2">Carregando contas...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : isError ? (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center text-destructive">
                        Erro ao carregar contas bancárias. Tente novamente mais tarde.
                      </TableCell>
                    </TableRow>
                  ) : bankAccountsData?.data?.filter(a => a.type === "checking")?.length ? (
                    bankAccountsData.data.filter(a => a.type === "checking").map((account) => (
                      <TableRow key={account.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <div className="flex-shrink-0 h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                              {account.bank?.code?.charAt(0) || account.name.charAt(0)}
                            </div>
                            <span>{account.bank?.name || account.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{account.accountNumber}</TableCell>
                        <TableCell>
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                            Corrente
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(account.currentBalance || 0)}
                        </TableCell>
                        <TableCell className="text-right">
                          {account.initialBalance !== null && account.initialBalance !== undefined ? formatCurrency(account.initialBalance) : '-'}
                        </TableCell>
                        <TableCell className="text-center text-sm text-muted-foreground">
                          {formatDate(new Date(account.updatedAt || new Date()))}
                        </TableCell>
                        <TableCell>
                          <div className="flex justify-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditAccount(account)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteAccount(account.id)}
                              className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        Nenhuma conta corrente encontrada.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TabsContent>

            <TabsContent value="savings">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Banco / Conta</TableHead>
                    <TableHead>Número</TableHead>
                    <TableHead>Tipo</TableHead>
                    <TableHead className="text-right">Saldo Atual</TableHead>
                    <TableHead className="text-right">Saldo Inicial</TableHead>
                    <TableHead className="text-center">Atualizado em</TableHead>
                    <TableHead className="text-center">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        <div className="flex justify-center items-center">
                          <Loader2 className="h-6 w-6 animate-spin text-primary" />
                          <span className="ml-2">Carregando contas...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : isError ? (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center text-destructive">
                        Erro ao carregar contas bancárias. Tente novamente mais tarde.
                      </TableCell>
                    </TableRow>
                  ) : bankAccountsData?.data?.filter(a => a.type === "savings")?.length ? (
                    bankAccountsData.data.filter(a => a.type === "savings").map((account) => (
                      <TableRow key={account.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <div className="flex-shrink-0 h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                              {account.bank?.code?.charAt(0) || account.name.charAt(0)}
                            </div>
                            <span>{account.bank?.name || account.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{account.accountNumber}</TableCell>
                        <TableCell>
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                            Poupança
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(account.currentBalance || 0)}
                        </TableCell>
                        <TableCell className="text-right">
                          {account.initialBalance !== null && account.initialBalance !== undefined ? formatCurrency(account.initialBalance) : '-'}
                        </TableCell>
                        <TableCell className="text-center text-sm text-muted-foreground">
                          {formatDate(new Date(account.updatedAt || new Date()))}
                        </TableCell>
                        <TableCell>
                          <div className="flex justify-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditAccount(account)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteAccount(account.id)}
                              className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        Nenhuma conta poupança encontrada.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TabsContent>

            <TabsContent value="investment">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Banco / Conta</TableHead>
                    <TableHead>Número</TableHead>
                    <TableHead>Tipo</TableHead>
                    <TableHead className="text-right">Saldo Atual</TableHead>
                    <TableHead className="text-right">Saldo Inicial</TableHead>
                    <TableHead className="text-center">Atualizado em</TableHead>
                    <TableHead className="text-center">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        <div className="flex justify-center items-center">
                          <Loader2 className="h-6 w-6 animate-spin text-primary" />
                          <span className="ml-2">Carregando contas...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : isError ? (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center text-destructive">
                        Erro ao carregar contas bancárias. Tente novamente mais tarde.
                      </TableCell>
                    </TableRow>
                  ) : bankAccountsData?.data?.filter(a => a.type === "investment")?.length ? (
                    bankAccountsData.data.filter(a => a.type === "investment").map((account) => (
                      <TableRow key={account.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <div className="flex-shrink-0 h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                              {account.bank?.code?.charAt(0) || account.name.charAt(0)}
                            </div>
                            <span>{account.bank?.name || account.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{account.accountNumber}</TableCell>
                        <TableCell>
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                            Investimento
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(account.currentBalance || 0)}
                        </TableCell>
                        <TableCell className="text-right">
                          {account.initialBalance !== null && account.initialBalance !== undefined ? formatCurrency(account.initialBalance) : '-'}
                        </TableCell>
                        <TableCell className="text-center text-sm text-muted-foreground">
                          {formatDate(new Date(account.updatedAt || new Date()))}
                        </TableCell>
                        <TableCell>
                          <div className="flex justify-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditAccount(account)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteAccount(account.id)}
                              className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        Nenhuma conta de investimento encontrada.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TabsContent>
          </Tabs>
        </GlassCard>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
          <GlassCard className="animate-scale-in" style={{ animationDelay: "0.2s" }}>
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-medium">Atividade Recente</h3>
              <Button variant="outline" size="sm" className="text-xs">Ver Todas</Button>
            </div>
            <div className="space-y-3">
              <div className="text-center py-8 text-muted-foreground">
                <div className="flex flex-col items-center gap-2">
                  <ArrowDownUp className="h-8 w-8 text-muted-foreground/50" />
                  <p className="text-sm">Nenhuma atividade recente</p>
                  <p className="text-xs">As transações aparecerão aqui quando forem realizadas</p>
                </div>
              </div>
            </div>
          </GlassCard>

          <GlassCard className="animate-scale-in" style={{ animationDelay: "0.4s" }}>
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-medium">Distribuição de Saldo</h3>
              <ArrowDownUp className="h-5 w-5 text-muted-foreground" />
            </div>
            <div className="space-y-3">
              {isLoading ? (
                <div className="flex justify-center items-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin text-primary" />
                </div>
              ) : isError ? (
                <div className="p-3 bg-destructive/10 text-destructive rounded-lg border border-destructive">
                  <p className="text-sm">Erro ao carregar contas bancárias</p>
                </div>
              ) : bankAccountsData?.data && bankAccountsData.data.length > 0 ? (
                bankAccountsData.data.map((account) => (
                  <div key={account.id} className="p-3 bg-secondary/50 dark:bg-secondary/25 rounded-lg border border-border">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-sm font-medium">{account.name}</p>
                      <div className={cn(
                        "h-2 w-2 rounded-full",
                        account.type === "checking" && "bg-blue-500",
                        account.type === "savings" && "bg-green-500",
                        account.type === "investment" && "bg-purple-500"
                      )}></div>
                    </div>
                    <p className="text-lg font-semibold">{formatCurrency(account.currentBalance || 0)}</p>
                    <p className="text-xs text-muted-foreground">{account.accountNumber || 'Sem número'}</p>
                  </div>
                ))
              ) : (
                <div className="p-3 bg-secondary/50 dark:bg-secondary/25 rounded-lg border border-border">
                  <p className="text-sm text-center">Nenhuma conta bancária encontrada</p>
                </div>
              )}
            </div>
          </GlassCard>
        </div>
      </div>

      {isModalOpen && (
        <BankAccountModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          account={currentAccount}
        />
      )}
    </Layout>
  );
};

export default BankAccounts;
