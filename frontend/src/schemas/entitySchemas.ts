import { z } from 'zod';
import { PersonType } from '@/types/api';
import { isValidCPF, isValidCNPJ } from '@/utils/brazilianDocuments';

/**
 * Validação flexível de URL que aceita diferentes formatos
 * @param url - URL para validar
 * @returns boolean indicando se a URL é válida
 */
function isValidFlexibleUrl(url: string): boolean {
  if (!url || url.trim() === '') return true; // Campo opcional

  try {
    // Remove espaços em branco
    const cleanUrl = url.trim();

    // Verifica se contém espaços (inválido)
    if (cleanUrl.includes(' ')) return false;

    // Verifica se é apenas texto sem estrutura de domínio
    if (!cleanUrl.includes('.')) return false;

    // Tenta criar URL com protocolo se não tiver
    let testUrl = cleanUrl;
    if (!cleanUrl.match(/^[a-zA-Z][a-zA-Z0-9+.-]*:/)) {
      testUrl = 'https://' + cleanUrl;
    }

    const urlObj = new URL(testUrl);

    // Verifica se tem domínio válido
    const domain = urlObj.hostname;
    if (!domain || domain.length === 0) return false;

    // Verifica se tem pelo menos um ponto (domínio.extensão)
    if (!domain.includes('.')) return false;

    // Verifica se o domínio não é apenas pontos
    if (domain.replace(/\./g, '').length === 0) return false;

    // Verifica se não termina ou começa com ponto
    if (domain.startsWith('.') || domain.endsWith('.')) return false;

    // Verifica se não tem pontos consecutivos
    if (domain.includes('..')) return false;

    return true;
  } catch {
    return false;
  }
}

/**
 * Configuração de campos obrigatórios
 * Esta configuração pode ser futuramente movida para o banco de dados
 * para permitir configuração dinâmica por administradores
 */
interface RequiredFieldsConfig {
  name: boolean;
  email: boolean;
  mobile: boolean;
  stateRegistration: boolean;
  // Campos condicionais baseados no tipo de pessoa
  cnpj: boolean; // Para pessoa jurídica
  cpf: boolean;  // Para pessoa física
}

/**
 * Configuração padrão de campos obrigatórios
 * Baseada nos requisitos de negócio atuais
 */
const DEFAULT_REQUIRED_FIELDS: RequiredFieldsConfig = {
  name: true,              // Nome/Razão Social sempre obrigatório
  email: false,            // Email opcional por enquanto
  mobile: false,           // Celular opcional por enquanto
  stateRegistration: false, // Inscrição Estadual opcional por enquanto
  cnpj: true,              // CNPJ obrigatório para pessoa jurídica
  cpf: true,               // CPF obrigatório para pessoa física
};

/**
 * Schema de validação dinâmico baseado no tipo de pessoa
 * Aplica validações apenas nos campos realmente obrigatórios
 */
export const createEntitySchema = (personType?: PersonType, requiredFields: RequiredFieldsConfig = DEFAULT_REQUIRED_FIELDS) => {
  const baseSchema = {
    // Nome/Razão Social - sempre obrigatório
    name: z.string().min(2, { message: "Nome deve ter pelo menos 2 caracteres" }),

    // Campos de classificação - sempre obrigatórios
    entityType: z.enum(['customer', 'supplier', 'both'] as const),
    personType: z.enum(['individual', 'company'] as const),

    // Email - validação condicional
    email: requiredFields.email
      ? z.string().min(1, { message: "Email é obrigatório" }).email({ message: "Email inválido" })
      : z.string().email({ message: "Email inválido" }).optional().or(z.literal("")),

    // Telefones - validação condicional
    phone: z.string().optional(),
    mobile: requiredFields.mobile
      ? z.string().min(1, { message: "Celular é obrigatório" })
      : z.string().optional(),

    // Outros campos sempre opcionais
    website: z.string()
      .optional()
      .or(z.literal(""))
      .refine((url) => isValidFlexibleUrl(url || ""), {
        message: "URL inválida. Use formato como: www.exemplo.com ou https://www.exemplo.com"
      }),
    contact: z.string().optional(),
    status: z.enum(['active', 'inactive', 'suspended', 'blocked'] as const).optional(),

    // Campos numéricos - tratamento especial para valores vazios/NaN/strings
    creditLimit: z.union([
      z.number().min(0, { message: "Limite de crédito deve ser maior ou igual a 0" }),
      z.string().transform((val) => {
        if (val === '' || val === null || val === undefined) return undefined;
        const num = parseFloat(val);
        return isNaN(num) ? undefined : num;
      }),
      z.nan().transform(() => undefined),
      z.undefined(),
      z.null().transform(() => undefined)
    ]).optional(),

    paymentTermDays: z.union([
      z.number().min(1, { message: "Prazo deve ser pelo menos 1 dia" }).max(365, { message: "Prazo não pode exceder 365 dias" }),
      z.string().transform((val) => {
        if (val === '' || val === null || val === undefined) return undefined;
        const num = parseInt(val);
        return isNaN(num) ? undefined : num;
      }),
      z.nan().transform(() => undefined),
      z.undefined(),
      z.null().transform(() => undefined)
    ]).optional(),

    discountPercentage: z.union([
      z.number().min(0, { message: "Desconto deve ser maior ou igual a 0%" }).max(100, { message: "Desconto não pode exceder 100%" }),
      z.string().transform((val) => {
        if (val === '' || val === null || val === undefined) return undefined;
        const num = parseFloat(val);
        return isNaN(num) ? undefined : num;
      }),
      z.nan().transform(() => undefined),
      z.undefined(),
      z.null().transform(() => undefined)
    ]).optional(),

    notes: z.string().optional(),
  };

  if (personType === 'individual') {
    return z.object({
      ...baseSchema,
      // CPF - obrigatório para pessoa física
      cpf: requiredFields.cpf
        ? z.string().min(1, { message: "CPF é obrigatório" })
            .refine((val) => isValidCPF(val), { message: "CPF inválido" })
        : z.string().optional()
            .refine((val) => !val || isValidCPF(val), { message: "CPF inválido" }),

      // Campos opcionais para pessoa física
      rg: z.string().optional(),
      rgIssuer: z.string().optional(),
      taxRegime: z.enum(['individual'] as const).optional(),
    });
  } else if (personType === 'company') {
    return z.object({
      ...baseSchema,
      // Nome fantasia - sempre opcional
      tradeName: z.string().optional(),

      // CNPJ - obrigatório para pessoa jurídica
      cnpj: requiredFields.cnpj
        ? z.string().min(1, { message: "CNPJ é obrigatório" })
            .refine((val) => isValidCNPJ(val), { message: "CNPJ inválido" })
        : z.string().optional()
            .refine((val) => !val || isValidCNPJ(val), { message: "CNPJ inválido" }),

      // Inscrição Estadual - validação condicional
      stateRegistration: requiredFields.stateRegistration
        ? z.string().min(1, { message: "Inscrição Estadual é obrigatória" })
        : z.string().optional(),

      // Campos sempre opcionais para pessoa jurídica
      municipalRegistration: z.string().optional(),
      taxRegime: z.enum(['simple_national', 'presumed_profit', 'real_profit'] as const).optional(),
      icmsTaxpayer: z.boolean().optional(),
      simpleNational: z.boolean().optional(),
      withholdTaxes: z.boolean().optional(),
      sendNfe: z.boolean().optional(),
      nfeEmail: z.string().email({ message: "Email inválido" }).optional().or(z.literal("")),

      // Dados bancários - sempre opcionais
      bankCode: z.string().optional(),
      bankName: z.string().optional(),
      agency: z.string().optional(),
      account: z.string().optional(),
      accountDigit: z.string().optional(),
      pixKey: z.string().optional(),
    });
  }

  return z.object(baseSchema);
};

export type EntityFormValues = z.infer<ReturnType<typeof createEntitySchema>>;

/**
 * Schema com configuração padrão (campos mínimos obrigatórios)
 * Para uso em formulários que precisam de validação mais flexível
 */
export const createMinimalEntitySchema = (personType?: PersonType) => {
  const minimalRequiredFields: RequiredFieldsConfig = {
    name: true,              // Nome sempre obrigatório
    email: false,            // Email opcional
    mobile: false,           // Celular opcional
    stateRegistration: false, // IE opcional
    cnpj: true,              // CNPJ obrigatório para PJ
    cpf: true,               // CPF obrigatório para PF
  };

  return createEntitySchema(personType, minimalRequiredFields);
};

/**
 * Schema com configuração completa (mais campos obrigatórios)
 * Para uso em formulários que exigem mais informações
 */
export const createCompleteEntitySchema = (personType?: PersonType) => {
  const completeRequiredFields: RequiredFieldsConfig = {
    name: true,              // Nome sempre obrigatório
    email: true,             // Email obrigatório
    mobile: true,            // Celular obrigatório
    stateRegistration: true, // IE obrigatória
    cnpj: true,              // CNPJ obrigatório para PJ
    cpf: true,               // CPF obrigatório para PF
  };

  return createEntitySchema(personType, completeRequiredFields);
};

/**
 * Cria schema baseado em configuração dinâmica de entidade
 * Integra com o sistema de configurações administrativas
 */
export const createEntitySchemaFromConfig = (
  personType?: PersonType,
  entityConfig?: any // EntityValidationConfig from API
) => {
  if (!entityConfig) {
    // Fallback para configuração mínima se não houver configuração dinâmica
    return createMinimalEntitySchema(personType);
  }

  // Converter configuração da API para RequiredFieldsConfig
  const configFromAPI: RequiredFieldsConfig = {
    name: true, // Nome sempre obrigatório
    email: false,
    mobile: false,
    stateRegistration: false,
    cnpj: true, // CNPJ obrigatório para PJ
    cpf: true,  // CPF obrigatório para PF
  };

  // Aplicar configurações dinâmicas
  entityConfig.fields?.forEach((field: any) => {
    if (field.appliesTo.includes(personType) || field.appliesTo.includes('both')) {
      if (field.fieldName in configFromAPI) {
        (configFromAPI as any)[field.fieldName] = field.isRequired;
      }
    }
  });

  return createEntitySchema(personType, configFromAPI);
};

/**
 * Hook para usar schema dinâmico baseado em configuração ativa
 */
export const useEntitySchemaWithConfig = (personType?: PersonType) => {
  // Este hook será implementado quando o sistema de configurações estiver no backend
  // Por enquanto, retorna o schema mínimo
  return createMinimalEntitySchema(personType);
};

/**
 * Exporta a configuração padrão para uso externo
 */
export { DEFAULT_REQUIRED_FIELDS };
export type { RequiredFieldsConfig };
