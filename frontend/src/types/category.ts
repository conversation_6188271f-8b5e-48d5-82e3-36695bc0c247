
export interface Category {
  id: string;
  companyId: string;
  name: string;
  transactionType: "payable" | "receivable";  // Contas a pagar ou Contas a receber
  parentCategoryId?: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;

  // Campos para exibição na hierarquia
  children?: Category[];
  level?: number;
  path?: string[];

  // Campos calculados para UI
  hasChildren?: boolean;
  childrenCount?: number;
  isExpanded?: boolean;

  // Campos de estatísticas
  transactionCount?: number;
  subcategoryCount?: number;
  level?: number;
}

export type CategoryFormValues = {
  id?: string;
  name: string;
  transactionType: "payable" | "receivable";
  parentCategoryId: string | null;
};

// Tipos para visualizações
export type CategoryViewMode = 'tree' | 'drilldown';

export interface CategoryBreadcrumb {
  id: string;
  name: string;
  level: number;
}

export interface CategoryDrilldownState {
  currentLevel: number;
  breadcrumbs: CategoryBreadcrumb[];
  currentCategories: Category[];
  parentId: string | null;
}

// Utilitários para hierarquia
export interface CategoryHierarchyUtils {
  getMaxDepth: (categories: Category[]) => number;
  flattenCategories: (categories: Category[]) => Category[];
  buildCategoryPath: (categoryId: string, categories: Category[]) => CategoryBreadcrumb[];
  validateMaxDepth: (parentId: string | null, categories: Category[], maxDepth?: number) => boolean;
}

// Tipos para estatísticas de categorias
export interface CategoryStatistics {
  categoryId: string;
  categoryName: string;
  transactionCount: number;
  subcategoryCount: number;
  level: number;
}

export interface CategoryStatisticsResponse {
  data: CategoryStatistics[];
}
