/**
 * Tipos para configuração dinâmica de campos obrigatórios em entidades
 */

export interface EntityFieldConfig {
  id: string;
  fieldName: string;
  label: string;
  description: string;
  isRequired: boolean;
  appliesTo: ('individual' | 'company' | 'both')[];
  category: 'basic' | 'contact' | 'document' | 'commercial' | 'tax' | 'banking';
}

export interface EntityValidationConfig {
  id: string;
  companyId: string;
  name: string;
  description: string;
  isActive: boolean;
  fields: EntityFieldConfig[];
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface CreateEntityValidationConfigRequest {
  name: string;
  description: string;
  fields: Omit<EntityFieldConfig, 'id'>[];
}

export interface UpdateEntityValidationConfigRequest {
  name?: string;
  description?: string;
  isActive?: boolean;
  fields?: Omit<EntityFieldConfig, 'id'>[];
}

/**
 * Configuração padrão de campos disponíveis para configuração
 */
export const DEFAULT_CONFIGURABLE_FIELDS: Omit<EntityFieldConfig, 'id' | 'isRequired'>[] = [
  // Campos básicos
  {
    fieldName: 'name',
    label: 'Nome/Razão Social',
    description: 'Nome da pessoa física ou razão social da empresa',
    appliesTo: ['individual', 'company'],
    category: 'basic'
  },
  {
    fieldName: 'tradeName',
    label: 'Nome Fantasia',
    description: 'Nome fantasia da empresa',
    appliesTo: ['company'],
    category: 'basic'
  },
  
  // Campos de contato
  {
    fieldName: 'email',
    label: 'Email',
    description: 'Endereço de email principal',
    appliesTo: ['individual', 'company'],
    category: 'contact'
  },
  {
    fieldName: 'phone',
    label: 'Telefone',
    description: 'Telefone fixo',
    appliesTo: ['individual', 'company'],
    category: 'contact'
  },
  {
    fieldName: 'mobile',
    label: 'Celular',
    description: 'Telefone celular',
    appliesTo: ['individual', 'company'],
    category: 'contact'
  },
  {
    fieldName: 'website',
    label: 'Website',
    description: 'Site da empresa',
    appliesTo: ['company'],
    category: 'contact'
  },
  {
    fieldName: 'contact',
    label: 'Pessoa de Contato',
    description: 'Nome da pessoa responsável pelo contato',
    appliesTo: ['company'],
    category: 'contact'
  },
  
  // Campos de documentos
  {
    fieldName: 'cpf',
    label: 'CPF',
    description: 'Cadastro de Pessoa Física',
    appliesTo: ['individual'],
    category: 'document'
  },
  {
    fieldName: 'rg',
    label: 'RG',
    description: 'Registro Geral',
    appliesTo: ['individual'],
    category: 'document'
  },
  {
    fieldName: 'rgIssuer',
    label: 'Órgão Emissor do RG',
    description: 'Órgão que emitiu o RG',
    appliesTo: ['individual'],
    category: 'document'
  },
  {
    fieldName: 'cnpj',
    label: 'CNPJ',
    description: 'Cadastro Nacional de Pessoa Jurídica',
    appliesTo: ['company'],
    category: 'document'
  },
  {
    fieldName: 'stateRegistration',
    label: 'Inscrição Estadual',
    description: 'Inscrição Estadual da empresa',
    appliesTo: ['company'],
    category: 'document'
  },
  {
    fieldName: 'municipalRegistration',
    label: 'Inscrição Municipal',
    description: 'Inscrição Municipal da empresa',
    appliesTo: ['company'],
    category: 'document'
  },
  
  // Campos comerciais
  {
    fieldName: 'creditLimit',
    label: 'Limite de Crédito',
    description: 'Limite de crédito para a entidade',
    appliesTo: ['individual', 'company'],
    category: 'commercial'
  },
  {
    fieldName: 'paymentTermDays',
    label: 'Prazo de Pagamento',
    description: 'Prazo padrão de pagamento em dias',
    appliesTo: ['individual', 'company'],
    category: 'commercial'
  },
  {
    fieldName: 'discountPercentage',
    label: 'Percentual de Desconto',
    description: 'Percentual de desconto padrão',
    appliesTo: ['individual', 'company'],
    category: 'commercial'
  },
  
  // Campos fiscais
  {
    fieldName: 'taxRegime',
    label: 'Regime Tributário',
    description: 'Regime tributário da entidade',
    appliesTo: ['individual', 'company'],
    category: 'tax'
  },
  {
    fieldName: 'icmsTaxpayer',
    label: 'Contribuinte de ICMS',
    description: 'Indica se é contribuinte de ICMS',
    appliesTo: ['company'],
    category: 'tax'
  },
  {
    fieldName: 'simpleNational',
    label: 'Simples Nacional',
    description: 'Optante pelo Simples Nacional',
    appliesTo: ['company'],
    category: 'tax'
  },
  {
    fieldName: 'withholdTaxes',
    label: 'Retém Impostos',
    description: 'Indica se retém impostos',
    appliesTo: ['company'],
    category: 'tax'
  },
  {
    fieldName: 'sendNfe',
    label: 'Enviar NFe',
    description: 'Enviar NFe por email',
    appliesTo: ['company'],
    category: 'tax'
  },
  {
    fieldName: 'nfeEmail',
    label: 'Email para NFe',
    description: 'Email para envio de NFe',
    appliesTo: ['company'],
    category: 'tax'
  },
  
  // Campos bancários
  {
    fieldName: 'bankCode',
    label: 'Código do Banco',
    description: 'Código do banco',
    appliesTo: ['individual', 'company'],
    category: 'banking'
  },
  {
    fieldName: 'bankName',
    label: 'Nome do Banco',
    description: 'Nome do banco',
    appliesTo: ['individual', 'company'],
    category: 'banking'
  },
  {
    fieldName: 'agency',
    label: 'Agência',
    description: 'Agência bancária',
    appliesTo: ['individual', 'company'],
    category: 'banking'
  },
  {
    fieldName: 'account',
    label: 'Conta',
    description: 'Número da conta bancária',
    appliesTo: ['individual', 'company'],
    category: 'banking'
  },
  {
    fieldName: 'accountDigit',
    label: 'Dígito da Conta',
    description: 'Dígito verificador da conta',
    appliesTo: ['individual', 'company'],
    category: 'banking'
  },
  {
    fieldName: 'pixKey',
    label: 'Chave PIX',
    description: 'Chave PIX para transferências',
    appliesTo: ['individual', 'company'],
    category: 'banking'
  }
];

/**
 * Categorias de campos para organização na interface
 */
export const FIELD_CATEGORIES = [
  { value: 'basic', label: 'Dados Básicos', description: 'Informações fundamentais da entidade' },
  { value: 'contact', label: 'Contato', description: 'Informações de contato' },
  { value: 'document', label: 'Documentos', description: 'Documentos de identificação' },
  { value: 'commercial', label: 'Comercial', description: 'Informações comerciais' },
  { value: 'tax', label: 'Fiscal', description: 'Informações fiscais e tributárias' },
  { value: 'banking', label: 'Bancário', description: 'Dados bancários' }
] as const;

export type FieldCategory = typeof FIELD_CATEGORIES[number]['value'];
