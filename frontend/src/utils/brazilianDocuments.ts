/**
 * Brazilian Documents Utilities
 * Provides validation and formatting functions for Brazilian documents
 */

/**
 * Validates Brazilian CPF (Cadastro de Pessoas Físicas)
 * @param cpf - CPF string (can be formatted or not)
 * @returns boolean indicating if CPF is valid
 */
export function isValidCPF(cpf: string): boolean {
  if (!cpf) return false;
  
  // Remove all non-numeric characters
  const cleanCPF = cpf.replace(/\D/g, '');
  
  // Check if has 11 digits
  if (cleanCPF.length !== 11) return false;
  
  // Check if all digits are the same (invalid CPF)
  if (/^(\d)\1{10}$/.test(cleanCPF)) return false;
  
  // Validate first check digit
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (10 - i);
  }
  let digit = 11 - (sum % 11);
  if (digit === 10 || digit === 11) digit = 0;
  if (digit !== parseInt(cleanCPF.charAt(9))) return false;
  
  // Validate second check digit
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (11 - i);
  }
  digit = 11 - (sum % 11);
  if (digit === 10 || digit === 11) digit = 0;
  if (digit !== parseInt(cleanCPF.charAt(10))) return false;
  
  return true;
}

/**
 * Validates Brazilian CNPJ (Cadastro Nacional da Pessoa Jurídica)
 * @param cnpj - CNPJ string (can be formatted or not)
 * @returns boolean indicating if CNPJ is valid
 */
export function isValidCNPJ(cnpj: string): boolean {
  if (!cnpj) return false;
  
  // Remove all non-numeric characters
  const cleanCNPJ = cnpj.replace(/\D/g, '');
  
  // Check if has 14 digits
  if (cleanCNPJ.length !== 14) return false;
  
  // Check if all digits are the same (invalid CNPJ)
  if (/^(\d)\1{13}$/.test(cleanCNPJ)) return false;
  
  // Validate first check digit
  const weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(cleanCNPJ.charAt(i)) * weights1[i];
  }
  let digit = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  if (digit !== parseInt(cleanCNPJ.charAt(12))) return false;
  
  // Validate second check digit
  const weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
  sum = 0;
  for (let i = 0; i < 13; i++) {
    sum += parseInt(cleanCNPJ.charAt(i)) * weights2[i];
  }
  digit = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  if (digit !== parseInt(cleanCNPJ.charAt(13))) return false;
  
  return true;
}

/**
 * Validates Brazilian CEP (Código de Endereçamento Postal)
 * @param cep - CEP string (can be formatted or not)
 * @returns boolean indicating if CEP format is valid
 */
export function isValidCEP(cep: string): boolean {
  if (!cep) return false;
  
  // Remove all non-numeric characters
  const cleanCEP = cep.replace(/\D/g, '');
  
  // Check if has 8 digits
  return cleanCEP.length === 8;
}

/**
 * Validates Brazilian phone number
 * @param phone - Phone string
 * @returns boolean indicating if phone format is valid
 */
export function isValidBrazilianPhone(phone: string): boolean {
  if (!phone) return false;
  
  // Remove all non-numeric characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Check if has 10 or 11 digits (with area code)
  return cleanPhone.length === 10 || cleanPhone.length === 11;
}

/**
 * Formats CPF string
 * @param cpf - CPF string (only numbers)
 * @returns formatted CPF (XXX.XXX.XXX-XX)
 */
export function formatCPF(cpf: string): string {
  if (!cpf) return '';
  
  const cleanCPF = cpf.replace(/\D/g, '');
  
  if (cleanCPF.length !== 11) return cpf;
  
  return cleanCPF.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
}

/**
 * Formats CNPJ string
 * @param cnpj - CNPJ string (only numbers)
 * @returns formatted CNPJ (XX.XXX.XXX/XXXX-XX)
 */
export function formatCNPJ(cnpj: string): string {
  if (!cnpj) return '';
  
  const cleanCNPJ = cnpj.replace(/\D/g, '');
  
  if (cleanCNPJ.length !== 14) return cnpj;
  
  return cleanCNPJ.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
}

/**
 * Formats CEP string
 * @param cep - CEP string (only numbers)
 * @returns formatted CEP (XXXXX-XXX)
 */
export function formatCEP(cep: string): string {
  if (!cep) return '';
  
  const cleanCEP = cep.replace(/\D/g, '');
  
  if (cleanCEP.length !== 8) return cep;
  
  return cleanCEP.replace(/(\d{5})(\d{3})/, '$1-$2');
}

/**
 * Formats Brazilian phone number
 * @param phone - Phone string (only numbers)
 * @returns formatted phone ((XX) XXXXX-XXXX or (XX) XXXX-XXXX)
 */
export function formatBrazilianPhone(phone: string): string {
  if (!phone) return '';
  
  const cleanPhone = phone.replace(/\D/g, '');
  
  if (cleanPhone.length === 10) {
    // Landline: (XX) XXXX-XXXX
    return cleanPhone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  } else if (cleanPhone.length === 11) {
    // Mobile: (XX) XXXXX-XXXX
    return cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  }
  
  return phone;
}

/**
 * Removes formatting from document
 * @param document - Formatted document string
 * @returns clean document (only numbers)
 */
export function cleanDocument(document: string): string {
  if (!document) return '';
  return document.replace(/\D/g, '');
}

/**
 * Auto-detects document type based on length
 * @param document - Document string
 * @returns detected document type or null
 */
export function detectDocumentType(document: string): 'cpf' | 'cnpj' | null {
  if (!document) return null;
  
  const cleanDoc = cleanDocument(document);
  
  if (cleanDoc.length === 11) return 'cpf';
  if (cleanDoc.length === 14) return 'cnpj';
  
  return null;
}

/**
 * Validates if document matches the expected type
 * @param document - Document string
 * @param type - Expected document type ('cpf' or 'cnpj')
 * @returns boolean indicating if document is valid for the type
 */
export function validateDocumentByType(document: string, type: 'cpf' | 'cnpj'): boolean {
  if (!document) return false;
  
  switch (type) {
    case 'cpf':
      return isValidCPF(document);
    case 'cnpj':
      return isValidCNPJ(document);
    default:
      return false;
  }
}

/**
 * Brazilian States - All valid UF codes
 */
export const BRAZILIAN_STATES = [
  { code: 'AC', name: 'Acre' },
  { code: 'AL', name: 'Alagoas' },
  { code: 'AP', name: 'Amapá' },
  { code: 'AM', name: 'Amazonas' },
  { code: 'BA', name: 'Bahia' },
  { code: 'CE', name: 'Ceará' },
  { code: 'DF', name: 'Distrito Federal' },
  { code: 'ES', name: 'Espírito Santo' },
  { code: 'GO', name: 'Goiás' },
  { code: 'MA', name: 'Maranhão' },
  { code: 'MT', name: 'Mato Grosso' },
  { code: 'MS', name: 'Mato Grosso do Sul' },
  { code: 'MG', name: 'Minas Gerais' },
  { code: 'PA', name: 'Pará' },
  { code: 'PB', name: 'Paraíba' },
  { code: 'PR', name: 'Paraná' },
  { code: 'PE', name: 'Pernambuco' },
  { code: 'PI', name: 'Piauí' },
  { code: 'RJ', name: 'Rio de Janeiro' },
  { code: 'RN', name: 'Rio Grande do Norte' },
  { code: 'RS', name: 'Rio Grande do Sul' },
  { code: 'RO', name: 'Rondônia' },
  { code: 'RR', name: 'Roraima' },
  { code: 'SC', name: 'Santa Catarina' },
  { code: 'SP', name: 'São Paulo' },
  { code: 'SE', name: 'Sergipe' },
  { code: 'TO', name: 'Tocantins' }
];

/**
 * Check if Brazilian state is valid
 * @param state - State string (UF)
 * @returns boolean indicating if state is valid
 */
export function isValidBrazilianState(state: string): boolean {
  if (!state) return false;
  
  return BRAZILIAN_STATES.some(s => s.code === state.toUpperCase());
}

/**
 * Get Brazilian state name by code
 * @param code - State code (UF)
 * @returns state name or null
 */
export function getBrazilianStateName(code: string): string | null {
  const state = BRAZILIAN_STATES.find(s => s.code === code.toUpperCase());
  return state ? state.name : null;
}
