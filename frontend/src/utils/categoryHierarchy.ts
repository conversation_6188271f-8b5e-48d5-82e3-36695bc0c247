import { Category, CategoryBreadcrumb } from "@/types/category";

/**
 * Utilitários para manipulação de hierarquia de categorias
 */

/**
 * Calcula a profundidade máxima de uma árvore de categorias
 */
export function getMaxDepth(categories: Category[]): number {
  let maxDepth = 0;
  
  const calculateDepth = (cats: Category[], currentDepth = 0): void => {
    cats.forEach(cat => {
      maxDepth = Math.max(maxDepth, currentDepth);
      if (cat.children && cat.children.length > 0) {
        calculateDepth(cat.children, currentDepth + 1);
      }
    });
  };
  
  calculateDepth(categories);
  return maxDepth;
}

/**
 * Converte uma árvore hierárquica em lista plana
 */
export function flattenCategories(categories: Category[]): Category[] {
  const result: Category[] = [];
  
  const flatten = (cats: Category[], level = 0, parentPath: string[] = []) => {
    cats.forEach(cat => {
      const currentPath = [...parentPath, cat.id];
      result.push({
        ...cat,
        level,
        path: currentPath,
        hasChildren: !!(cat.children && cat.children.length > 0),
        childrenCount: cat.children?.length || 0
      });
      
      if (cat.children && cat.children.length > 0) {
        flatten(cat.children, level + 1, currentPath);
      }
    });
  };
  
  flatten(categories);
  return result;
}

/**
 * Constrói o caminho de breadcrumbs para uma categoria específica
 */
export function buildCategoryPath(categoryId: string, categories: Category[]): CategoryBreadcrumb[] {
  const path: CategoryBreadcrumb[] = [];
  
  const findPath = (cats: Category[], targetId: string, currentPath: CategoryBreadcrumb[]): boolean => {
    for (const cat of cats) {
      const newPath = [...currentPath, { id: cat.id, name: cat.name, level: currentPath.length }];
      
      if (cat.id === targetId) {
        path.push(...newPath);
        return true;
      }
      
      if (cat.children && findPath(cat.children, targetId, newPath)) {
        return true;
      }
    }
    return false;
  };
  
  findPath(categories, categoryId, []);
  return path.slice(0, -1); // Remove o último item (categoria atual)
}

/**
 * Valida se uma categoria pode ter um pai específico sem exceder a profundidade máxima
 */
export function validateMaxDepth(parentId: string | null, categories: Category[], maxDepth = 5): boolean {
  if (!parentId) return true; // Categoria raiz sempre válida
  
  const path = buildCategoryPath(parentId, categories);
  return path.length < maxDepth - 1; // -1 porque vamos adicionar mais um nível
}

/**
 * Encontra uma categoria por ID em toda a árvore
 */
export function findCategoryById(id: string, categories: Category[]): Category | null {
  for (const cat of categories) {
    if (cat.id === id) return cat;
    if (cat.children) {
      const found = findCategoryById(id, cat.children);
      if (found) return found;
    }
  }
  return null;
}

/**
 * Encontra todos os descendentes de uma categoria
 */
export function findAllDescendants(categoryId: string, categories: Category[]): string[] {
  const descendants: string[] = [];
  const category = findCategoryById(categoryId, categories);
  
  if (!category || !category.children) return descendants;
  
  const collectDescendants = (cats: Category[]) => {
    cats.forEach(cat => {
      descendants.push(cat.id);
      if (cat.children && cat.children.length > 0) {
        collectDescendants(cat.children);
      }
    });
  };
  
  collectDescendants(category.children);
  return descendants;
}

/**
 * Filtra categorias recursivamente baseado em um termo de busca
 * Retorna apenas categorias que correspondem ao termo ou que têm filhos que correspondem
 */
export function filterCategoriesBySearch(categories: Category[], searchTerm: string): Category[] {
  if (!searchTerm) return categories;
  
  const searchRecursive = (cats: Category[]): Category[] => {
    return cats.filter(cat => {
      const matchesSearch = cat.name.toLowerCase().includes(searchTerm.toLowerCase());
      const hasMatchingChildren = cat.children ? searchRecursive(cat.children).length > 0 : false;
      
      return matchesSearch || hasMatchingChildren;
    }).map(cat => ({
      ...cat,
      children: cat.children ? searchRecursive(cat.children) : undefined
    }));
  };
  
  return searchRecursive(categories);
}

/**
 * Filtra categorias por tipo de transação recursivamente
 */
export function filterCategoriesByType(categories: Category[], transactionType: string): Category[] {
  const filterRecursive = (cats: Category[]): Category[] => {
    return cats.filter(cat => {
      if (cat.transactionType === transactionType) {
        return true;
      }
      if (cat.children) {
        const filteredChildren = filterRecursive(cat.children);
        if (filteredChildren.length > 0) {
          return true;
        }
      }
      return false;
    }).map(cat => ({
      ...cat,
      children: cat.children ? filterRecursive(cat.children) : undefined
    }));
  };
  
  return filterRecursive(categories);
}

/**
 * Encontra todos os IDs de categorias que devem ser expandidos para mostrar resultados de busca
 */
export function findExpandedIdsForSearch(categories: Category[], searchTerm: string): Set<string> {
  const expandedIds = new Set<string>();
  
  if (!searchTerm) return expandedIds;
  
  const searchAndExpand = (cats: Category[], parentIds: string[] = []): boolean => {
    let hasMatchingDescendant = false;
    
    cats.forEach(cat => {
      const matchesSearch = cat.name.toLowerCase().includes(searchTerm.toLowerCase());
      let hasMatchingChild = false;
      
      if (cat.children && cat.children.length > 0) {
        hasMatchingChild = searchAndExpand(cat.children, [...parentIds, cat.id]);
      }
      
      if (matchesSearch || hasMatchingChild) {
        // Expandir todos os pais desta categoria
        parentIds.forEach(id => expandedIds.add(id));
        
        // Se esta categoria tem filhos correspondentes, expandi-la também
        if (hasMatchingChild) {
          expandedIds.add(cat.id);
        }
        
        hasMatchingDescendant = true;
      }
    });
    
    return hasMatchingDescendant;
  };
  
  searchAndExpand(categories);
  return expandedIds;
}

/**
 * Conta o total de categorias em uma árvore (incluindo subcategorias)
 */
export function countTotalCategories(categories: Category[]): number {
  let count = 0;
  
  const countRecursive = (cats: Category[]) => {
    cats.forEach(cat => {
      count++;
      if (cat.children && cat.children.length > 0) {
        countRecursive(cat.children);
      }
    });
  };
  
  countRecursive(categories);
  return count;
}

/**
 * Conta categorias por nível
 */
export function countCategoriesByLevel(categories: Category[]): Record<number, number> {
  const counts: Record<number, number> = {};
  
  const countByLevel = (cats: Category[], level = 0) => {
    if (!counts[level]) counts[level] = 0;
    counts[level] += cats.length;
    
    cats.forEach(cat => {
      if (cat.children && cat.children.length > 0) {
        countByLevel(cat.children, level + 1);
      }
    });
  };
  
  countByLevel(categories);
  return counts;
}

/**
 * Verifica se uma categoria pode ser movida para um novo pai sem criar referência circular
 */
export function canMoveCategory(categoryId: string, newParentId: string | null, categories: Category[]): boolean {
  if (!newParentId) return true; // Mover para raiz sempre é válido
  if (categoryId === newParentId) return false; // Não pode ser pai de si mesmo
  
  // Verificar se o novo pai não é um descendente da categoria
  const descendants = findAllDescendants(categoryId, categories);
  return !descendants.includes(newParentId);
}
