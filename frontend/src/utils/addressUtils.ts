import { AddressType, AddressTypeInfo, CreateAddressRequest } from '@/types/api';

// Configuração dos tipos de endereço
export const ADDRESS_TYPES: AddressTypeInfo[] = [
  { value: 'main', label: 'Principal', icon: '🏢' },
  { value: 'billing', label: 'Cobran<PERSON>', icon: '💳' },
  { value: 'shipping', label: 'Entrega', icon: '📦' }
];

// Função para obter informações do tipo de endereço
export const getAddressTypeInfo = (type: AddressType): AddressTypeInfo => {
  return ADDRESS_TYPES.find(t => t.value === type) || ADDRESS_TYPES[0];
};

// Função para mapear o tipo do backend para o frontend
export const mapBackendAddressType = (addressType: any): AddressType => {
  if (!addressType?.name) {
    return 'main';
  }

  switch (addressType.name) {
    case 'Comercial':
      return 'main';
    case 'Faturamento':
      return 'billing';
    case 'Entrega':
      return 'shipping';
    default:
      return 'main';
  }
};

// Função para formatar CEP
export const formatCep = (cep: string): string => {
  if (!cep) return '';
  
  // Remove caracteres não numéricos
  const cleanCep = cep.replace(/\D/g, '');
  
  // Se tem menos de 8 dígitos, retorna como está
  if (cleanCep.length < 8) return cleanCep;
  
  // Formata como XXXXX-XXX
  return `${cleanCep.slice(0, 5)}-${cleanCep.slice(5, 8)}`;
};

// Função para limpar CEP (remover formatação)
export const cleanCep = (cep: string): string => {
  return cep.replace(/\D/g, '');
};

// Função para validar CEP
export const isValidCep = (cep: string): boolean => {
  const clean = cleanCep(cep);
  return clean.length === 8 && /^\d{8}$/.test(clean);
};

// Função para formatar endereço completo para exibição
export const formatFullAddress = (address: {
  street?: string;
  number?: string;
  complement?: string;
  district?: string;
  neighborhood?: string;
  city?: string;
  state?: string;
  zipCode?: string;
}): string => {
  const parts: string[] = [];
  
  // Linha 1: Rua, Número - Complemento
  if (address.street) {
    let line1 = address.street;
    if (address.number) {
      line1 += `, ${address.number}`;
    }
    if (address.complement) {
      line1 += ` - ${address.complement}`;
    }
    parts.push(line1);
  }
  
  // Linha 2: Bairro - Cidade/UF
  if (address.district || address.neighborhood || address.city || address.state) {
    let line2 = '';
    const district = address.district || address.neighborhood;
    if (district) {
      line2 += district;
    }
    if (address.city || address.state) {
      if (line2) line2 += ' - ';
      if (address.city) {
        line2 += address.city;
      }
      if (address.state) {
        line2 += `/${address.state}`;
      }
    }
    if (line2) parts.push(line2);
  }
  
  // Linha 3: CEP
  if (address.zipCode) {
    parts.push(`CEP: ${formatCep(address.zipCode)}`);
  }
  
  return parts.join('\n');
};

// Função para formatar endereço em uma linha
export const formatAddressOneLine = (address: {
  street?: string;
  number?: string;
  district?: string;
  neighborhood?: string;
  city?: string;
  state?: string;
}): string => {
  const parts: string[] = [];
  
  if (address.street) {
    let streetPart = address.street;
    if (address.number) {
      streetPart += `, ${address.number}`;
    }
    parts.push(streetPart);
  }
  
  const district = address.district || address.neighborhood;
  if (district) {
    parts.push(district);
  }
  
  if (address.city && address.state) {
    parts.push(`${address.city}/${address.state}`);
  } else if (address.city) {
    parts.push(address.city);
  } else if (address.state) {
    parts.push(address.state);
  }
  
  return parts.join(' - ');
};

// Função para validar campos obrigatórios do endereço
export const validateAddress = (address: {
  street?: string;
  number?: string;
  district?: string;
  neighborhood?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  type?: AddressType;
}): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};

  // Validar tipo de endereço
  if (!address.type) {
    errors.type = 'Tipo de endereço é obrigatório';
  }

  if (!address.zipCode?.trim()) {
    errors.zipCode = 'CEP é obrigatório';
  } else if (!isValidCep(address.zipCode)) {
    errors.zipCode = 'CEP deve ter 8 dígitos válidos';
    console.log('CEP inválido:', address.zipCode, 'Limpo:', cleanCep(address.zipCode));
  }

  if (!address.street?.trim()) {
    errors.street = 'Rua é obrigatória';
  }

  if (!address.number?.trim()) {
    errors.number = 'Número é obrigatório';
  }

  const district = address.district || address.neighborhood;
  if (!district?.trim()) {
    errors.district = 'Bairro é obrigatório';
  }

  if (!address.city?.trim()) {
    errors.city = 'Cidade é obrigatória';
  }

  if (!address.state?.trim()) {
    errors.state = 'Estado é obrigatório';
  } else if (address.state.length !== 2) {
    errors.state = 'Estado deve ter 2 caracteres (UF)';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// Função para criar um endereço vazio
export const createEmptyAddress = (type: AddressType = 'main'): CreateAddressRequest => ({
  id: '',
  street: '',
  number: '',
  complement: '',
  district: '',
  city: '',
  state: '',
  zipCode: '',
  country: 'Brasil',
  type,
  isDefault: false
});
