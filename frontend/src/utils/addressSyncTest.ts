/**
 * Utilitário para testar a sincronização de dados de endereços
 * Este arquivo contém funções para validar se o problema de sincronização foi corrigido
 */

import { queryClientInstance } from '@/contexts/QueryClientContext';

export interface AddressSyncTestResult {
  success: boolean;
  message: string;
  details?: any;
}

/**
 * Testa se os dados de endereços estão sendo sincronizados corretamente
 */
export const testAddressSync = {
  /**
   * Verifica se o cache está sendo invalidado corretamente
   */
  checkCacheInvalidation: (entityId: string): AddressSyncTestResult => {
    try {
      if (!queryClientInstance) {
        return {
          success: false,
          message: 'QueryClient não está disponível'
        };
      }

      const queryKey = ['entity-addresses', entityId];
      const queryState = queryClientInstance.getQueryState(queryKey);
      
      return {
        success: true,
        message: 'Cache verificado com sucesso',
        details: {
          queryKey,
          isStale: queryState?.isStale,
          dataUpdatedAt: queryState?.dataUpdatedAt,
          lastFetched: new Date(queryState?.dataUpdatedAt || 0).toLocaleString()
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Erro ao verificar cache: ${error}`
      };
    }
  },

  /**
   * Força invalidação do cache para uma entidade
   */
  forceRefresh: async (entityId: string): Promise<AddressSyncTestResult> => {
    try {
      if (!queryClientInstance) {
        return {
          success: false,
          message: 'QueryClient não está disponível'
        };
      }

      const queryKey = ['entity-addresses', entityId];
      
      // Invalidar e refetch
      await queryClientInstance.invalidateQueries({ queryKey });
      await queryClientInstance.refetchQueries({ queryKey });
      
      return {
        success: true,
        message: 'Cache invalidado e dados atualizados com sucesso'
      };
    } catch (error) {
      return {
        success: false,
        message: `Erro ao forçar refresh: ${error}`
      };
    }
  },

  /**
   * Verifica se os dados estão sendo atualizados em tempo real
   */
  checkRealTimeUpdates: (entityId: string): AddressSyncTestResult => {
    try {
      if (!queryClientInstance) {
        return {
          success: false,
          message: 'QueryClient não está disponível'
        };
      }

      const queryKey = ['entity-addresses', entityId];
      const cachedData = queryClientInstance.getQueryData(queryKey);
      const queryState = queryClientInstance.getQueryState(queryKey);
      
      const isDataFresh = queryState?.dataUpdatedAt && 
        (Date.now() - queryState.dataUpdatedAt) < 60000; // Menos de 1 minuto
      
      return {
        success: true,
        message: 'Verificação de atualizações em tempo real concluída',
        details: {
          hasData: !!cachedData,
          dataCount: Array.isArray(cachedData) ? cachedData.length : 0,
          isDataFresh,
          lastUpdate: queryState?.dataUpdatedAt ? 
            new Date(queryState.dataUpdatedAt).toLocaleString() : 'Nunca',
          staleTime: queryState?.staleTime || 0
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Erro ao verificar atualizações: ${error}`
      };
    }
  },

  /**
   * Executa todos os testes de sincronização
   */
  runAllTests: async (entityId: string): Promise<AddressSyncTestResult[]> => {
    const results: AddressSyncTestResult[] = [];
    
    // Teste 1: Verificar cache
    results.push(testAddressSync.checkCacheInvalidation(entityId));
    
    // Teste 2: Verificar atualizações em tempo real
    results.push(testAddressSync.checkRealTimeUpdates(entityId));
    
    // Teste 3: Forçar refresh
    const refreshResult = await testAddressSync.forceRefresh(entityId);
    results.push(refreshResult);
    
    return results;
  }
};

/**
 * Função para debug - mostra informações detalhadas sobre o estado do cache
 */
export const debugAddressCache = (entityId: string) => {
  if (!queryClientInstance) {
    console.warn('QueryClient não está disponível para debug');
    return;
  }

  const queryKey = ['entity-addresses', entityId];
  const queryState = queryClientInstance.getQueryState(queryKey);
  const cachedData = queryClientInstance.getQueryData(queryKey);
  
  console.group(`🔍 Debug Cache - Endereços da Entidade ${entityId}`);
  console.log('Query Key:', queryKey);
  console.log('Query State:', queryState);
  console.log('Cached Data:', cachedData);
  console.log('Data Count:', Array.isArray(cachedData) ? cachedData.length : 0);
  console.log('Is Stale:', queryState?.isStale);
  console.log('Last Updated:', queryState?.dataUpdatedAt ? 
    new Date(queryState.dataUpdatedAt).toLocaleString() : 'Nunca');
  console.log('Stale Time:', queryState?.staleTime || 0, 'ms');
  console.groupEnd();
};

/**
 * Hook para usar em componentes React para testar sincronização
 */
export const useAddressSyncTest = () => {
  return {
    testSync: testAddressSync,
    debugCache: debugAddressCache
  };
};
