import { useState, useMemo } from "react";
import { Category, CategoryFormValues, CategoryViewMode, CategoryBreadcrumb } from "@/types/category";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { useCategoryTree, useCreateCategory, useUpdateCategory, useDeleteCategory } from "@/hooks/api/useCategories";

// Tipo para categoria com filhos para representação em árvore
type CategoryWithChildren = Category & {
  children?: CategoryWithChildren[];
};

export function useCategoryManagement() {
  const { activeCompanyId } = useAuth();
  const [selectedType, setSelectedType] = useState<string | undefined>(undefined);
  const [viewMode, setViewMode] = useState<CategoryViewMode>('tree');

  // Buscar categorias da API usando React Query
  const {
    data: categoriesTreeData,
    isLoading,
    isError
  } = useCategoryTree(selectedType);

  // Mutations para operações CRUD
  const createCategoryMutation = useCreateCategory();
  const updateCategoryMutation = useUpdateCategory();
  const deleteCategoryMutation = useDeleteCategory();

  // Função para converter árvore hierárquica em lista plana
  const flattenCategoryTree = (categories: Category[]): Category[] => {
    const result: Category[] = [];

    const flatten = (cats: Category[], level = 0) => {
      cats.forEach(category => {
        // Adiciona a categoria atual com informações de nível
        result.push({
          ...category,
          level,
          path: level === 0 ? [category.id] : undefined
        });

        // Se tem filhos, processa recursivamente
        if (category.children && category.children.length > 0) {
          flatten(category.children, level + 1);
        }
      });
    };

    flatten(categories);
    return result;
  };

  // Converte os dados da árvore em lista plana para compatibilidade
  const categoriesData = categoriesTreeData ? flattenCategoryTree(categoriesTreeData) : [];

  // Create category
  const createCategory = async (data: CategoryFormValues): Promise<Category> => {
    try {
      const result = await createCategoryMutation.mutateAsync({
        name: data.name,
        transactionType: data.transactionType,
        parentCategoryId: data.parentCategoryId || null
      });

      return result;
    } catch (error) {
      console.error("Error creating category:", error);
      toast({
        title: "Failed to create category",
        description: "Please try again later.",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Update category
  const updateCategory = async (id: string, data: CategoryFormValues): Promise<Category> => {
    try {
      // Verificar se não estamos criando uma referência circular
      if (data.parentCategoryId) {
        const descendantIds = findAllDescendants(id);
        if (descendantIds.includes(data.parentCategoryId)) {
          throw new Error("Cannot set a descendant as parent (circular reference)");
        }
      }
      
      const result = await updateCategoryMutation.mutateAsync({
        id,
        data: {
          name: data.name,
          transactionType: data.transactionType,
          parentCategoryId: data.parentCategoryId || null,
        }
      });
      
      return result;
    } catch (error) {
      console.error("Error updating category:", error);
      toast({
        title: "Failed to update category",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Delete category
  const deleteCategory = async (id: string): Promise<void> => {
    try {
      // Verificar se a categoria tem filhos
      const hasChildren = categoriesData?.some((c) => c.parentCategoryId === id);
      if (hasChildren) {
        toast({
          title: "Não é possível excluir categoria",
          description: "Esta categoria possui subcategorias. Exclua-as primeiro.",
          variant: "destructive",
        });
        return;
      }

      await deleteCategoryMutation.mutateAsync(id);
    } catch (error) {
      console.error("Erro ao excluir categoria:", error);

      // Melhor tratamento de erro baseado no tipo
      let errorMessage = "Erro inesperado ao excluir categoria.";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null && 'response' in error) {
        const apiError = error as any;
        errorMessage = apiError.response?.data?.message || "Erro na comunicação com o servidor.";
      }

      toast({
        title: "Falha ao excluir categoria",
        description: errorMessage,
        variant: "destructive",
      });

      // Re-throw para que o componente possa lidar com o erro se necessário
      throw error;
    }
  };

  // Helper function to find all descendants of a category
  const findAllDescendants = (categoryId: string): string[] => {
    if (!categoriesData) return [];
    
    const descendants: string[] = [];
    
    const findChildren = (id: string) => {
      const children = categoriesData.filter(c => c.parentCategoryId === id);
      children.forEach(child => {
        descendants.push(child.id);
        findChildren(child.id);
      });
    };
    
    findChildren(categoryId);
    return descendants;
  };

  // Build category tree - agora usa os dados já hierárquicos
  const getCategoryTree = (type?: string): CategoryWithChildren[] => {
    if (!categoriesTreeData) return [];

    if (type) {
      // Filtra a árvore por tipo de transação
      return categoriesTreeData
        .filter((c) => c.transactionType === type)
        .map(category => ({
          ...category,
          children: category.children?.filter(child => child.transactionType === type) || []
        }));
    }

    return categoriesTreeData;
  };

  // Get flat list of categories
  const getCategories = (type?: string): Category[] => {
    if (!categoriesData) return [];

    if (type) {
      return categoriesData.filter((c) => c.transactionType === type);
    }
    return categoriesData;
  };

  // Filter categories by type and set selected type
  const filterByType = (type?: string) => {
    setSelectedType(type);
    return type && categoriesData ? categoriesData.filter((c) => c.transactionType === type) : categoriesData || [];
  };

  // Utilitários para hierarquia
  const getMaxDepth = (categories: Category[]): number => {
    let maxDepth = 0;

    const calculateDepth = (cats: Category[], currentDepth = 0): void => {
      cats.forEach(cat => {
        maxDepth = Math.max(maxDepth, currentDepth);
        if (cat.children && cat.children.length > 0) {
          calculateDepth(cat.children, currentDepth + 1);
        }
      });
    };

    calculateDepth(categories);
    return maxDepth;
  };

  const buildCategoryPath = (categoryId: string, categories: Category[]): CategoryBreadcrumb[] => {
    const path: CategoryBreadcrumb[] = [];

    const findPath = (cats: Category[], targetId: string, currentPath: CategoryBreadcrumb[]): boolean => {
      for (const cat of cats) {
        const newPath = [...currentPath, { id: cat.id, name: cat.name, level: currentPath.length }];

        if (cat.id === targetId) {
          path.push(...newPath);
          return true;
        }

        if (cat.children && findPath(cat.children, targetId, newPath)) {
          return true;
        }
      }
      return false;
    };

    findPath(categories, categoryId, []);
    return path.slice(0, -1); // Remove o último item (categoria atual)
  };

  const validateMaxDepth = (parentId: string | null, categories: Category[], maxDepth = 5): boolean => {
    if (!parentId) return true; // Categoria raiz sempre válida

    const path = buildCategoryPath(parentId, categories);
    return path.length < maxDepth - 1; // -1 porque vamos adicionar mais um nível
  };

  const flattenCategories = (categories: Category[]): Category[] => {
    const result: Category[] = [];

    const flatten = (cats: Category[], level = 0) => {
      cats.forEach(cat => {
        result.push({
          ...cat,
          level,
          hasChildren: !!(cat.children && cat.children.length > 0),
          childrenCount: cat.children?.length || 0
        });

        if (cat.children && cat.children.length > 0) {
          flatten(cat.children, level + 1);
        }
      });
    };

    flatten(categories);
    return result;
  };

  // Controle do modo de visualização
  const toggleViewMode = () => {
    setViewMode(prev => prev === 'tree' ? 'drilldown' : 'tree');
  };

  const setViewModeDirectly = (mode: CategoryViewMode) => {
    setViewMode(mode);
  };

  return {
    categories: categoriesData || [],
    categoriesTree: categoriesTreeData || [],
    isLoading,
    isError,
    createCategory,
    updateCategory,
    deleteCategory,
    getCategoryTree,
    getCategories,
    filterByType,

    // Controle de visualização
    viewMode,
    toggleViewMode,
    setViewMode: setViewModeDirectly,

    // Utilitários de hierarquia
    hierarchyUtils: {
      getMaxDepth,
      flattenCategories,
      buildCategoryPath,
      validateMaxDepth
    }
  };
}
