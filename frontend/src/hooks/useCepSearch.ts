import { useState, useCallback } from 'react';
import { CepSearchResponse } from '@/types/api';
import { zipCodeService } from '@/services/api';
import { toast } from 'sonner';

interface UseCepSearchReturn {
  searchCep: (cep: string) => Promise<CepSearchResponse | null>;
  isLoading: boolean;
  error: string | null;
}

export const useCepSearch = (): UseCepSearchReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchCep = useCallback(async (cep: string): Promise<CepSearchResponse | null> => {
    // Limpa o CEP removendo caracteres não numéricos
    const cleanCep = cep.replace(/\D/g, '');

    // Valida se o CEP tem 8 dígitos
    if (cleanCep.length !== 8) {
      setError('CEP deve ter 8 dígitos');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Primeiro tenta buscar no backend usando o serviço autenticado
      try {
        const data = await zipCodeService.getZipCode(cleanCep);
        if (data) {
          const result: CepSearchResponse = {
            zipCode: data.zipCode || `${cleanCep.slice(0, 5)}-${cleanCep.slice(5)}`,
            street: data.street || '',
            district: data.neighborhood || '', // Backend usa 'neighborhood' em vez de 'district'
            city: data.city || '',
            state: data.state || '',
            country: data.country || 'Brasil'
          };
          return result;
        }
      } catch (backendError) {
        console.warn('Backend CEP search failed, trying ViaCEP:', backendError);
      }

      // Se o backend falhar, usa ViaCEP diretamente
      const viaCepResponse = await fetch(`https://viacep.com.br/ws/${cleanCep}/json/`);
      const viaCepData = await viaCepResponse.json();

      if (viaCepData.erro) {
        setError('CEP não encontrado');
        toast.error('CEP não encontrado. Verifique o CEP informado.');
        return null;
      }

      const result: CepSearchResponse = {
        zipCode: `${cleanCep.slice(0, 5)}-${cleanCep.slice(5)}`,
        street: viaCepData.logradouro || '',
        district: viaCepData.bairro || '',
        city: viaCepData.localidade || '',
        state: viaCepData.uf || '',
        country: 'Brasil'
      };

      return result;
    } catch (err) {
      const errorMessage = 'Erro ao buscar CEP. Tente novamente.';
      setError(errorMessage);
      toast.error(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    searchCep,
    isLoading,
    error
  };
};
