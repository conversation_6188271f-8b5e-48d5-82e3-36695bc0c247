
import { useState, useMemo } from "react";
import { Bank } from "@/types/bank";
import { useToast } from "@/hooks/use-toast";
import { useBanks, useCreateBank, useUpdateBank, useDeleteBank } from "@/hooks/api/useBanks";

export const useBankManagement = (itemsPerPage: number = 10) => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentBank, setCurrentBank] = useState<Bank | undefined>(undefined);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [bankToDelete, setBankToDelete] = useState<Bank | null>(null);

  // Buscar bancos via API com paginação baseada na busca
  const { data: banksData, isLoading: loading, error } = useBanks(1, 1000, searchTerm);

  // Hooks de mutação
  const createBankMutation = useCreateBank();
  const updateBankMutation = useUpdateBank();
  const deleteBankMutation = useDeleteBank();

  // Processar dados da API
  const banks = banksData?.data || [];

  // Filtragem e paginação local (já que a API pode retornar muitos resultados)
  const filteredBanks = useMemo(() => {
    if (!searchTerm) return banks;
    return banks.filter(bank =>
      bank.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      bank.code.includes(searchTerm)
    );
  }, [banks, searchTerm]);

  const paginatedBanks = useMemo(() => {
    return filteredBanks.slice(
      (currentPage - 1) * itemsPerPage,
      currentPage * itemsPerPage
    );
  }, [filteredBanks, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredBanks.length / itemsPerPage);

  // Manipuladores de eventos
  const handleEditBank = (bank: Bank) => {
    setCurrentBank(bank);
    setIsModalOpen(true);
  };

  const handleAddNewBank = () => {
    setCurrentBank(undefined);
    setIsModalOpen(true);
  };

  const handleDeleteBank = (bank: Bank) => {
    setBankToDelete(bank);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (bankToDelete) {
      try {
        await deleteBankMutation.mutateAsync(bankToDelete.id);
        setIsDeleteDialogOpen(false);
        setBankToDelete(null);
      } catch (error) {
        // O erro já é tratado no hook useDeleteBank
        console.error('Erro ao excluir banco:', error);
      }
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset para a primeira página ao buscar
  };

  const handleSaveBank = async (bankData: Partial<Bank>) => {
    try {
      if (currentBank) {
        // Atualizar banco existente
        await updateBankMutation.mutateAsync({
          id: currentBank.id,
          data: {
            code: bankData.code || currentBank.code,
            name: bankData.name || currentBank.name,
            logo: bankData.logo || currentBank.logo
          }
        });
      } else {
        // Criar novo banco
        await createBankMutation.mutateAsync({
          code: bankData.code || "",
          name: bankData.name || "",
          logo: bankData.logo
        });
      }

      setIsModalOpen(false);
      setCurrentBank(undefined);
    } catch (error) {
      // O erro já é tratado nos hooks de mutação
      console.error('Erro ao salvar banco:', error);
    }
  };

  return {
    banks: paginatedBanks,
    loading,
    error,
    searchTerm,
    handleSearchChange,
    currentPage,
    totalPages,
    setCurrentPage,
    isModalOpen,
    setIsModalOpen,
    currentBank,
    handleEditBank,
    handleAddNewBank,
    handleDeleteBank,
    isDeleteDialogOpen,
    setIsDeleteDialogOpen,
    bankToDelete,
    confirmDelete,
    handleSaveBank,
    isCreating: createBankMutation.isPending,
    isUpdating: updateBankMutation.isPending,
    isDeleting: deleteBankMutation.isPending
  };
};
