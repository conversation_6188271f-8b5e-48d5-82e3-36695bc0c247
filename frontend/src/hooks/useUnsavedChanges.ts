import { useEffect, useRef, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

interface UseUnsavedChangesOptions {
  hasUnsavedChanges: boolean;
  message?: string;
  onBeforeUnload?: () => void;
  onNavigateAway?: () => boolean | Promise<boolean>;
}

/**
 * Hook para detectar e confirmar mudanças não salvas antes de sair da página
 */
export const useUnsavedChanges = ({
  hasUnsavedChanges,
  message = 'Você tem alterações não salvas. Deseja realmente sair?',
  onBeforeUnload,
  onNavigateAway
}: UseUnsavedChangesOptions) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(null);
  const isNavigatingRef = useRef(false);

  // Interceptar navegação do React Router
  useEffect(() => {
    if (!hasUnsavedChanges) return;

    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        event.preventDefault();
        event.returnValue = message;
        if (onBeforeUnload) {
          onBeforeUnload();
        }
        return message;
      }
    };

    // Adicionar listener para beforeunload (fechar aba/navegador)
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges, message, onBeforeUnload]);

  // Função para confirmar navegação
  const confirmNavigation = async () => {
    if (!pendingNavigation) return;

    let canNavigate = true;
    
    if (onNavigateAway) {
      canNavigate = await onNavigateAway();
    }

    if (canNavigate) {
      isNavigatingRef.current = true;
      navigate(pendingNavigation);
    }

    setShowConfirmDialog(false);
    setPendingNavigation(null);
  };

  // Função para cancelar navegação
  const cancelNavigation = () => {
    setShowConfirmDialog(false);
    setPendingNavigation(null);
  };

  // Função para navegar com confirmação
  const navigateWithConfirmation = (to: string) => {
    if (!hasUnsavedChanges || isNavigatingRef.current) {
      navigate(to);
      return;
    }

    setPendingNavigation(to);
    setShowConfirmDialog(true);
  };

  // Função para forçar navegação (sem confirmação)
  const forceNavigate = (to: string) => {
    isNavigatingRef.current = true;
    navigate(to);
  };

  return {
    showConfirmDialog,
    confirmNavigation,
    cancelNavigation,
    navigateWithConfirmation,
    forceNavigate,
    hasUnsavedChanges
  };
};

/**
 * Hook simplificado para usar com formulários
 */
export const useFormUnsavedChanges = (isDirty: boolean) => {
  return useUnsavedChanges({
    hasUnsavedChanges: isDirty,
    message: 'Você tem alterações não salvas no formulário. Deseja realmente sair?'
  });
};
