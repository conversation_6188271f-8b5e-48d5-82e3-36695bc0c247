import { useState, useCallback, useEffect } from 'react';
import { z } from 'zod';
import { toast } from 'sonner';
import monitoringService from '@/services/api/monitoringService';
import { logger } from '@/utils/secureLogger';

interface FormOptions<T> {
  initialValues: T;
  schema: z.ZodType<any, any>;
  onSubmit?: (values: T) => Promise<void> | void;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  resetOnSubmit?: boolean;
  successMessage?: string;
  errorMessage?: string;
}

export function useFormWithValidation<T extends Record<string, any>>(options: FormOptions<T>) {
  const {
    initialValues,
    schema,
    onSubmit,
    onSuccess,
    onError,
    resetOnSubmit = false,
    successMessage = 'Operação realizada com sucesso!',
    errorMessage = 'Ocorreu um erro. Tente novamente.'
  } = options;

  // Estados do formulário
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [isDirty, setIsDirty] = useState(false);

  // Validar o formulário sempre que os valores mudarem
  useEffect(() => {
    validateForm();
    
    // Verificar se o formulário está "sujo" (modificado)
    const isDirtyNow = Object.keys(initialValues).some(
      key => values[key] !== initialValues[key]
    );
    
    setIsDirty(isDirtyNow);
  }, [values]);

  // Função para validar o formulário
  const validateForm = useCallback(() => {
    try {
      schema.parse(values);
      setErrors({});
      setIsValid(true);
      return true;
    } catch (err) {
      if (err instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        
        err.errors.forEach(error => {
          const path = error.path.join('.');
          newErrors[path] = error.message;
        });
        
        setErrors(newErrors);
        setIsValid(false);
        return false;
      }
      
      setIsValid(false);
      return false;
    }
  }, [values, schema]);

  // Manipulador para alteração de campos
  const handleChange = useCallback((event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = event.target;
    
    setValues(prev => ({
      ...prev,
      [name]: type === 'checkbox' 
        ? (event.target as HTMLInputElement).checked 
        : value
    }));
    
    // Marcar o campo como tocado
    if (!touched[name]) {
      setTouched(prev => ({
        ...prev,
        [name]: true
      }));
    }
  }, [touched]);

  // Manipulador para alteração de valores diretamente
  const setValue = useCallback((name: string, value: any) => {
    setValues(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Marcar o campo como tocado
    if (!touched[name]) {
      setTouched(prev => ({
        ...prev,
        [name]: true
      }));
    }
  }, [touched]);

  // Manipulador para blur (quando o campo perde o foco)
  const handleBlur = useCallback((event: React.FocusEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name } = event.target;
    
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));
  }, []);

  // Função para resetar o formulário
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
    setIsDirty(false);
  }, [initialValues]);

  // Função para manipular o envio do formulário
  const handleSubmit = useCallback(async (event?: React.FormEvent) => {
    if (event) {
      event.preventDefault();
    }
    
    // Marcar todos os campos como tocados
    const allTouched = Object.keys(values).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {} as Record<string, boolean>);
    
    setTouched(allTouched);
    
    // Validar o formulário antes de enviar
    const isFormValid = validateForm();
    
    if (!isFormValid) {
      toast.error('Por favor, corrija os erros no formulário antes de continuar.');
      
      monitoringService.recordEvent('form_validation_failed', {
        formErrors: Object.keys(errors).length,
        fields: Object.keys(errors)
      });
      
      return;
    }
    
    if (!onSubmit) return;
    
    setIsSubmitting(true);
    
    try {
      monitoringService.recordEvent('form_submit_start', {
        formFields: Object.keys(values).length
      });
      
      const result = await onSubmit(values);
      
      if (successMessage) {
        toast.success(successMessage);
      }
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      if (resetOnSubmit) {
        resetForm();
      }
      
      monitoringService.recordEvent('form_submit_success', {
        formFields: Object.keys(values).length
      });
    } catch (error) {
      logger.error('Erro ao enviar formulário', error);

      if (errorMessage) {
        toast.error(errorMessage);
      }

      if (onError) {
        onError(error);
      }

      monitoringService.recordEvent('form_submit_error', {
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [values, errors, validateForm, onSubmit, onSuccess, onError, resetForm, resetOnSubmit, successMessage, errorMessage]);

  // Verificar se um campo tem erro e foi tocado
  const hasError = useCallback((fieldName: string) => {
    return touched[fieldName] && !!errors[fieldName];
  }, [touched, errors]);

  // Obter a mensagem de erro para um campo
  const getError = useCallback((fieldName: string) => {
    return hasError(fieldName) ? errors[fieldName] : '';
  }, [hasError, errors]);

  return {
    values,
    errors,
    touched,
    isSubmitting,
    isValid,
    isDirty,
    handleChange,
    handleBlur,
    handleSubmit,
    setValue,
    resetForm,
    hasError,
    getError
  };
}
