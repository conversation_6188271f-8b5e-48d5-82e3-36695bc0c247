import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'sonner';
import authService from '@/services/api/authService';
import tokenStorage from '@/services/api/tokenStorage';
import monitoringService from '@/services/api/monitoringService';
import { logger } from '@/utils/secureLogger';
import { useApiCache } from './useApiCache';
import { User } from '@/types/api';

// Removido - usando User de @/types/api

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface ResetPasswordData {
  password: string;
  confirmPassword: string;
  token: string;
}

export interface ForgotPasswordData {
  email: string;
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Usar o hook de cache para o perfil do usuário
  const { 
    data: userProfile, 
    loading: profileLoading,
    error: profileError,
    refetch: refetchProfile,
    invalidateCache: invalidateProfileCache
  } = useApiCache<User | null>(
    'auth/profile',
    async () => {
      try {
        if (!tokenStorage.getAccessToken()) {
          return null;
        }
        const userData = await authService.getProfile();
        return userData;
      } catch (error) {
        // Se houver erro ao obter o perfil, pode ser token inválido
        if (tokenStorage.getRefreshToken()) {
          try {
            // Tentar renovar o token
            await authService.refreshToken();
            // Tentar novamente com o novo token
            return await authService.getProfile();
          } catch (refreshError) {
            // Se falhar na renovação, limpar tokens
            tokenStorage.clearTokens();
            return null;
          }
        }
        // Se não tiver refresh token, limpar tokens
        tokenStorage.clearTokens();
        return null;
      }
    },
    {
      cacheDuration: 5 * 60 * 1000, // 5 minutos
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      enabled: !!tokenStorage.getAccessToken(),
      errorMessage: 'Falha ao carregar dados do usuário'
    }
  );

  // Atualizar o estado do usuário quando o perfil for carregado
  useEffect(() => {
    if (!profileLoading) {
      setUser(userProfile);
      setLoading(false);
      setInitialized(true);
    }
  }, [userProfile, profileLoading]);

  // Inicializar o estado de autenticação na montagem do componente
  useEffect(() => {
    const initAuth = async () => {
      setLoading(true);
      
      try {
        // Verificar se há tokens armazenados
        const hasTokens = !!tokenStorage.getAccessToken();
        
        if (hasTokens) {
          // Verificar se o token é válido
          try {
            // O refetchProfile já tenta renovar o token se necessário
            await refetchProfile();
            monitoringService.recordEvent('auth_auto_login_success', {});
          } catch (error) {
            logger.error('Erro ao verificar autenticação', error);
            tokenStorage.clearTokens();
            monitoringService.recordEvent('auth_token_invalid', {});
          }
        } else {
          setUser(null);
          monitoringService.recordEvent('auth_no_tokens', {});
        }
      } catch (error) {
        logger.error('Erro ao inicializar autenticação', error);
        monitoringService.recordEvent('auth_init_error', {
          error: error instanceof Error ? error.message : 'Erro desconhecido'
        });
      } finally {
        setLoading(false);
        setInitialized(true);
      }
    };
    
    initAuth();
  }, [refetchProfile]);

  // Função para fazer login
  const login = useCallback(async (credentials: LoginCredentials) => {
    setLoading(true);
    
    try {
      monitoringService.recordEvent('auth_login_attempt', { email: credentials.email });
      
      const userData = await authService.login(credentials);
      setUser(userData);
      
      // Registrar evento de login bem-sucedido
      monitoringService.recordEvent('auth_login_success', { userId: userData.id });
      
      // Invalidar o cache do perfil para garantir dados atualizados
      invalidateProfileCache();
      
      // Redirecionar após o login
      const from = location.state?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
      
      toast.success('Login realizado com sucesso!');
      return userData;
    } catch (error) {
      // Erro ao fazer login - processar sem log
      
      monitoringService.recordEvent('auth_login_error', {
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
      
      toast.error('Falha ao fazer login. Verifique suas credenciais.');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [navigate, location, invalidateProfileCache]);

  // Função para fazer logout
  const logout = useCallback(async () => {
    setLoading(true);
    
    try {
      // Registrar evento de logout
      monitoringService.recordEvent('auth_logout', { userId: user?.id });
      
      await authService.logout();
      setUser(null);
      
      // Limpar o cache do perfil
      invalidateProfileCache();
      
      // Redirecionar para a página de login
      navigate('/login');
      
      toast.success('Logout realizado com sucesso!');
    } catch (error) {
      // Erro ao fazer logout - processar sem log
      
      // Mesmo com erro, limpar tokens e estado do usuário
      tokenStorage.clearTokens();
      setUser(null);
      
      monitoringService.recordEvent('auth_logout_error', {
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
      
      navigate('/login');
    } finally {
      setLoading(false);
    }
  }, [navigate, user, invalidateProfileCache]);

  // Função para registrar um novo usuário
  const register = useCallback(async (data: RegisterCredentials) => {
    setLoading(true);
    
    try {
      monitoringService.recordEvent('auth_register_attempt', { email: data.email });
      
      const userData = await authService.register(data);
      setUser(userData);
      
      // Registrar evento de registro bem-sucedido
      monitoringService.recordEvent('auth_register_success', { userId: userData.id });
      
      // Invalidar o cache do perfil
      invalidateProfileCache();
      
      // Redirecionar após o registro
      navigate('/dashboard');
      
      toast.success('Registro realizado com sucesso!');
      return userData;
    } catch (error) {
      // Erro ao registrar - processar sem log
      
      monitoringService.recordEvent('auth_register_error', {
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
      
      toast.error('Falha ao registrar. Verifique os dados e tente novamente.');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [navigate, invalidateProfileCache]);

  // Função para solicitar redefinição de senha
  const forgotPassword = useCallback(async (data: ForgotPasswordData) => {
    try {
      monitoringService.recordEvent('auth_forgot_password_attempt', { email: data.email });
      
      await authService.forgotPassword(data);
      
      monitoringService.recordEvent('auth_forgot_password_success', { email: data.email });
      
      toast.success('Instruções de redefinição de senha enviadas para seu e-mail.');
    } catch (error) {
      logger.error('Erro ao solicitar redefinição de senha', error);

      monitoringService.recordEvent('auth_forgot_password_error', {
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });

      toast.error('Falha ao solicitar redefinição de senha. Tente novamente.');
      throw error;
    }
  }, []);

  // Função para redefinir senha
  const resetPassword = useCallback(async (data: ResetPasswordData) => {
    try {
      monitoringService.recordEvent('auth_reset_password_attempt', {});
      
      await authService.resetPassword(data);
      
      monitoringService.recordEvent('auth_reset_password_success', {});
      
      toast.success('Senha redefinida com sucesso!');
      
      // Redirecionar para a página de login
      navigate('/login');
    } catch (error) {
      logger.error('Erro ao redefinir senha', error);

      monitoringService.recordEvent('auth_reset_password_error', {
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });

      toast.error('Falha ao redefinir senha. Verifique o token e tente novamente.');
      throw error;
    }
  }, [navigate]);

  // Verificar se o usuário está autenticado
  const isAuthenticated = !!user;

  // Verificar se o usuário tem uma determinada função
  const hasRole = useCallback((roleName: string | string[]) => {
    if (!user || !user.role) return false;

    if (Array.isArray(roleName)) {
      return roleName.includes(user.role.name);
    }

    return user.role.name === roleName;
  }, [user]);

  return {
    user,
    loading,
    initialized,
    isAuthenticated,
    login,
    logout,
    register,
    forgotPassword,
    resetPassword,
    hasRole,
    refetchProfile
  };
}
