import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { categoryService } from '@/services/api';
import { CreateCategoryRequest, UpdateCategoryRequest } from '@/types/api';
import { toast } from 'sonner';

// Hook para listar categorias com paginação
export const useCategories = (page = 1, limit = 10, search?: string, type?: string) => {
  return useQuery({
    queryKey: ['categories', { page, limit, search, type }],
    queryFn: () => categoryService.getCategories(page, limit, search, type),
    placeholderData: (previousData) => previousData,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para buscar uma categoria específica por ID
export const useCategory = (id: string) => {
  return useQuery({
    queryKey: ['categories', id],
    queryFn: () => categoryService.getCategoryById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para buscar a árvore de categorias
export const useCategoryTree = (transactionType?: string) => {
  return useQuery({
    queryKey: ['categories', 'tree', { transactionType }],
    queryFn: () => categoryService.getCategoryTree(transactionType),
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para buscar estatísticas das categorias
export const useCategoryStatistics = (transactionType?: string) => {
  return useQuery({
    queryKey: ['categories', 'statistics', { transactionType }],
    queryFn: () => categoryService.getCategoryStatistics(transactionType),
    staleTime: 2 * 60 * 1000, // 2 minutos (estatísticas mudam mais frequentemente)
  });
};

// Hook para criar uma nova categoria
export const useCreateCategory = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateCategoryRequest) => categoryService.createCategory(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      toast.success('Categoria criada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao criar categoria. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar uma categoria existente
export const useUpdateCategory = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateCategoryRequest }) => 
      categoryService.updateCategory(id, data),
    onSuccess: (updatedCategory) => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      queryClient.setQueryData(['categories', updatedCategory.id], updatedCategory);
      toast.success('Categoria atualizada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar categoria. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir uma categoria
export const useDeleteCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => categoryService.deleteCategory(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      queryClient.removeQueries({ queryKey: ['categories', id] });
      toast.success('Categoria excluída com sucesso!');
    },
    onError: (error: any) => {
      console.error("Erro ao excluir categoria:", error);
      toast.error(
        error.response?.data?.message ||
        'Falha ao excluir categoria. Por favor, tente novamente.'
      );
    }
  });
};
