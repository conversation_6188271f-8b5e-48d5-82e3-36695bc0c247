import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { entityAddressService } from '@/services/api/entityAddressService';
import { CreateAddressRequest, UpdateAddressRequest } from '@/types/api';
import { toast } from 'sonner';

// Hook para listar endereços de uma entidade específica
export const useEntityAddresses = (entityId: string, options?: {
  staleTime?: number;
  refetchOnMount?: boolean;
  refetchOnWindowFocus?: boolean;
}) => {
  return useQuery({
    queryKey: ['entity-addresses', entityId],
    queryFn: () => entityAddressService.getEntityAddresses(entityId),
    enabled: !!entityId,
    staleTime: 0, // Sempre buscar dados frescos
    refetchOnMount: true, // Sempre refetch ao montar
    refetchOnWindowFocus: true, // Refetch ao focar na janela
    gcTime: 0, // N<PERSON> manter cache
  });
};

// Hook para buscar um endereço específico de uma entidade
export const useEntityAddress = (entityId: string, addressId: string) => {
  return useQuery({
    queryKey: ['entity-addresses', entityId, addressId],
    queryFn: () => entityAddressService.getEntityAddressById(entityId, addressId),
    enabled: !!entityId && !!addressId,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para criar um novo endereço para uma entidade
export const useCreateEntityAddress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ entityId, data }: { entityId: string, data: CreateAddressRequest }) =>
      entityAddressService.createEntityAddress(entityId, data),
    onMutate: async ({ entityId, data }) => {
      // Cancelar queries em andamento para evitar conflitos
      await queryClient.cancelQueries({ queryKey: ['entity-addresses', entityId] });

      // Snapshot do estado anterior para rollback se necessário
      const previousAddresses = queryClient.getQueryData(['entity-addresses', entityId]);

      // Atualização otimista: adicionar o novo endereço ao cache imediatamente
      const optimisticAddress = {
        id: `temp-${Date.now()}`, // ID temporário
        ...data,
        entityId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      queryClient.setQueryData(['entity-addresses', entityId], (old: any[]) => {
        return old ? [...old, optimisticAddress] : [optimisticAddress];
      });

      return { previousAddresses };
    },
    onSuccess: (newAddress, { entityId }) => {
      // Invalidar e refetch para garantir dados atualizados do servidor
      queryClient.invalidateQueries({ queryKey: ['entity-addresses', entityId] });
      queryClient.refetchQueries({ queryKey: ['entity-addresses', entityId] });

      // Invalidar também a lista geral de endereços se necessário
      queryClient.invalidateQueries({ queryKey: ['addresses'] });

      toast.success('Endereço criado com sucesso!');
    },
    onError: (error: any, { entityId }, context) => {
      // Rollback em caso de erro
      if (context?.previousAddresses) {
        queryClient.setQueryData(['entity-addresses', entityId], context.previousAddresses);
      }

      toast.error(
        error.response?.data?.message ||
        'Falha ao criar endereço. Por favor, tente novamente.'
      );
    },
    onSettled: (data, error, { entityId }) => {
      // Sempre invalidar no final para garantir consistência
      queryClient.invalidateQueries({ queryKey: ['entity-addresses', entityId] });
    }
  });
};

// Hook para atualizar um endereço de uma entidade
export const useUpdateEntityAddress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ entityId, addressId, data }: {
      entityId: string,
      addressId: string,
      data: UpdateAddressRequest
    }) => entityAddressService.updateEntityAddress(entityId, addressId, data),
    onMutate: async ({ entityId, addressId, data }) => {
      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey: ['entity-addresses', entityId] });

      // Snapshot do estado anterior
      const previousAddresses = queryClient.getQueryData(['entity-addresses', entityId]);

      // Atualização otimista
      queryClient.setQueryData(['entity-addresses', entityId], (old: any[]) => {
        if (!old) return old;
        return old.map(address =>
          address.id === addressId
            ? { ...address, ...data, updatedAt: new Date().toISOString() }
            : address
        );
      });

      return { previousAddresses };
    },
    onSuccess: (updatedAddress, { entityId, addressId }) => {
      // Invalidar e refetch para garantir dados atualizados
      queryClient.invalidateQueries({ queryKey: ['entity-addresses', entityId] });
      queryClient.refetchQueries({ queryKey: ['entity-addresses', entityId] });

      // Atualizar o cache do endereço específico
      queryClient.setQueryData(['entity-addresses', entityId, addressId], updatedAddress);

      // Invalidar também a lista geral de endereços se necessário
      queryClient.invalidateQueries({ queryKey: ['addresses'] });

      toast.success('Endereço atualizado com sucesso!');
    },
    onError: (error: any, { entityId }, context) => {
      // Rollback em caso de erro
      if (context?.previousAddresses) {
        queryClient.setQueryData(['entity-addresses', entityId], context.previousAddresses);
      }

      toast.error(
        error.response?.data?.message ||
        'Falha ao atualizar endereço. Por favor, tente novamente.'
      );
    },
    onSettled: (data, error, { entityId }) => {
      // Sempre invalidar no final
      queryClient.invalidateQueries({ queryKey: ['entity-addresses', entityId] });
    }
  });
};

// Hook para excluir um endereço de uma entidade
export const useDeleteEntityAddress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ entityId, addressId }: { entityId: string, addressId: string }) =>
      entityAddressService.deleteEntityAddress(entityId, addressId),
    onMutate: async ({ entityId, addressId }) => {
      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey: ['entity-addresses', entityId] });

      // Snapshot do estado anterior
      const previousAddresses = queryClient.getQueryData(['entity-addresses', entityId]);

      // Atualização otimista: remover o endereço do cache
      queryClient.setQueryData(['entity-addresses', entityId], (old: any[]) => {
        return old ? old.filter(address => address.id !== addressId) : old;
      });

      return { previousAddresses };
    },
    onSuccess: (_data, { entityId, addressId }) => {
      // Invalidar e refetch para garantir dados atualizados
      queryClient.invalidateQueries({ queryKey: ['entity-addresses', entityId] });
      queryClient.refetchQueries({ queryKey: ['entity-addresses', entityId] });

      // Remover o endereço específico do cache
      queryClient.removeQueries({ queryKey: ['entity-addresses', entityId, addressId] });

      // Invalidar também a lista geral de endereços se necessário
      queryClient.invalidateQueries({ queryKey: ['addresses'] });

      toast.success('Endereço excluído com sucesso!');
    },
    onError: (error: any, { entityId }, context) => {
      // Rollback em caso de erro
      if (context?.previousAddresses) {
        queryClient.setQueryData(['entity-addresses', entityId], context.previousAddresses);
      }

      toast.error(
        error.response?.data?.message ||
        'Falha ao excluir endereço. Por favor, tente novamente.'
      );
    },
    onSettled: (data, error, { entityId }) => {
      // Sempre invalidar no final
      queryClient.invalidateQueries({ queryKey: ['entity-addresses', entityId] });
    }
  });
};

// Hook para forçar refresh dos endereços de uma entidade
export const useRefreshEntityAddresses = () => {
  const queryClient = useQueryClient();

  return (entityId: string) => {
    // Remover completamente do cache e refetch
    queryClient.removeQueries({ queryKey: ['entity-addresses', entityId] });
    queryClient.invalidateQueries({ queryKey: ['entity-addresses', entityId] });
    return queryClient.refetchQueries({ queryKey: ['entity-addresses', entityId] });
  };
};
