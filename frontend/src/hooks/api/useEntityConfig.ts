import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { entityConfigService } from '@/services/api/entityConfigService';
import {
  EntityValidationConfig,
  CreateEntityValidationConfigRequest,
  UpdateEntityValidationConfigRequest
} from '@/types/entityConfig';

/**
 * Hook para listar configurações de validação
 */
export const useEntityConfigs = (
  page = 1,
  limit = 10,
  search?: string,
  isActive?: boolean
) => {
  return useQuery({
    queryKey: ['entity-configs', page, limit, search, isActive],
    queryFn: () => entityConfigService.getConfigs(page, limit, search, isActive),
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

/**
 * Hook para obter configuração por ID
 */
export const useEntityConfig = (id: string) => {
  return useQuery({
    queryKey: ['entity-config', id],
    queryFn: () => entityConfigService.getConfigById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook para obter configuração ativa
 */
export const useActiveEntityConfig = () => {
  return useQuery({
    queryKey: ['entity-config', 'active'],
    queryFn: () => entityConfigService.getActiveConfig(),
    staleTime: 10 * 60 * 1000, // 10 minutos - configuração ativa muda raramente
    retry: 1, // Tentar apenas uma vez se falhar
  });
};

/**
 * Hook para obter campos disponíveis
 */
export const useAvailableEntityFields = () => {
  return useQuery({
    queryKey: ['entity-config', 'available-fields'],
    queryFn: () => entityConfigService.getAvailableFields(),
    staleTime: 30 * 60 * 1000, // 30 minutos - campos disponíveis raramente mudam
  });
};

/**
 * Hook para obter estatísticas
 */
export const useEntityConfigStats = () => {
  return useQuery({
    queryKey: ['entity-config', 'stats'],
    queryFn: () => entityConfigService.getConfigStats(),
    staleTime: 15 * 60 * 1000, // 15 minutos
  });
};

/**
 * Hook para criar configuração
 */
export const useCreateEntityConfig = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEntityValidationConfigRequest) => 
      entityConfigService.createConfig(data),
    onSuccess: (newConfig) => {
      queryClient.invalidateQueries({ queryKey: ['entity-configs'] });
      queryClient.invalidateQueries({ queryKey: ['entity-config', 'stats'] });
      toast.success(`Configuração "${newConfig.name}" criada com sucesso!`);
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Erro ao criar configuração';
      toast.error(message);
    },
  });
};

/**
 * Hook para atualizar configuração
 */
export const useUpdateEntityConfig = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEntityValidationConfigRequest }) =>
      entityConfigService.updateConfig(id, data),
    onSuccess: (updatedConfig) => {
      queryClient.invalidateQueries({ queryKey: ['entity-configs'] });
      queryClient.invalidateQueries({ queryKey: ['entity-config', updatedConfig.id] });
      queryClient.invalidateQueries({ queryKey: ['entity-config', 'active'] });
      queryClient.invalidateQueries({ queryKey: ['entity-config', 'stats'] });
      toast.success(`Configuração "${updatedConfig.name}" atualizada com sucesso!`);
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Erro ao atualizar configuração';
      toast.error(message);
    },
  });
};

/**
 * Hook para ativar configuração
 */
export const useActivateEntityConfig = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => entityConfigService.activateConfig(id),
    onSuccess: (activatedConfig) => {
      queryClient.invalidateQueries({ queryKey: ['entity-configs'] });
      queryClient.invalidateQueries({ queryKey: ['entity-config', 'active'] });
      queryClient.invalidateQueries({ queryKey: ['entity-config', 'stats'] });
      toast.success(`Configuração "${activatedConfig.name}" ativada com sucesso!`);
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Erro ao ativar configuração';
      toast.error(message);
    },
  });
};

/**
 * Hook para desativar configuração
 */
export const useDeactivateEntityConfig = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => entityConfigService.deactivateConfig(id),
    onSuccess: (deactivatedConfig) => {
      queryClient.invalidateQueries({ queryKey: ['entity-configs'] });
      queryClient.invalidateQueries({ queryKey: ['entity-config', 'active'] });
      queryClient.invalidateQueries({ queryKey: ['entity-config', 'stats'] });
      toast.success(`Configuração "${deactivatedConfig.name}" desativada com sucesso!`);
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Erro ao desativar configuração';
      toast.error(message);
    },
  });
};

/**
 * Hook para excluir configuração
 */
export const useDeleteEntityConfig = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => entityConfigService.deleteConfig(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['entity-configs'] });
      queryClient.invalidateQueries({ queryKey: ['entity-config', 'active'] });
      queryClient.invalidateQueries({ queryKey: ['entity-config', 'stats'] });
      toast.success('Configuração excluída com sucesso!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Erro ao excluir configuração';
      toast.error(message);
    },
  });
};

/**
 * Hook para duplicar configuração
 */
export const useDuplicateEntityConfig = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, name }: { id: string; name: string }) =>
      entityConfigService.duplicateConfig(id, name),
    onSuccess: (duplicatedConfig) => {
      queryClient.invalidateQueries({ queryKey: ['entity-configs'] });
      queryClient.invalidateQueries({ queryKey: ['entity-config', 'stats'] });
      toast.success(`Configuração "${duplicatedConfig.name}" duplicada com sucesso!`);
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Erro ao duplicar configuração';
      toast.error(message);
    },
  });
};

/**
 * Hook para validar configuração
 */
export const useValidateEntityConfig = () => {
  return useMutation({
    mutationFn: (data: CreateEntityValidationConfigRequest) =>
      entityConfigService.validateConfig(data),
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Erro ao validar configuração';
      toast.error(message);
    },
  });
};
