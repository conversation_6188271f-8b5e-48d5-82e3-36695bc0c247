import api from './axios';
import { 
  Currency, 
  CreateCurrencyRequest, 
  UpdateCurrencyRequest, 
  PaginatedResponse, 
  ApiResponse 
} from '@/types/api';

export const currencyService = {
  // Listar todas as moedas com paginação
  getCurrencies: async (
    page = 1, 
    limit = 10, 
    search?: string
  ): Promise<PaginatedResponse<Currency>> => {
    const params = { page, limit, search };
    const response = await api.get<PaginatedResponse<Currency>>('/currencies', { params });
    return response.data;
  },
  
  // Obter moeda por ID
  getCurrencyById: async (id: string): Promise<Currency> => {
    const response = await api.get<Currency>(`/currencies/${id}`);
    return response.data;
  },
  
  // Obter moeda padrão
  getDefaultCurrency: async (): Promise<Currency> => {
    try {
      // Buscar todas as moedas (aumentar limite para garantir que a padrão seja incluída, se existir)
      const response = await api.get<ApiResponse<PaginatedResponse<Currency>>>('/currencies', { params: { limit: 100 } }); // Considerar um limite maior se necessário
      
      console.log('>>> Raw API response for /currencies:', response); // Log da resposta crua
      console.log('>>> response.data:', response.data); // LOG ADICIONADO PARA INSPEÇÃO

      // --- Validação Temporariamente Removida para Depuração --- 
      // if (!response || !response.data || !Array.isArray(response.data.items)) {
      //   console.error('Estrutura de dados inválida recebida da API de moedas (esperado response.data.items):', response);
      //   throw new Error('Estrutura de dados inválida recebida da API de moedas.');
      // }

      // Tentativa de acesso (pode falhar, mas veremos no log acima)
      const items = response?.data?.data || []; 
      console.log('>>> Tentative Currencies extracted:', items); // Log das moedas extraídas (tentativa)

      if (!Array.isArray(items) || items.length === 0) {
        console.warn('>>> No items array found or items array is empty in response.data');
        throw new Error('Nenhuma moeda encontrada na resposta da API (items vazio ou não encontrado).');
      }
      
      const defaultCurrency = items.find(currency => currency.isDefault === true); // Verificação explícita

      if (!defaultCurrency) {
        console.warn('>>> No default currency found in the list:', items);
        // Lança um erro claro se nenhuma moeda for retornada pela API ou se nenhuma for padrão
        throw new Error('Nenhuma moeda padrão encontrada na resposta da API.');
      }

      return defaultCurrency;
    } catch (error) {
      console.error('Erro detalhado ao buscar moeda padrão:', error); // Log do erro capturado
      // Propaga o erro para ser tratado pelo hook/componente que chamou a função
      if (error instanceof Error) {
         throw new Error(`Falha ao buscar moeda padrão: ${error.message}`);
      }
      throw new Error('Falha ao buscar moeda padrão: erro desconhecido.');
    }
  },
  
  // Criar moeda
  createCurrency: async (data: CreateCurrencyRequest): Promise<Currency> => {
    const response = await api.post<Currency>('/currencies', data);
    return response.data;
  },

  // Atualizar moeda
  updateCurrency: async (id: string, data: UpdateCurrencyRequest): Promise<Currency> => {
    const response = await api.put<Currency>(`/currencies/${id}`, data);
    return response.data;
  },
  
  // Excluir moeda
  deleteCurrency: async (id: string): Promise<void> => {
    await api.delete(`/currencies/${id}`);
  }
};
