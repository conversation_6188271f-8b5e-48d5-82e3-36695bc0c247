import api from './axios';
import { 
  Role, 
  CreateRoleRequest, 
  UpdateRoleRequest, 
  PaginatedResponse, 
  ApiResponse 
} from '@/types/api';

export const roleService = {
  // Listar todos os papéis com paginação
  getRoles: async (
    page = 1, 
    limit = 10, 
    search?: string
  ): Promise<PaginatedResponse<Role>> => {
    const params = { page, limit, search };
    const response = await api.get<PaginatedResponse<Role>>('/roles', { params });
    return response.data;
  },
  
  // Obter papel por ID
  getRoleById: async (id: string): Promise<Role> => {
    const response = await api.get<Role>(`/roles/${id}`);
    return response.data;
  },

  // Criar papel
  createRole: async (data: CreateRoleRequest): Promise<Role> => {
    const response = await api.post<Role>('/roles', data);
    return response.data;
  },
  
  // Atualizar papel
  updateRole: async (id: string, data: UpdateRoleRequest): Promise<Role> => {
    const response = await api.put<Role>(`/roles/${id}`, data);
    return response.data;
  },
  
  // Excluir papel
  deleteRole: async (id: string): Promise<void> => {
    await api.delete(`/roles/${id}`);
  }
};
