import api from './axios';
import {
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  PaginatedResponse,
  ApiResponse
} from '@/types/api';
import { CategoryStatistics } from '@/types/category';

export const categoryService = {
  // Listar todas as categorias com paginação
  getCategories: async (
    page = 1, 
    limit = 10, 
    search?: string,
    type?: string,
    companyId?: string
  ): Promise<PaginatedResponse<Category>> => {
    const params = { page, limit, search, type, companyId };
    const response = await api.get<PaginatedResponse<Category>>('/categories', { params });
    return response.data;
  },
  
  // Obter categoria por ID
  getCategoryById: async (id: string): Promise<Category> => {
    const response = await api.get<Category>(`/categories/${id}`);
    return response.data;
  },
  
  // Criar categoria
  createCategory: async (data: CreateCategoryRequest): Promise<Category> => {
    const response = await api.post<Category>('/categories', data);
    return response.data;
  },

  // Atualizar categoria
  updateCategory: async (id: string, data: UpdateCategoryRequest): Promise<Category> => {
    const response = await api.put<Category>(`/categories/${id}`, data);
    return response.data;
  },
  
  // Excluir categoria
  deleteCategory: async (id: string): Promise<void> => {
    await api.delete(`/categories/${id}`);
  },
  
  // Obter árvore de categorias
  getCategoryTree: async (transactionType?: string): Promise<Category[]> => {
    const params = { transactionType };
    const response = await api.get<Category[]>('/categories/tree', { params });
    return response.data;
  },

  // Obter estatísticas das categorias
  getCategoryStatistics: async (transactionType?: string): Promise<CategoryStatistics[]> => {
    const params = { transactionType };
    const response = await api.get<CategoryStatistics[]>('/categories/statistics', { params });
    return response.data;
  }
};
