import api from './axios';
import { 
  Address, 
  CreateAddressRequest, 
  UpdateAddressRequest, 
  PaginatedResponse, 
  ApiResponse 
} from '@/types/api';

export const addressService = {
  // Listar todos os endereços com paginação
  getAddresses: async (
    page = 1,
    limit = 10,
    search?: string
  ): Promise<PaginatedResponse<Address>> => {
    const params = { page, limit, search };
    const response = await api.get<PaginatedResponse<Address>>('/addresses', { params });
    return response.data;
  },

  // Obter endereço por ID
  getAddressById: async (id: string): Promise<Address> => {
    const response = await api.get<Address>(`/addresses/${id}`);
    return response.data;
  },

  // Criar endereço
  createAddress: async (data: CreateAddressRequest): Promise<Address> => {
    // Mapear neighborhood para district se necessário
    const addressData = {
      ...data,
      district: data.district || data.neighborhood,
      // Remover campos que o backend não aceita diretamente
      neighborhood: undefined,
      entityId: undefined,
      isDefault: undefined,
      id: undefined,
    };

    const response = await api.post<Address>('/addresses', addressData);
    return response.data;
  },
  
  // Atualizar endereço
  updateAddress: async (id: string, data: UpdateAddressRequest): Promise<Address> => {
    // Mapear neighborhood para district se necessário
    const addressData = {
      ...data,
      district: data.district || data.neighborhood,
      // Remover campos que o backend não aceita diretamente
      neighborhood: undefined,
      isDefault: undefined,
    };

    const response = await api.put<Address>(`/addresses/${id}`, addressData);
    return response.data;
  },
  
  // Excluir endereço
  deleteAddress: async (id: string): Promise<void> => {
    await api.delete(`/addresses/${id}`);
  }
};
