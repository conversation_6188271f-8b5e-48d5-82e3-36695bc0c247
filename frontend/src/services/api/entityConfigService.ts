import { api } from './api';
import {
  EntityValidationConfig,
  CreateEntityValidationConfigRequest,
  UpdateEntityValidationConfigRequest,
  EntityFieldConfig
} from '@/types/entityConfig';
import { PaginatedResponse } from '@/types/api';

/**
 * Serviço para gerenciar configurações de validação de entidades
 */
export const entityConfigService = {
  /**
   * Listar todas as configurações de validação
   */
  getConfigs: async (
    page = 1,
    limit = 10,
    search?: string,
    isActive?: boolean
  ): Promise<PaginatedResponse<EntityValidationConfig>> => {
    const params = { page, limit, search, isActive };
    const response = await api.get<PaginatedResponse<EntityValidationConfig>>('/entity-configs', { params });
    return response.data;
  },

  /**
   * Obter configuração por ID
   */
  getConfigById: async (id: string): Promise<EntityValidationConfig> => {
    const response = await api.get<EntityValidationConfig>(`/entity-configs/${id}`);
    return response.data;
  },

  /**
   * Obter configuração ativa da empresa
   */
  getActiveConfig: async (): Promise<EntityValidationConfig | null> => {
    try {
      const response = await api.get<EntityValidationConfig>('/entity-configs/active');
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null; // Nenhuma configuração ativa encontrada
      }
      throw error;
    }
  },

  /**
   * Criar nova configuração
   */
  createConfig: async (data: CreateEntityValidationConfigRequest): Promise<EntityValidationConfig> => {
    const response = await api.post<EntityValidationConfig>('/entity-configs', data);
    return response.data;
  },

  /**
   * Atualizar configuração existente
   */
  updateConfig: async (id: string, data: UpdateEntityValidationConfigRequest): Promise<EntityValidationConfig> => {
    const response = await api.put<EntityValidationConfig>(`/entity-configs/${id}`, data);
    return response.data;
  },

  /**
   * Ativar configuração
   */
  activateConfig: async (id: string): Promise<EntityValidationConfig> => {
    const response = await api.patch<EntityValidationConfig>(`/entity-configs/${id}/activate`);
    return response.data;
  },

  /**
   * Desativar configuração
   */
  deactivateConfig: async (id: string): Promise<EntityValidationConfig> => {
    const response = await api.patch<EntityValidationConfig>(`/entity-configs/${id}/deactivate`);
    return response.data;
  },

  /**
   * Excluir configuração
   */
  deleteConfig: async (id: string): Promise<void> => {
    await api.delete(`/entity-configs/${id}`);
  },

  /**
   * Duplicar configuração
   */
  duplicateConfig: async (id: string, name: string): Promise<EntityValidationConfig> => {
    const response = await api.post<EntityValidationConfig>(`/entity-configs/${id}/duplicate`, { name });
    return response.data;
  },

  /**
   * Obter campos configuráveis disponíveis
   */
  getAvailableFields: async (): Promise<Omit<EntityFieldConfig, 'id' | 'isRequired'>[]> => {
    const response = await api.get<Omit<EntityFieldConfig, 'id' | 'isRequired'>[]>('/entity-configs/available-fields');
    return response.data;
  },

  /**
   * Validar configuração antes de salvar
   */
  validateConfig: async (data: CreateEntityValidationConfigRequest): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> => {
    const response = await api.post<{
      isValid: boolean;
      errors: string[];
      warnings: string[];
    }>('/entity-configs/validate', data);
    return response.data;
  },

  /**
   * Obter estatísticas de uso das configurações
   */
  getConfigStats: async (): Promise<{
    totalConfigs: number;
    activeConfigs: number;
    mostUsedFields: { fieldName: string; usage: number }[];
    configsByCategory: { category: string; count: number }[];
  }> => {
    const response = await api.get('/entity-configs/stats');
    return response.data;
  }
};

/**
 * Hook personalizado para gerenciar configurações de entidade
 */
export const useEntityConfig = () => {
  const getRequiredFieldsFromConfig = (config: EntityValidationConfig | null, personType: 'individual' | 'company') => {
    if (!config) {
      // Retorna configuração padrão se não houver configuração ativa
      return {
        name: true,
        email: false,
        mobile: false,
        stateRegistration: false,
        cnpj: personType === 'company',
        cpf: personType === 'individual'
      };
    }

    const requiredFields: Record<string, boolean> = {};
    
    config.fields.forEach(field => {
      if (field.appliesTo.includes(personType) || field.appliesTo.includes('both')) {
        requiredFields[field.fieldName] = field.isRequired;
      }
    });

    return requiredFields;
  };

  const getFieldConfigByName = (config: EntityValidationConfig | null, fieldName: string): EntityFieldConfig | null => {
    if (!config) return null;
    return config.fields.find(field => field.fieldName === fieldName) || null;
  };

  const isFieldRequired = (config: EntityValidationConfig | null, fieldName: string, personType: 'individual' | 'company'): boolean => {
    const fieldConfig = getFieldConfigByName(config, fieldName);
    if (!fieldConfig) return false;
    
    return fieldConfig.isRequired && 
           (fieldConfig.appliesTo.includes(personType) || fieldConfig.appliesTo.includes('both'));
  };

  const getFieldsByCategory = (config: EntityValidationConfig | null, category: string) => {
    if (!config) return [];
    return config.fields.filter(field => field.category === category);
  };

  return {
    getRequiredFieldsFromConfig,
    getFieldConfigByName,
    isFieldRequired,
    getFieldsByCategory
  };
};
