import api from './axios';

// Exportar cliente HTTP
export { api };

// Exportar todos os serviços
export { authService } from './authService';
export { userService } from './userService';
export { companyService } from './companyService';
export { accountsPayableService } from './accountsPayableService';
export { accountsReceivableService } from './accountsReceivableService';
export { transactionService } from './transactionService';
export { bankAccountService } from './bankAccountService';
export { bankService } from './bankService';
export { currencyService } from './currencyService';
export { categoryService } from './categoryService';
export { entityService } from './entityService';
export { addressService } from './addressService';
export { entityAddressService } from './entityAddressService';
export { zipCodeService } from './zipCodeService';
export { projectService } from './projectService';
export { roleService } from './roleService';
export { permissionService } from './permissionService';
export { customPeriodService } from './customPeriodService';
export { reportService } from './reportService';
export { notificationService } from './notificationService';
