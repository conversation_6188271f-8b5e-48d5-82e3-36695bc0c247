import api from './axios';
import { 
  Bank, 
  CreateBankRequest, 
  UpdateBankRequest, 
  PaginatedResponse, 
  ApiResponse 
} from '@/types/api';

export const bankService = {
  // Listar todos os bancos com paginação
  getBanks: async (
    page = 1,
    limit = 10,
    search?: string
  ): Promise<PaginatedResponse<Bank>> => {
    const params = { page, limit, search };
    const response = await api.get<PaginatedResponse<Bank>>('/banks', { params });
    return response.data;
  },
  
  // Obter banco por ID
  getBankById: async (id: string): Promise<Bank> => {
    const response = await api.get<Bank>(`/banks/${id}`);
    return response.data;
  },

  // Criar banco
  createBank: async (data: CreateBankRequest): Promise<Bank> => {
    const response = await api.post<Bank>('/banks', data);
    return response.data;
  },
  
  // Atualizar banco
  updateBank: async (id: string, data: UpdateBankRequest): Promise<Bank> => {
    const response = await api.put<Bank>(`/banks/${id}`, data);
    return response.data;
  },
  
  // Excluir banco
  deleteBank: async (id: string): Promise<void> => {
    await api.delete(`/banks/${id}`);
  }
};
