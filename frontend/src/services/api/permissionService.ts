import api from './axios';
import { 
  Permission, 
  CreatePermissionRequest, 
  UpdatePermissionRequest, 
  PaginatedResponse, 
  ApiResponse 
} from '@/types/api';

export const permissionService = {
  // Listar todas as permissões com paginação
  getPermissions: async (
    page = 1, 
    limit = 10, 
    search?: string,
    resource?: string,
    action?: string
  ): Promise<PaginatedResponse<Permission>> => {
    const params = { page, limit, search, resource, action };
    const response = await api.get<PaginatedResponse<Permission>>('/permissions', { params });
    return response.data;
  },
  
  // Obter permissão por ID
  getPermissionById: async (id: string): Promise<Permission> => {
    const response = await api.get<Permission>(`/permissions/${id}`);
    return response.data;
  },
  
  // Criar permissão
  createPermission: async (data: CreatePermissionRequest): Promise<Permission> => {
    const response = await api.post<Permission>('/permissions', data);
    return response.data;
  },

  // Atualizar permissão
  updatePermission: async (id: string, data: UpdatePermissionRequest): Promise<Permission> => {
    const response = await api.put<Permission>(`/permissions/${id}`, data);
    return response.data;
  },
  
  // Excluir permissão
  deletePermission: async (id: string): Promise<void> => {
    await api.delete(`/permissions/${id}`);
  }
};
