import api from './axios';
import {
  Address,
  CreateAddressRequest,
  UpdateAddressRequest,
  ApiResponse,
  AddressType
} from '@/types/api';
import { formatCep } from '@/utils/addressUtils';

// Mapeamento de tipos do frontend para nomes no banco de dados
const ADDRESS_TYPE_MAPPING: Record<AddressType, string> = {
  'main': 'Comercial',
  'billing': 'Faturamento',
  'shipping': 'Entrega'
};

// Cache para IDs dos tipos de endereço
let addressTypeCache: Record<string, string> | null = null;

// Função para buscar e cachear os tipos de endereço
const getAddressTypeId = async (type: AddressType): Promise<string | undefined> => {
  try {
    // Se não temos cache, buscar todos os tipos
    if (!addressTypeCache) {
      const response = await api.get('/address-types?limit=100');
      addressTypeCache = {};

      if (response.data?.data) {
        response.data.data.forEach((addressType: any) => {
          addressTypeCache![addressType.name] = addressType.id;
        });
      }
    }

    const typeName = ADDRESS_TYPE_MAPPING[type];
    return addressTypeCache[typeName];
  } catch (error) {
    console.warn('Erro ao buscar tipos de endereço:', error);
    return undefined;
  }
};

export const entityAddressService = {
  // Listar todos os endereços de uma entidade
  getEntityAddresses: async (entityId: string): Promise<Address[]> => {
    const response = await api.get<Address[]>(`/entities/${entityId}/addresses`);
    return response.data;
  },

  // Obter endereço específico de uma entidade
  getEntityAddressById: async (entityId: string, addressId: string): Promise<Address> => {
    const response = await api.get<Address>(`/entities/${entityId}/addresses/${addressId}`);
    return response.data;
  },

  // Criar endereço para uma entidade
  createEntityAddress: async (entityId: string, data: CreateAddressRequest): Promise<Address> => {
    // Buscar o addressTypeId se um tipo foi fornecido
    let addressTypeId: string | undefined;
    if (data.type) {
      try {
        addressTypeId = await getAddressTypeId(data.type);
      } catch (error) {
        console.warn('Erro ao buscar addressTypeId:', error);
      }
    }

    // Mapear campos do frontend para o formato esperado pelo backend
    const addressData = {
      street: data.street.trim(),
      number: data.number?.trim() || '',
      complement: data.complement?.trim() || '',
      district: data.district?.trim() || data.neighborhood?.trim() || '',
      city: data.city.trim(),
      state: data.state.trim().toUpperCase(),
      zipCode: formatCep(data.zipCode.trim()),
      isDefault: data.isDefault || false,
      addressTypeId, // Agora enviamos o UUID correto
    };

    const response = await api.post<Address>(`/entities/${entityId}/addresses`, addressData);
    return response.data;
  },
  
  // Atualizar endereço de uma entidade
  updateEntityAddress: async (entityId: string, addressId: string, data: UpdateAddressRequest): Promise<Address> => {
    // Buscar o addressTypeId se um tipo foi fornecido
    let addressTypeId: string | undefined;
    if (data.type) {
      addressTypeId = await getAddressTypeId(data.type);
    }

    // Mapear campos do frontend para o formato esperado pelo backend
    const addressData = {
      ...(data.street && { street: data.street.trim() }),
      ...(data.number !== undefined && { number: data.number?.trim() || '' }),
      ...(data.complement !== undefined && { complement: data.complement?.trim() || '' }),
      ...(data.district && { district: data.district.trim() }),
      ...(data.neighborhood && { district: data.neighborhood.trim() }),
      ...(data.city && { city: data.city.trim() }),
      ...(data.state && { state: data.state.trim().toUpperCase() }),
      ...(data.zipCode && { zipCode: formatCep(data.zipCode.trim()) }),
      ...(data.isDefault !== undefined && { isDefault: data.isDefault }),
      ...(addressTypeId && { addressTypeId }), // Incluir o UUID correto se disponível
    };



    const response = await api.put<Address>(`/entities/${entityId}/addresses/${addressId}`, addressData);
    return response.data;
  },
  
  // Excluir endereço de uma entidade
  deleteEntityAddress: async (entityId: string, addressId: string): Promise<void> => {
    await api.delete(`/entities/${entityId}/addresses/${addressId}`);
  },

  // Limpar cache dos tipos de endereço (útil para testes ou quando tipos são atualizados)
  clearAddressTypeCache: () => {
    addressTypeCache = null;
  },
};
