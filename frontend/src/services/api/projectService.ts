import api from './axios';
import { 
  Project, 
  CreateProjectRequest, 
  UpdateProjectRequest, 
  PaginatedResponse, 
  ApiResponse 
} from '@/types/api';

export const projectService = {
  // Listar todos os projetos com paginação
  getProjects: async (
    page = 1, 
    limit = 10, 
    search?: string,
    status?: string,
    companyId?: string
  ): Promise<PaginatedResponse<Project>> => {
    const params = { page, limit, search, status, companyId };
    const response = await api.get<PaginatedResponse<Project>>('/projects', { params });
    return response.data;
  },
  
  // Obter projeto por ID
  getProjectById: async (id: string): Promise<Project> => {
    const response = await api.get<Project>(`/projects/${id}`);
    return response.data;
  },

  // Criar projeto
  createProject: async (data: CreateProjectRequest): Promise<Project> => {
    const response = await api.post<Project>('/projects', data);
    return response.data;
  },
  
  // Atualizar projeto
  updateProject: async (id: string, data: UpdateProjectRequest): Promise<Project> => {
    const response = await api.put<Project>(`/projects/${id}`, data);
    return response.data;
  },
  
  // Excluir projeto
  deleteProject: async (id: string): Promise<void> => {
    await api.delete(`/projects/${id}`);
  }
};
