import api from './axios';
import {
  Entity,
  CreateEntityRequest,
  UpdateEntityRequest,
  PaginatedResponse,
  ApiResponse,
  EntityType,
  PersonType,
  EntityStatus
} from '@/types/api';

export const entityService = {
  // Listar todas as entidades com filtros avançados
  getEntities: async (
    page = 1,
    limit = 10,
    search?: string,
    type?: string,
    entityType?: EntityType,
    personType?: PersonType,
    status?: EntityStatus,
    companyId?: string
  ): Promise<PaginatedResponse<Entity>> => {
    const params = {
      page,
      limit,
      search,
      type,
      entityType,
      personType,
      status,
      companyId
    };
    const response = await api.get<PaginatedResponse<Entity>>('/entities', { params });
    return response.data;
  },
  
  // Obter entidade por ID
  getEntityById: async (id: string): Promise<Entity> => {
    const response = await api.get<Entity>(`/entities/${id}`);
    return response.data;
  },
  
  // Criar entidade
  createEntity: async (data: CreateEntityRequest): Promise<Entity> => {
    const response = await api.post<Entity>('/entities', data);
    return response.data;
  },
  
  // Atualizar entidade
  updateEntity: async (id: string, data: UpdateEntityRequest): Promise<Entity> => {
    const response = await api.put<Entity>(`/entities/${id}`, data);
    return response.data;
  },
  
  // Excluir entidade
  deleteEntity: async (id: string): Promise<void> => {
    await api.delete(`/entities/${id}`);
  },
  
  // Obter apenas clientes
  getClients: async (
    page = 1,
    limit = 10,
    search?: string,
    personType?: PersonType,
    status?: EntityStatus,
    companyId?: string
  ): Promise<PaginatedResponse<Entity>> => {
    const params = { page, limit, search, personType, status, companyId };
    const response = await api.get<PaginatedResponse<Entity>>('/entities/clients', { params });
    return response.data;
  },

  // Obter apenas fornecedores
  getSuppliers: async (
    page = 1,
    limit = 10,
    search?: string,
    personType?: PersonType,
    status?: EntityStatus,
    companyId?: string
  ): Promise<PaginatedResponse<Entity>> => {
    const params = { page, limit, search, personType, status, companyId };
    const response = await api.get<PaginatedResponse<Entity>>('/entities/suppliers', { params });
    return response.data;
  },

  // Obter entidades mistas (cliente e fornecedor)
  getMixed: async (
    page = 1,
    limit = 10,
    search?: string,
    personType?: PersonType,
    status?: EntityStatus,
    companyId?: string
  ): Promise<PaginatedResponse<Entity>> => {
    const params = { page, limit, search, personType, status, companyId };
    const response = await api.get<PaginatedResponse<Entity>>('/entities/mixed', { params });
    return response.data;
  },

  // Obter entidades por tipo de pessoa
  getByPersonType: async (
    personType: PersonType,
    page = 1,
    limit = 10,
    search?: string,
    entityType?: EntityType,
    status?: EntityStatus,
    companyId?: string
  ): Promise<PaginatedResponse<Entity>> => {
    const params = { page, limit, search, entityType, status, companyId };
    const response = await api.get<PaginatedResponse<Entity>>(`/entities/person-type/${personType}`, { params });
    return response.data;
  },

  // Obter estatísticas das entidades
  getStatistics: async (): Promise<{
    total: number;
    customers: number;
    suppliers: number;
    mixed: number;
    individuals: number;
    companies: number;
    active: number;
    inactive: number;
  }> => {
    const response = await api.get('/entities/statistics');
    return response.data;
  }
};
