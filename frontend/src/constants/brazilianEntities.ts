/**
 * Brazilian Entity Constants
 * Defines constants and helper functions for Brazilian entity management
 */

import { EntityType, PersonType, EntityStatus, TaxRegime } from '@/types/api';

/**
 * Entity Type Options
 */
export const ENTITY_TYPE_OPTIONS = [
  { value: 'customer' as EntityType, label: 'Cliente', description: 'Apenas cliente' },
  { value: 'supplier' as EntityType, label: 'Fornecedor', description: 'Apenas fornecedor' },
  { value: 'both' as EntityType, label: 'Cliente e Fornecedor', description: 'Cliente e fornecedor' }
];

/**
 * Person Type Options
 */
export const PERSON_TYPE_OPTIONS = [
  { value: 'individual' as PersonType, label: 'Pessoa Física', description: 'CPF obrigatório' },
  { value: 'company' as PersonType, label: 'Pessoa Jurídica', description: 'CNPJ e IE obrigatórios' }
];

/**
 * Entity Status Options
 */
export const ENTITY_STATUS_OPTIONS = [
  { value: 'active' as EntityStatus, label: 'Ativo', color: 'green' },
  { value: 'inactive' as EntityStatus, label: 'Inativo', color: 'gray' },
  { value: 'suspended' as EntityStatus, label: 'Suspenso', color: 'yellow' },
  { value: 'blocked' as EntityStatus, label: 'Bloqueado', color: 'red' }
];

/**
 * Tax Regime Options
 */
export const TAX_REGIME_OPTIONS = [
  { value: 'simple_national' as TaxRegime, label: 'Simples Nacional', description: 'Regime simplificado' },
  { value: 'presumed_profit' as TaxRegime, label: 'Lucro Presumido', description: 'Lucro presumido' },
  { value: 'real_profit' as TaxRegime, label: 'Lucro Real', description: 'Lucro real' },
  { value: 'individual' as TaxRegime, label: 'Pessoa Física', description: 'Para pessoa física' }
];

/**
 * Helper functions for entity management
 */
export class BrazilianEntityHelper {
  /**
   * Get entity type display name
   */
  static getEntityTypeLabel(entityType: EntityType): string {
    const option = ENTITY_TYPE_OPTIONS.find(opt => opt.value === entityType);
    return option ? option.label : entityType;
  }

  /**
   * Get person type display name
   */
  static getPersonTypeLabel(personType: PersonType): string {
    const option = PERSON_TYPE_OPTIONS.find(opt => opt.value === personType);
    return option ? option.label : personType;
  }

  /**
   * Get entity status display name
   */
  static getEntityStatusLabel(status: EntityStatus): string {
    const option = ENTITY_STATUS_OPTIONS.find(opt => opt.value === status);
    return option ? option.label : status;
  }

  /**
   * Get entity status color
   */
  static getEntityStatusColor(status: EntityStatus): string {
    const option = ENTITY_STATUS_OPTIONS.find(opt => opt.value === status);
    return option ? option.color : 'gray';
  }

  /**
   * Get tax regime display name
   */
  static getTaxRegimeLabel(regime: TaxRegime): string {
    const option = TAX_REGIME_OPTIONS.find(opt => opt.value === regime);
    return option ? option.label : regime;
  }

  /**
   * Get default tax regime for person type
   */
  static getDefaultTaxRegime(personType: PersonType): TaxRegime {
    return personType === 'individual' ? 'individual' : 'simple_national';
  }

  /**
   * Check if entity can be both customer and supplier
   */
  static canBeBoth(entityType: EntityType): boolean {
    return entityType === 'both';
  }

  /**
   * Get required fields for person type
   * ATUALIZADO: Removidos campos que não são obrigatórios por padrão
   */
  static getRequiredFields(personType: PersonType): string[] {
    const baseFields = ['name', 'entityType', 'personType'];

    if (personType === 'individual') {
      return [...baseFields, 'cpf'];
    } else {
      return [...baseFields, 'cnpj'];
    }
  }

  /**
   * Get required fields for person type with custom configuration
   * Permite configuração dinâmica de campos obrigatórios
   */
  static getRequiredFieldsWithConfig(personType: PersonType, config?: {
    requireEmail?: boolean;
    requireMobile?: boolean;
    requireStateRegistration?: boolean;
  }): string[] {
    const baseFields = ['name', 'entityType', 'personType'];
    const conditionalFields: string[] = [];

    // Adicionar campos condicionais baseados na configuração
    if (config?.requireEmail) {
      conditionalFields.push('email');
    }
    if (config?.requireMobile) {
      conditionalFields.push('mobile');
    }
    if (config?.requireStateRegistration && personType === 'company') {
      conditionalFields.push('stateRegistration');
    }

    // Adicionar documento obrigatório baseado no tipo de pessoa
    if (personType === 'individual') {
      return [...baseFields, ...conditionalFields, 'cpf'];
    } else {
      return [...baseFields, ...conditionalFields, 'cnpj'];
    }
  }

  /**
   * Get forbidden fields for person type
   */
  static getForbiddenFields(personType: PersonType): string[] {
    if (personType === 'individual') {
      return ['cnpj', 'stateRegistration', 'municipalRegistration', 'tradeName'];
    } else {
      return ['cpf', 'rg', 'rgIssuer'];
    }
  }

  /**
   * Validate entity data consistency
   * ATUALIZADO: Usa configuração flexível de campos obrigatórios
   */
  static validateEntityData(data: any, config?: {
    requireEmail?: boolean;
    requireMobile?: boolean;
    requireStateRegistration?: boolean;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.personType) {
      errors.push('Tipo de pessoa é obrigatório');
      return { isValid: false, errors };
    }

    const requiredFields = this.getRequiredFieldsWithConfig(data.personType, config);
    const forbiddenFields = this.getForbiddenFields(data.personType);

    // Check required fields
    for (const field of requiredFields) {
      if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
        const fieldLabels: Record<string, string> = {
          name: 'Nome',
          entityType: 'Tipo de entidade',
          personType: 'Tipo de pessoa',
          email: 'Email',
          mobile: 'Celular',
          cpf: 'CPF',
          cnpj: 'CNPJ',
          stateRegistration: 'Inscrição Estadual'
        };
        errors.push(`${fieldLabels[field] || field} é obrigatório`);
      }
    }

    // Check forbidden fields (apenas se preenchidos)
    for (const field of forbiddenFields) {
      if (data[field] && typeof data[field] === 'string' && data[field].trim() !== '') {
        const fieldLabels: Record<string, string> = {
          cnpj: 'CNPJ',
          cpf: 'CPF',
          stateRegistration: 'Inscrição Estadual',
          municipalRegistration: 'Inscrição Municipal',
          tradeName: 'Nome Fantasia',
          rg: 'RG',
          rgIssuer: 'Órgão Emissor do RG'
        };
        const personTypeLabel = this.getPersonTypeLabel(data.personType);
        errors.push(`${fieldLabels[field] || field} não é permitido para ${personTypeLabel}`);
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Validate entity data with minimal requirements (backward compatibility)
   * Usa apenas os campos essenciais como obrigatórios
   */
  static validateEntityDataMinimal(data: any): { isValid: boolean; errors: string[] } {
    return this.validateEntityData(data, {
      requireEmail: false,
      requireMobile: false,
      requireStateRegistration: false
    });
  }

  /**
   * Validate entity data with complete requirements
   * Usa configuração mais rigorosa de campos obrigatórios
   */
  static validateEntityDataComplete(data: any): { isValid: boolean; errors: string[] } {
    return this.validateEntityData(data, {
      requireEmail: true,
      requireMobile: true,
      requireStateRegistration: true
    });
  }

  /**
   * Get entity type badge variant
   */
  static getEntityTypeBadgeVariant(entityType: EntityType): 'default' | 'secondary' | 'destructive' | 'outline' {
    switch (entityType) {
      case 'customer':
        return 'default';
      case 'supplier':
        return 'secondary';
      case 'both':
        return 'outline';
      default:
        return 'outline';
    }
  }

  /**
   * Get person type badge variant
   */
  static getPersonTypeBadgeVariant(personType: PersonType): 'default' | 'secondary' {
    return personType === 'individual' ? 'default' : 'secondary';
  }

  /**
   * Get entity status badge variant
   */
  static getEntityStatusBadgeVariant(status: EntityStatus): 'default' | 'secondary' | 'destructive' | 'outline' {
    switch (status) {
      case 'active':
        return 'default';
      case 'inactive':
        return 'secondary';
      case 'suspended':
        return 'outline';
      case 'blocked':
        return 'destructive';
      default:
        return 'outline';
    }
  }

  /**
   * Format entity display name
   */
  static formatEntityDisplayName(entity: any): string {
    if (!entity) return '';
    
    if (entity.personType === 'company' && entity.tradeName) {
      return `${entity.name} (${entity.tradeName})`;
    }
    
    return entity.name;
  }

  /**
   * Format entity document
   */
  static formatEntityDocument(entity: any): string {
    if (!entity) return '';
    
    if (entity.personType === 'individual' && entity.cpf) {
      return entity.cpf;
    }
    
    if (entity.personType === 'company' && entity.cnpj) {
      return entity.cnpj;
    }
    
    return '';
  }

  /**
   * Get entity summary for display
   */
  static getEntitySummary(entity: any): {
    displayName: string;
    document: string;
    type: string;
    status: string;
  } {
    return {
      displayName: this.formatEntityDisplayName(entity),
      document: this.formatEntityDocument(entity),
      type: this.getEntityTypeLabel(entity.entityType || entity.type),
      status: this.getEntityStatusLabel(entity.status || 'active')
    };
  }
}
