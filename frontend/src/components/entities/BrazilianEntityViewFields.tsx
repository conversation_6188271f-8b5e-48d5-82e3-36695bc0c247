import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  PersonType, 
  EntityType,
  Entity 
} from '@/types/api';
import { 
  ENTITY_TYPE_OPTIONS, 
  PERSON_TYPE_OPTIONS, 
  ENTITY_STATUS_OPTIONS, 
  TAX_REGIME_OPTIONS,
  BrazilianEntityHelper 
} from '@/constants/brazilianEntities';

interface BrazilianEntityViewFieldsProps {
  entity: Entity;
}

interface ViewFieldProps {
  label: string;
  value?: string | boolean | null;
  type?: 'text' | 'email' | 'phone' | 'website' | 'boolean' | 'badge';
  badgeVariant?: 'default' | 'secondary' | 'destructive' | 'outline';
}

const ViewField: React.FC<ViewFieldProps> = ({ 
  label, 
  value, 
  type = 'text',
  badgeVariant = 'default'
}) => {
  const renderValue = () => {
    if (value === null || value === undefined || value === '') {
      return <span className="text-muted-foreground">-</span>;
    }

    switch (type) {
      case 'email':
        return (
          <a 
            href={`mailto:${value}`} 
            className="text-blue-600 hover:underline"
          >
            {value}
          </a>
        );
      case 'phone':
        return (
          <a 
            href={`tel:${value}`} 
            className="text-blue-600 hover:underline"
          >
            {value}
          </a>
        );
      case 'website':
        return (
          <a 
            href={value as string} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline"
          >
            {value}
          </a>
        );
      case 'boolean':
        return (
          <Badge variant={value ? 'default' : 'secondary'}>
            {value ? 'Sim' : 'Não'}
          </Badge>
        );
      case 'badge':
        return (
          <Badge variant={badgeVariant}>
            {value}
          </Badge>
        );
      default:
        return <span>{value}</span>;
    }
  };

  return (
    <div className="space-y-1">
      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </label>
      <div className="text-sm text-gray-900 dark:text-gray-100">
        {renderValue()}
      </div>
    </div>
  );
};

export const BrazilianEntityViewFields: React.FC<BrazilianEntityViewFieldsProps> = ({
  entity
}) => {
  const isIndividual = entity.personType === 'individual';
  const isCompany = entity.personType === 'company';

  // Buscar labels das opções
  const getEntityTypeLabel = (value: string) => {
    const option = ENTITY_TYPE_OPTIONS.find(opt => opt.value === value);
    return option ? option.label : value;
  };

  const getPersonTypeLabel = (value: string) => {
    const option = PERSON_TYPE_OPTIONS.find(opt => opt.value === value);
    return option ? option.label : value;
  };

  const getStatusLabel = (value: string) => {
    const option = ENTITY_STATUS_OPTIONS.find(opt => opt.value === value);
    return option ? option.label : value;
  };

  const getTaxRegimeLabel = (value: string) => {
    const option = TAX_REGIME_OPTIONS.find(opt => opt.value === value);
    return option ? option.label : value;
  };

  return (
    <Tabs defaultValue="basic" className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="basic">Dados Básicos</TabsTrigger>
        <TabsTrigger value="documents">Documentos</TabsTrigger>
        <TabsTrigger value="commercial">Comercial</TabsTrigger>
      </TabsList>

      <TabsContent value="basic" className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <ViewField
            label="Tipo de Relacionamento"
            value={getEntityTypeLabel(entity.entityType || '')}
          />
          <ViewField
            label="Tipo de Pessoa"
            value={getPersonTypeLabel(entity.personType || '')}
          />
        </div>

        <ViewField
          label={isIndividual ? 'Nome Completo' : 'Razão Social'}
          value={entity.name}
        />

        {isCompany && (
          <ViewField
            label="Nome Fantasia"
            value={entity.tradeName}
          />
        )}

        <div className="grid grid-cols-2 gap-4">
          <ViewField
            label="Email"
            value={entity.email}
            type="email"
          />
          <ViewField
            label="Status"
            value={getStatusLabel(entity.status || '')}
            type="badge"
            badgeVariant={BrazilianEntityHelper.getEntityStatusBadgeVariant(entity.status || '')}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <ViewField
            label="Telefone"
            value={entity.phone}
            type="phone"
          />
          <ViewField
            label="Celular"
            value={entity.mobile}
            type="phone"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <ViewField
            label="Website"
            value={entity.website}
            type="website"
          />
          <ViewField
            label="Pessoa de Contato"
            value={entity.contact}
          />
        </div>
      </TabsContent>

      <TabsContent value="documents" className="space-y-4">
        {isIndividual ? (
          // Campos para Pessoa Física
          <div className="space-y-4">
            <ViewField
              label="CPF"
              value={entity.cpf}
            />

            <div className="grid grid-cols-2 gap-4">
              <ViewField
                label="RG"
                value={entity.rg}
              />
              <ViewField
                label="Órgão Emissor"
                value={entity.rgIssuer}
              />
            </div>
          </div>
        ) : (
          // Campos para Pessoa Jurídica
          <div className="space-y-4">
            <ViewField
              label="CNPJ"
              value={entity.cnpj}
            />

            <div className="grid grid-cols-2 gap-4">
              <ViewField
                label="Inscrição Estadual"
                value={entity.stateRegistration}
              />
              <ViewField
                label="Inscrição Municipal"
                value={entity.municipalRegistration}
              />
            </div>

            <ViewField
              label="Regime Tributário"
              value={getTaxRegimeLabel(entity.taxRegime || '')}
            />

            <div className="space-y-4">
              <h4 className="text-sm font-medium">Configurações Fiscais</h4>

              <div className="grid grid-cols-2 gap-4">
                <ViewField
                  label="Contribuinte de ICMS"
                  value={entity.icmsTaxpayer}
                  type="boolean"
                />
                <ViewField
                  label="Simples Nacional"
                  value={entity.simpleNational}
                  type="boolean"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <ViewField
                  label="Retém Impostos"
                  value={entity.withholdTaxes}
                  type="boolean"
                />
                <ViewField
                  label="Contribuinte de ISS"
                  value={entity.issTaxpayer}
                  type="boolean"
                />
              </div>
            </div>
          </div>
        )}
      </TabsContent>

      <TabsContent value="commercial" className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <ViewField
            label="Limite de Crédito"
            value={entity.creditLimit ? `R$ ${entity.creditLimit.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : undefined}
          />
          <ViewField
            label="Prazo de Pagamento (dias)"
            value={entity.paymentTermDays?.toString()}
          />
        </div>

        <ViewField
          label="Observações"
          value={entity.notes}
        />

        <div className="space-y-4">
          <h4 className="text-sm font-medium">Informações Adicionais</h4>
          
          <div className="grid grid-cols-2 gap-4">
            <ViewField
              label="Data de Cadastro"
              value={entity.createdAt ? new Date(entity.createdAt).toLocaleDateString('pt-BR') : undefined}
            />
            <ViewField
              label="Última Atualização"
              value={entity.updatedAt ? new Date(entity.updatedAt).toLocaleDateString('pt-BR') : undefined}
            />
          </div>
        </div>
      </TabsContent>
    </Tabs>
  );
};
