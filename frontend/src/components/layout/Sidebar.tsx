import { cn } from "@/lib/utils";
import {
  BarChart3,
  Briefcase,
  Building2,
  Calendar,
  CalendarDays,
  CalendarRange,
  Check,
  ChevronDown,
  CreditCard,
  FileInput,
  FileOutput,
  FileText,
  Home,
  Landmark,
  MapPin,
  PieChart,
  Receipt,
  Settings,
  Tags,
  Truck,
  UserCircle,
  Users,
  Wrench,
  X,
  Gauge
} from "lucide-react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { ReactNode, useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useIsMobile } from "@/hooks/use-mobile";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useAuth } from "@/contexts/AuthContext";

interface SidebarProps {
  isOpen: boolean;
  className?: string;
}

interface SidebarItemProps {
  icon: ReactNode;
  label: string;
  isActive?: boolean;
  to?: string;
  onClick?: () => void;
}

// Interface local para o seletor de empresas
interface SidebarCompany {
  id: string;
  name: string;
  calendarType?: string;
}

const SidebarItem = ({ icon, label, isActive = false, to, onClick }: SidebarItemProps) => {
  const content = (
    <div className="contents">
      {icon}
      <span className="text-sm">{label}</span>
    </div>
  );

  return to ? (
    <Link
      to={to}
      className={cn("sidebar-item", isActive && "sidebar-item-active")}
    >
      {content}
    </Link>
  ) : (
    <button
      className={cn("sidebar-item", isActive && "sidebar-item-active")}
      onClick={onClick}
    >
      {content}
    </button>
  );
};

const SidebarCollapsibleItem = ({
  icon,
  label,
  children,
  isActive = false,
  isOpen,
  onToggle
}: {
  icon: ReactNode;
  label: string;
  children: ReactNode;
  isActive?: boolean;
  isOpen: boolean;
  onToggle: () => void;
}) => {
  return (
    <Collapsible open={isOpen} onOpenChange={onToggle} className="w-full">
      <CollapsibleTrigger className="w-full">
        <div className={cn("sidebar-item justify-between", isActive && "sidebar-item-active")}>
          <div className="flex items-center gap-2">
            {icon}
            <span className="text-sm">{label}</span>
          </div>
          <ChevronDown className={cn("h-4 w-4 transition-transform", isOpen && "rotate-180")} />
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent className="pl-9 space-y-1 pt-1">
        {children}
      </CollapsibleContent>
    </Collapsible>
  );
};

const CompanySelector = ({ selectedCompany, companies, onCompanySelect }: {
  selectedCompany: SidebarCompany | null;
  companies: SidebarCompany[];
  onCompanySelect: (company: SidebarCompany) => void;
}) => {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const navigate = useNavigate();

  const handleCompanySelect = (company: SidebarCompany) => {
    onCompanySelect(company);
    setIsPopoverOpen(false);
  };

  const handleManageCompanies = () => {
    setIsPopoverOpen(false);
    navigate("/settings/companies");
  };

  // Se não há empresa selecionada, não renderizar
  if (!selectedCompany) {
    return null;
  }

  return (
    <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          className="h-auto w-full px-3 py-2 gap-2 text-sm flex items-center justify-between cursor-pointer rounded-lg bg-gradient-to-r from-purple-50/70 to-blue-50/70 hover:from-purple-100/80 hover:to-blue-100/80 dark:from-slate-800/70 dark:to-slate-700/60 dark:hover:from-slate-800 dark:hover:to-slate-700 border border-slate-200/70 dark:border-slate-700/70 text-slate-800 dark:text-slate-200 transition-all"
        >
          <div className="flex items-center gap-2">
            {selectedCompany.calendarType === "days" ? (
              <div className="flex items-center justify-center h-6 w-6 rounded-full bg-blue-500/10 text-blue-600">
                <CalendarDays className="h-3.5 w-3.5" strokeWidth={1.5} />
              </div>
            ) : (
              <div className="flex items-center justify-center h-6 w-6 rounded-full bg-orange-500/10 text-orange-500">
                <CalendarRange className="h-3.5 w-3.5" strokeWidth={1.5} />
              </div>
            )}
            <span className="truncate font-medium">{selectedCompany.name}</span>
          </div>
          <ChevronDown className="h-3.5 w-3.5 opacity-70" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-0 shadow-lg rounded-md border border-gray-100">
        <div className="p-2.5 pb-2 border-b border-gray-50">
          <h3 className="text-sm font-semibold text-gray-700">Empresas</h3>
        </div>
        <div className="py-1 max-h-72 overflow-y-auto scrollbar-thin">
          {companies.map((company) => (
            <div
              key={company.id}
              className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer"
              onClick={() => handleCompanySelect(company)}
            >
              <div className="flex items-center gap-2">
                {selectedCompany && selectedCompany.id === company.id ? (
                  <Check className="h-4 w-4 text-gray-800 min-w-4" />
                ) : (
                  <div className="w-4 min-w-4" />
                )}
                <span className="text-sm text-gray-800 font-medium">{company.name}</span>
              </div>
              {company.calendarType === "days" ? (
                <CalendarDays className="h-4 w-4 text-blue-600" strokeWidth={1.5} />
              ) : (
                <CalendarRange className="h-4 w-4 text-orange-500" strokeWidth={1.5} />
              )}
            </div>
          ))}
        </div>
        <div className="border-t border-gray-50">
          <div
            className="flex items-center gap-2 p-2.5 text-xs font-medium text-gray-700 hover:bg-gray-50 cursor-pointer"
            onClick={handleManageCompanies}
          >
            <Building2 className="h-3.5 w-3.5" />
            <span>Gerenciar Empresas</span>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

const Sidebar = ({ isOpen, className }: SidebarProps) => {
  const location = useLocation();
  const isMobile = useIsMobile();
  const { companies, activeCompanyId, switchCompany, loading, initialized } = useAuth();
  const [selectedCompany, setSelectedCompany] = useState<SidebarCompany | null>(null);

  // Atualizar a empresa selecionada quando o activeCompanyId ou companies mudarem
  useEffect(() => {
    // Se ainda está carregando ou não foi inicializado, não fazer nada
    if (loading || !initialized) {
      return;
    }

    // Se temos companies e activeCompanyId, encontrar a empresa ativa
    if (companies && companies.length > 0 && activeCompanyId) {
      const active = companies.find(company => company.id === activeCompanyId);
      if (active) {
        setSelectedCompany({
          id: active.id,
          name: active.name,
          calendarType: active.calendarType || 'days'
        });
      } else {
        // Se não encontrou a empresa ativa, usar a primeira disponível
        const firstCompany = companies[0];
        setSelectedCompany({
          id: firstCompany.id,
          name: firstCompany.name,
          calendarType: firstCompany.calendarType || 'days'
        });
      }
    } else if (companies && companies.length === 0) {
      // Se não há empresas, limpar o estado
      setSelectedCompany(null);
    }
  }, [companies, activeCompanyId, loading, initialized]);

  const [entitiesOpen, setEntitiesOpen] = useState(
    location.pathname === '/customers' || location.pathname === '/suppliers'
  );

  const [utilitiesOpen, setUtilitiesOpen] = useState(
    location.pathname === '/addresses' ||
    location.pathname === '/postal-codes' ||
    location.pathname === '/diagnostico'
  );

  // Note: managementOpen is not needed anymore since we removed the collapsible submenu
  // but we keep entitiesOpen for the Entidades submenu

  const handleCompanySelect = (company: SidebarCompany) => {
    switchCompany(company.id);
  };

  if (isMobile && !isOpen) {
    return null;
  }

  return (
    <div className="contents">
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-20"
          onClick={() => {}} // This will be handled by Layout's toggle function
        />
      )}

      <aside
        className={cn(
          "fixed inset-y-0 left-0 w-64 bg-sidebar flex flex-col border-r border-sidebar-border transition-all duration-300 ease-in-out z-30",
          !isOpen && "lg:w-20",
          isMobile && isOpen && "translate-x-0",
          isMobile && !isOpen && "-translate-x-full",
          !isMobile && !isOpen && "lg:-translate-x-0 lg:w-20",
          className
        )}
      >
        <div className="h-16 border-b border-sidebar-border flex items-center justify-between px-4">
          <div className={cn(
            "h-full flex items-center",
            !isOpen && !isMobile && "lg:opacity-0"
          )}>
            <div className="flex items-center">
              <span className="text-xl font-medium">Fluxo</span>
              <span className="text-xl font-medium text-primary">Max</span>
            </div>
          </div>

          {isMobile && (
            <Button variant="ghost" size="sm" className="h-8 w-8" onClick={() => {}}> {/* Will be handled by Layout */}
              <X className="h-4 w-4" />
            </Button>
          )}

          <div className={cn(
            "h-full w-full flex items-center justify-center transition-opacity duration-300 hidden",
            !isOpen && !isMobile && "lg:flex lg:flex-col"
          )}>
            <div className="flex items-center">
              {selectedCompany && selectedCompany.calendarType === "days" ?
                <CalendarDays className="h-5 w-5 text-blue-600" strokeWidth={1.5} /> :
                <CalendarRange className="h-5 w-5 text-orange-500" strokeWidth={1.5} />
              }
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-3">
          <div className="mb-6 mt-2">
            {loading || !initialized || !selectedCompany ? (
              <div className="flex items-center justify-center p-3 bg-sidebar-accent rounded-lg">
                <div className="flex items-center gap-2">
                  <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                  <span className="text-sm text-sidebar-foreground/70">Carregando empresas...</span>
                </div>
              </div>
            ) : (
              <CompanySelector
                selectedCompany={selectedCompany}
                companies={companies?.map(company => ({
                  id: company.id,
                  name: company.name,
                  calendarType: company.calendarType || 'days'
                })) || []}
                onCompanySelect={handleCompanySelect}
              />
            )}
          </div>

          <div className="sidebar-section">
            <p className={cn("text-xs font-medium text-sidebar-foreground/70 mb-2 ml-2 transition-opacity duration-300", !isOpen && !isMobile && "lg:opacity-0")}>
              Painel
            </p>
            <nav className="flex flex-col">
              <SidebarItem
                icon={<Home className="h-5 w-5" />}
                label="Visão Geral"
                isActive={location.pathname === '/'}
                to="/"
              />
              <SidebarItem
                icon={<BarChart3 className="h-5 w-5" />}
                label="Análises"
                isActive={location.pathname === '/analytics'}
                to="/analytics"
              />
              <SidebarItem icon={<Calendar className="h-5 w-5" />} label="Calendário" />
            </nav>
          </div>

          <div className="sidebar-section">
            <p className={cn("text-xs font-medium text-sidebar-foreground/70 mb-2 ml-2 transition-opacity duration-300", !isOpen && !isMobile && "lg:opacity-0")}>
              Financeiro
            </p>
            <nav className="flex flex-col">
              <SidebarItem
                icon={<Receipt className="h-5 w-5" />}
                label="Transações"
                isActive={location.pathname === '/transactions'}
                to="/transactions"
              />
              <SidebarItem
                icon={<CreditCard className="h-5 w-5" />}
                label="Contas"
                isActive={location.pathname === '/bank-accounts'}
                to="/bank-accounts"
              />
              <SidebarItem
                icon={<FileOutput className="h-5 w-5" />}
                label="A Pagar"
                isActive={location.pathname === '/accounts-payable'}
                to="/accounts-payable"
              />
              <SidebarItem
                icon={<FileInput className="h-5 w-5" />}
                label="A Receber"
                isActive={location.pathname === '/accounts-receivable'}
                to="/accounts-receivable"
              />
            </nav>
          </div>

          <div className="sidebar-section">
            <p className={cn("text-xs font-medium text-sidebar-foreground/70 mb-2 ml-2 transition-opacity duration-300", !isOpen && !isMobile && "lg:opacity-0")}>
              Gerenciamento
            </p>
            <nav className="flex flex-col">
              <SidebarItem
                icon={<Briefcase className="h-5 w-5" />}
                label="Projetos"
                isActive={location.pathname === '/projects'}
                to="/projects"
              />

              <SidebarCollapsibleItem
                icon={<Users className="h-5 w-5" />}
                label="Entidades"
                isActive={location.pathname === '/customers' || location.pathname === '/suppliers'}
                isOpen={entitiesOpen}
                onToggle={() => setEntitiesOpen(!entitiesOpen)}
              >
                <SidebarItem
                  icon={<UserCircle className="h-4 w-4" />}
                  label="Clientes"
                  isActive={location.pathname === '/customers'}
                  to="/customers"
                />
                <SidebarItem
                  icon={<Truck className="h-4 w-4" />}
                  label="Fornecedores"
                  isActive={location.pathname === '/suppliers'}
                  to="/suppliers"
                />
              </SidebarCollapsibleItem>

              <SidebarItem
                icon={<Landmark className="h-5 w-5" />}
                label="Bancos"
                isActive={location.pathname === '/banks'}
                to="/banks"
              />

              <SidebarItem
                icon={<Tags className="h-5 w-5" />}
                label="Categorias"
                isActive={location.pathname === '/categories'}
                to="/categories"
              />

              <SidebarItem icon={<PieChart className="h-5 w-5" />} label="Relatórios" />
            </nav>
          </div>

          <div className="sidebar-section">
            <p className={cn("text-xs font-medium text-sidebar-foreground/70 mb-2 ml-2 transition-opacity duration-300", !isOpen && !isMobile && "lg:opacity-0")}>
              Utilidades
            </p>
            <nav className="flex flex-col">
              <SidebarCollapsibleItem
                icon={<Wrench className="h-5 w-5" />}
                label="Utilidades"
                isActive={
                  location.pathname === '/addresses' ||
                  location.pathname === '/postal-codes' ||
                  location.pathname === '/diagnostico'
                }
                isOpen={utilitiesOpen}
                onToggle={() => setUtilitiesOpen(!utilitiesOpen)}
              >
                <SidebarItem
                  icon={<MapPin className="h-4 w-4" />}
                  label="Endereços"
                  isActive={location.pathname === '/addresses'}
                  to="/addresses"
                />
                <SidebarItem
                  icon={<FileText className="h-4 w-4" />}
                  label="CEPs"
                  isActive={location.pathname === '/postal-codes'}
                  to="/postal-codes"
                />
                <SidebarItem
                  icon={<Gauge className="h-4 w-4" />}
                  label="Diagnóstico"
                  isActive={location.pathname === '/diagnostico'}
                  to="/diagnostico"
                />
              </SidebarCollapsibleItem>
            </nav>
          </div>
        </div>

        <div className="p-3 border-t border-sidebar-border">
          <SidebarItem
            icon={<Settings className="h-5 w-5" />}
            label="Configurações"
            isActive={location.pathname.startsWith('/settings')}
            to="/settings"
          />
        </div>
      </aside>
    </div>
  );
};

export default Sidebar;
