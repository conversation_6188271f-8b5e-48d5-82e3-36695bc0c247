import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { logger } from "@/utils/secureLogger";
import { AddressSection } from "@/components/address";
import { Address, AddressType, CreateAddressRequest } from "@/types/api";
import { useEntityAddresses } from "@/hooks/api/useEntityAddresses";
import { mapBackendAddressType } from "@/utils/addressUtils";
import { toast } from 'sonner';

const customerSchema = z.object({
  name: z.string().min(2, { message: "Nome deve ter pelo menos 2 caracteres" }),
  email: z.string().email({ message: "Email inválido" }).optional().or(z.literal("")),
  phone: z.string().optional(),
  contact: z.string().optional(),
});

type CustomerFormValues = z.infer<typeof customerSchema>;

interface CustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: CustomerFormValues & { type: string; addresses?: Address[] }) => void;
  customer?: any;
}

const CustomerModal = ({ isOpen, onClose, onSave, customer }: CustomerModalProps) => {
  const [isSaving, setIsSaving] = useState(false);
  const [addresses, setAddresses] = useState<(Address & { type?: AddressType })[]>([]);
  const companyId = localStorage.getItem('activeCompanyId') || "";

  // Hook para carregar endereços da entidade quando estiver editando
  // Usar staleTime menor para garantir dados atualizados em modais
  const { data: entityAddresses, isLoading: loadingAddresses, refetch: refetchAddresses } = useEntityAddresses(
    customer?.id || '',
    {
      staleTime: 0, // Sempre buscar dados frescos em modais
      refetchOnMount: true,
      refetchOnWindowFocus: true
    }
  );

  const form = useForm<CustomerFormValues>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      contact: "",
    },
  });

  useEffect(() => {
    if (isOpen) {
      if (customer) {
        form.reset({
          name: customer.name,
          email: customer.email,
          phone: customer.phone,
          contact: customer.contact,
        });
        // Não definir endereços aqui - será feito no próximo useEffect
      } else {
        form.reset({
          name: "",
          email: "",
          phone: "",
          contact: "",
        });
        setAddresses([]);
      }
    }
  }, [isOpen, customer, form]);

  // Carregar endereços quando os dados da API chegarem ou quando o modal for aberto
  useEffect(() => {
    if (customer && entityAddresses !== undefined) {
      // Mapear endereços da API para o formato esperado pelo frontend
      const mappedAddresses = entityAddresses.map(addr => ({
        ...addr,
        district: addr.district || addr.neighborhood || '',
        neighborhood: addr.neighborhood || addr.district || '',
        type: mapBackendAddressType((addr as any).addressType),
      }));
      setAddresses(mappedAddresses);
    } else if (!customer) {
      setAddresses([]);
    }
  }, [customer, entityAddresses, loadingAddresses, isOpen]);

  // Funções para gerenciar endereços
  const handleAddAddress = (addressData: CreateAddressRequest) => {
    // Verificar se todos os campos obrigatórios estão presentes
    if (!addressData.type) {
      console.error('Tipo de endereço não definido:', addressData);
      toast.error('Tipo de endereço é obrigatório');
      return;
    }

    if (!addressData.district) {
      console.error('Distrito/bairro não definido:', addressData);
      toast.error('Bairro é obrigatório');
      return;
    }

    if (!addressData.street || !addressData.city || !addressData.state || !addressData.zipCode) {
      console.error('Campos obrigatórios não preenchidos:', addressData);
      toast.error('Preencha todos os campos obrigatórios do endereço');
      return;
    }

    const newAddress: Address & { type?: AddressType } = {
      id: `temp-${Date.now()}`, // ID temporário
      street: addressData.street,
      number: addressData.number || '',
      complement: addressData.complement || '',
      district: addressData.district,
      city: addressData.city,
      state: addressData.state,
      zipCode: addressData.zipCode,
      country: addressData.country || 'Brasil',
      type: addressData.type,
      isDefault: addressData.isDefault || false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Se é padrão, remove padrão dos outros
    if (newAddress.isDefault) {
      setAddresses(prev => prev.map(addr => ({ ...addr, isDefault: false })));
    }

    setAddresses(prev => [...prev, newAddress]);
    toast.success('Endereço adicionado com sucesso!');
  };

  const handleUpdateAddress = (id: string, updates: Partial<Address>) => {
    setAddresses(prev => prev.map(addr =>
      addr.id === id ? { ...addr, ...updates } : addr
    ));
  };

  const handleDeleteAddress = (id: string) => {
    setAddresses(prev => prev.filter(addr => addr.id !== id));
  };

  const handleSetDefaultAddress = (id: string) => {
    setAddresses(prev => prev.map(addr => ({
      ...addr,
      isDefault: addr.id === id
    })));
  };

  const handleSubmit = async (data: CustomerFormValues) => {
    setIsSaving(true);

    try {
      onSave({ ...data, type: "customer", addresses });
    } catch (error) {
      logger.error("Erro ao salvar cliente", error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[650px]">
        <DialogHeader>
          <DialogTitle>{customer ? "Editar Cliente" : "Novo Cliente"}</DialogTitle>
          <DialogDescription>Preencha as informações do cliente e seus endereços</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome</FormLabel>
                    <FormControl>
                      <Input placeholder="Nome do cliente" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Telefone</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="(00) 0000-0000"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value.replace(/[^\d]/g, '');
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="contact"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome do Contato</FormLabel>
                    <FormControl>
                      <Input placeholder="Nome da pessoa de contato" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <AddressSection
              addresses={addresses}
              onAddAddress={handleAddAddress}
              onUpdateAddress={handleUpdateAddress}
              onDeleteAddress={handleDeleteAddress}
              onSetDefaultAddress={handleSetDefaultAddress}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancelar
              </Button>

              <Button type="submit" disabled={isSaving}>
                {isSaving ? "Salvando..." : "Salvar"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default CustomerModal;
