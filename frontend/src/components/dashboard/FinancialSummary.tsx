import { ArrowDown, Wallet, CreditCard, DollarSign, TrendingUp, AlertTriangle, CalendarClock, Loader2 } from 'lucide-react'; 
import DashboardCard from './DashboardCard';
import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query'; 
import { accountsPayableService } from '@/services/api/accountsPayableService';
import { AccountsPayableSummary, ApiResponse } from '@/types/api';

interface FinancialSummaryProps {
  selectedPeriod: string;
  selectedAccountId: string;
}

const FinancialSummary = ({ selectedPeriod, selectedAccountId }: FinancialSummaryProps) => {
  // Buscar dados do resumo de Contas a Pagar
  const { data: summaryData, isLoading: isLoadingSummary, error: summaryError } = useQuery<
    ApiResponse<AccountsPayableSummary>,
    Error,
    AccountsPayableSummary // Select data directly
  >({
    queryKey: ['accountsPayableSummary'], // Query key as part of options
    queryFn: () => accountsPayableService.getAccountsPayableSummary(), // Fetcher function
    select: (response) => response.data, // Extrai os dados da ApiResponse
    staleTime: 1000 * 60 * 5, // Cache de 5 minutos
  });

  // Formatar valores de moeda
  const formatCurrency = (value: number | undefined | null) => {
    if (value === undefined || value === null) return "--"; // Handle undefined/null
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  // Obter a descrição do período para o subtítulo
  const getPeriodDescription = () => {
    const date = new Date();
    switch(selectedPeriod) {
      case 'today':
        return `Hoje, ${date.toLocaleDateString('pt-BR')}`;
      case 'current-week':
        return 'Esta Semana';
      case 'next-week':
        return 'Próxima Semana';
      case 'current-month':
        return `${date.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })}`;
      case 'next-month':
        date.setMonth(date.getMonth() + 1);
        return `${date.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })}`;
      case 'previous-month':
        date.setMonth(date.getMonth() - 1);
        return `${date.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })}`;
      case 'last-3-months':
        return 'Últimos 3 Meses';
      case 'current-year':
        return `${date.getFullYear()}`;
      default:
        return `Atualizado em ${date.toLocaleDateString('pt-BR')}`;
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
      {/* Card 1: Saldo Total - Removido dados mockados */}
      <div className="animate-scale-in" style={{ animationDelay: '0.1s' }}>
        <DashboardCard
          title="Saldo Total"
          value="--" // Removido valor mockado
          valueColor="text-gray-500"
          icon={<DollarSign className="h-5 w-5" />}
          subtitle="Aguardando dados reais"
          isLoading={false}
          hasError={false}
        />
      </div>

      {/* Card 2: Em Atraso (Contas a Pagar) */}
      <div className="animate-scale-in" style={{ animationDelay: '0.2s' }}>
        <DashboardCard
          title="Em Atraso"
          value={formatCurrency(summaryData?.totalOverdue)} // Passar valor formatado
          valueColor={(summaryData?.totalOverdue ?? 0) > 0 ? 'text-orange-500' : 'text-gray-500'}
          icon={<AlertTriangle className="h-5 w-5" />}
          subtitle="Contas a Pagar Vencidas"
          isLoading={isLoadingSummary} // Passar isLoading
          hasError={!!summaryError} // Passar hasError
        />
      </div>

      {/* Card 3: Total a Pagar (Contas a Pagar) */}
      <div className="animate-scale-in" style={{ animationDelay: '0.3s' }}>
        <DashboardCard
          title="Total a Pagar"
          value={formatCurrency(summaryData?.totalDue)} // Passar valor formatado
          valueColor={'text-red-500'} // Cor fixa
          icon={<ArrowDown className="h-5 w-5" />}
          subtitle="Próximos 30 dias"
          isLoading={isLoadingSummary} // Passar isLoading
          hasError={!!summaryError} // Passar hasError
        />
      </div>

      {/* Card 4: A Vencer (Contas a Pagar) */}
      <div className="animate-scale-in" style={{ animationDelay: '0.4s' }}>
        <DashboardCard
          title="A Vencer"
          value={formatCurrency(summaryData?.totalUpcoming)} // Passar valor formatado
          valueColor={'text-yellow-500'} // Cor fixa
          icon={<CalendarClock className="h-5 w-5" />}
          subtitle="Próximos 30 dias"
          isLoading={isLoadingSummary} // Passar isLoading
          hasError={!!summaryError} // Passar hasError
        />
      </div>
    </div>
  );
};

export default FinancialSummary;
