import { ArrowDown, <PERSON>R<PERSON>, ArrowUp } from "lucide-react";
import GlassC<PERSON> from "../ui-custom/GlassCard";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

interface Transaction {
  id: string;
  description: string;
  amount: number;
  date: string;
  type: "income" | "expense";
}

interface RecentTransactionsProps {
  selectedPeriod: string;
  selectedAccountId: string;
}

const RecentTransactions = ({ selectedPeriod, selectedAccountId }: RecentTransactionsProps) => {
  // Removido dados mockados - componente agora mostra estado vazio
  const transactions: Transaction[] = [];
  
  // Função para formatar moeda
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(amount);
  };
  
  // Função para formatar data
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR');
  };
  
  return (
    <GlassCard className="h-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Transações Recentes</h2>
        <Link to="/transactions" className="text-xs text-primary font-medium hover:underline">
          Ver Todas
        </Link>
      </div>
      
      <div className="space-y-3">
        {transactions.length > 0 ? (
          transactions.map((transaction) => (
            <div
              key={transaction.id}
              className="flex items-center p-3 bg-white/30 dark:bg-white/5 rounded-lg border border-border"
            >
              <div className={`h-8 w-8 rounded-full flex items-center justify-center mr-3 ${
                transaction.type === "income" ? "bg-green-100 text-green-600" : "bg-red-100 text-red-600"
              }`}>
                {transaction.type === "income" ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
              </div>

              <div className="flex-1">
                <p className="text-sm font-medium">{transaction.description}</p>
                <p className="text-xs text-muted-foreground">{formatDate(transaction.date)}</p>
              </div>

              <p className={`text-sm font-semibold ${
                transaction.type === "income" ? "text-green-600" : "text-red-600"
              }`}>
                {formatCurrency(Math.abs(transaction.amount))}
              </p>

              <ArrowRight className="h-4 w-4 ml-2 text-muted-foreground" />
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <div className="flex flex-col items-center gap-2">
              <ArrowRight className="h-8 w-8 text-muted-foreground/50" />
              <p className="text-sm">Nenhuma transação registrada ainda</p>
              <p className="text-xs">As transações aparecerão aqui quando forem criadas</p>
            </div>
          </div>
        )}
      </div>
    </GlassCard>
  );
};

export default RecentTransactions;
