import { useEffect, useState } from "react";
import { Bank } from "@/types/bank";
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON><PERSON>ooter, <PERSON>alogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { X, Upload, Camera } from "lucide-react";
import { cn } from "@/lib/utils";

interface BankModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (data: Partial<Bank>) => void;
  bank?: Bank;
}

const bankFormSchema = z.object({
  code: z.string()
    .min(3, "O código deve ter pelo menos 3 caracteres")
    .max(5, "O código deve ter no máximo 5 caracteres")
    .regex(/^\d+$/, "O código deve conter apenas números"),
  name: z.string()
    .min(3, "O nome deve ter pelo menos 3 caracteres")
    .max(100, "O nome deve ter no máximo 100 caracteres"),
  logo: z.string().nullable().optional().or(z.literal(''))
});

type BankFormValues = z.infer<typeof bankFormSchema>;

export function BankModal({ open, onOpenChange, onSave, bank }: BankModalProps) {
  const [logoPreview, setLogoPreview] = useState<string | undefined>(bank?.logo || undefined);
  const [isDragging, setIsDragging] = useState(false);

  const form = useForm<BankFormValues>({
    resolver: zodResolver(bankFormSchema),
    defaultValues: {
      code: bank?.code || '',
      name: bank?.name || '',
      logo: bank?.logo || ''
    }
  });

  useEffect(() => {
    if (bank) {
      form.reset({
        code: bank.code,
        name: bank.name,
        logo: bank.logo || ''
      });
      setLogoPreview(bank.logo || undefined);
    } else {
      form.reset({
        code: '',
        name: '',
        logo: ''
      });
      setLogoPreview(undefined);
    }
  }, [bank, form]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    handleFile(file);
  };

  const resizeImage = (file: File, maxWidth: number = 200, maxHeight: number = 200, quality: number = 0.8): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calcular novas dimensões mantendo a proporção
        let { width, height } = img;

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Desenhar a imagem redimensionada
        ctx?.drawImage(img, 0, 0, width, height);

        // Converter para base64 com qualidade reduzida
        const base64String = canvas.toDataURL('image/jpeg', quality);
        resolve(base64String);
      };

      img.onerror = () => reject(new Error('Erro ao carregar a imagem'));
      img.src = URL.createObjectURL(file);
    });
  };

  const handleFile = async (file?: File) => {
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      form.setError('logo', {
        message: "Por favor, selecione apenas arquivos de imagem."
      });
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      form.setError('logo', {
        message: "A imagem deve ter no máximo 5MB."
      });
      return;
    }

    try {
      // Redimensionar a imagem para reduzir o tamanho do payload
      const resizedBase64 = await resizeImage(file, 200, 200, 0.8);

      // Verificar se a imagem redimensionada ainda é muito grande
      const sizeInBytes = (resizedBase64.length * 3) / 4; // Aproximação do tamanho em bytes
      if (sizeInBytes > 2 * 1024 * 1024) { // 2MB limite para base64
        form.setError('logo', {
          message: "A imagem é muito grande mesmo após redimensionamento. Tente uma imagem menor."
        });
        return;
      }

      setLogoPreview(resizedBase64);
      form.setValue('logo', resizedBase64);
      form.clearErrors('logo');
    } catch (error) {
      form.setError('logo', {
        message: "Erro ao processar a imagem. Tente novamente."
      });
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);

    if (event.dataTransfer.files?.length) {
      handleFile(event.dataTransfer.files[0]);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const removeLogo = () => {
    setLogoPreview(undefined);
    form.setValue('logo', '');
  };

  const handleSubmit = form.handleSubmit((data) => {
    onSave({
      code: data.code,
      name: data.name,
      logo: data.logo || undefined
    });
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{bank ? 'Editar Banco' : 'Novo Banco'}</DialogTitle>
          <DialogDescription>
            {bank ? 'Edite as informações do banco selecionado.' : 'Preencha as informações para adicionar um novo banco.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={handleSubmit} className="space-y-4 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Código Bancário*</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Ex: 001"
                        maxLength={5}
                        inputMode="numeric"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome do Banco*</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Ex: Banco do Brasil"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="logo"
              render={({ field }) => (
                <FormItem className="col-span-2">
                  <FormLabel>Logotipo</FormLabel>
                  <FormControl>
                    <div className="space-y-2">
                      {logoPreview ? (
                        <div className="relative w-40 h-40 mx-auto border rounded-md overflow-hidden flex items-center justify-center bg-gray-50">
                          <img
                            src={logoPreview}
                            alt="Logo preview"
                            className="max-w-full max-h-full object-contain"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="absolute top-2 right-2 h-7 w-7 p-0"
                            onClick={removeLogo}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div
                          className={cn(
                            "w-full h-40 border-2 border-dashed rounded-md flex flex-col items-center justify-center p-6 transition-colors",
                            isDragging ? "border-primary bg-primary/5" : "hover:border-primary",
                            "relative"
                          )}
                          onDrop={handleDrop}
                          onDragOver={handleDragOver}
                          onDragLeave={handleDragLeave}
                        >
                          <Camera className="h-10 w-10 text-gray-400 mb-2" />
                          <div className="text-sm text-center">
                            <p className="font-medium">Arraste ou clique para fazer upload</p>
                            <p className="text-muted-foreground text-xs mt-1">
                              PNG, JPG ou SVG (máx. 5MB)<br />
                              A imagem será redimensionada automaticamente
                            </p>
                          </div>
                          <label htmlFor="logo-upload" className="absolute inset-0 w-full h-full cursor-pointer">
                            <input
                              type="file"
                              accept="image/*"
                              className="opacity-0 w-full h-full"
                              onChange={handleFileChange}
                              id="logo-upload"
                            />
                          </label>
                        </div>
                      )}
                      <FormMessage />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />

            <DialogFooter className="pt-4">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancelar
              </Button>
              <Button type="submit">
                {bank ? 'Salvar alterações' : 'Adicionar banco'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
