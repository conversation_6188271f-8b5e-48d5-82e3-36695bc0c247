
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Calendar, CheckSquare, Upload, Search, MapPin, Loader2 } from "lucide-react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { AddressSearchModal } from "@/components/address/AddressSearchModal";
import { AddressFormModal } from "@/components/address/AddressFormModal";
import { Company, CreateCompanyRequest, UpdateCompanyRequest, Address } from "@/types/api";
import { addressService } from "@/services/api";
import { toast } from "sonner";
import { logger } from "@/utils/secureLogger";

interface CompanyModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (company: CreateCompanyRequest | UpdateCompanyRequest) => void;
  company?: Company;
  isLoading?: boolean;
}

export function CompanyModal({ open, onOpenChange, onSave, company, isLoading = false }: CompanyModalProps) {
  const [formData, setFormData] = useState<CreateCompanyRequest | UpdateCompanyRequest>({
    name: '',
    cnpj: '',
    phone: '',
    email: '',
    addressId: '',
    calendarType: 'padrão',
    active: true,
    logo: '',
  });

  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [isAddressSearchOpen, setIsAddressSearchOpen] = useState(false);
  const [isAddressFormOpen, setIsAddressFormOpen] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [isLoadingAddress, setIsLoadingAddress] = useState(false);

  // Reset form when modal opens or company changes
  useEffect(() => {
    if (company) {
      setFormData({
        name: company.name || '',
        cnpj: company.cnpj || '',
        phone: company.phone || '',
        email: company.email || '',
        addressId: company.addressId || '',
        calendarType: company.calendarType || 'padrão',
        active: company.active !== undefined ? company.active : true,
        logo: company.logo || '',
      });

      // Set logo preview if company has a logo
      if (company.logo) {
        setLogoPreview(company.logo);
      } else {
        setLogoPreview(null);
      }

      // Load address if company has an addressId
      if (company.addressId) {
        setIsLoadingAddress(true);
        addressService.getAddressById(company.addressId)
          .then(address => {
            setSelectedAddress(address);
          })
          .catch(error => {
            logger.error('Erro ao carregar endereço da empresa', error);
            toast.error('Não foi possível carregar o endereço da empresa');
            setSelectedAddress(null);
          })
          .finally(() => {
            setIsLoadingAddress(false);
          });
      } else {
        setSelectedAddress(null);
      }
    } else {
      setFormData({
        name: '',
        cnpj: '',
        phone: '',
        email: '',
        addressId: '',
        calendarType: 'padrão',
        active: true,
        logo: '',
      });
      setLogoPreview(null);
      setSelectedAddress(null);
    }
  }, [company, open]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result as string;
        setLogoPreview(result);
        setFormData({ ...formData, logo: result });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAddressSelect = (address: Address) => {
    setSelectedAddress(address);
    setFormData({ ...formData, addressId: address.id });
    setIsAddressSearchOpen(false);
  };

  const handleAddressCreate = (address: Address) => {
    setSelectedAddress(address);
    setFormData({ ...formData, addressId: address.id });
    setIsAddressFormOpen(false);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({...formData, address: selectedAddress});
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">
              {company ? "Editar Empresa" : "Nova Empresa"}
            </DialogTitle>
            <DialogDescription>
              {company
                ? "Edite os dados da empresa selecionada."
                : "Preencha os dados para cadastrar uma nova empresa."}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            <div className="flex flex-col items-center gap-4">
              <Label htmlFor="logo" className="cursor-pointer w-full text-center">
                Logo da Empresa
              </Label>
              <div className="relative group">
                <Avatar className="h-24 w-24 cursor-pointer group-hover:opacity-80 transition-opacity">
                  {logoPreview ? (
                    <AvatarImage src={logoPreview} alt="Logo da empresa" />
                  ) : (
                    <AvatarFallback className="bg-slate-200 text-slate-500 text-xl">
                      {formData.name ? formData.name.charAt(0).toUpperCase() : 'L'}
                    </AvatarFallback>
                  )}
                </Avatar>
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="bg-black bg-opacity-50 rounded-full p-2">
                    <Upload className="h-6 w-6 text-white" />
                  </div>
                </div>
                <Input
                  id="logo"
                  name="logo"
                  type="file"
                  accept="image/*"
                  onChange={handleLogoChange}
                  className="hidden"
                />
              </div>
              <span className="text-xs text-slate-500">Clique para selecionar um logo</span>
            </div>

            <div className="space-y-2">
              <Label htmlFor="name">
                Nome da Empresa<span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Nome da Empresa"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="cnpj">
                CNPJ<span className="text-red-500">*</span>
              </Label>
              <Input
                id="cnpj"
                name="cnpj"
                value={formData.cnpj}
                onChange={handleChange}
                placeholder="00.000.000/0000-00"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Telefone</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="(00) 00000-0000"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">E-mail</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Endereço</Label>
              <div className="flex gap-2">
                <div className="flex-1 relative">
                  <Input
                    id="address"
                    name="address"
                    value={isLoadingAddress
                      ? 'Carregando endereço...'
                      : selectedAddress
                        ? `${selectedAddress.street}, ${selectedAddress.number || 'S/N'} - ${selectedAddress.city}/${selectedAddress.state}`
                        : ''
                    }
                    readOnly
                    placeholder="Selecione um endereço"
                    className="pr-10"
                  />
                  {isLoadingAddress
                    ? <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 animate-spin" />
                    : <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />}
                </div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddressSearchOpen(true)}
                  className="flex items-center gap-1"
                >
                  <Search className="h-4 w-4" />
                  Buscar
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddressFormOpen(true)}
                  className="flex items-center gap-1"
                >
                  <MapPin className="h-4 w-4" />
                  Novo
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Tipo de Calendário</Label>
              <RadioGroup
                defaultValue={formData.calendarType}
                className="flex gap-4"
                onValueChange={(value) => setFormData({...formData, calendarType: value})}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="padrão" id="calendar-padrao" />
                  <Label htmlFor="calendar-padrao" className="flex items-center gap-2 font-normal cursor-pointer">
                    <Calendar className="h-4 w-4 text-blue-500" />
                    Padrão
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="periódico" id="calendar-periodico" />
                  <Label htmlFor="calendar-periodico" className="flex items-center gap-2 font-normal cursor-pointer">
                    <Calendar className="h-4 w-4 text-amber-500" />
                    Periódico
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="active"
                checked={formData.active}
                onCheckedChange={(checked) =>
                  setFormData({...formData, active: checked as boolean})
                }
              />
              <Label htmlFor="active" className="font-normal cursor-pointer flex items-center gap-2">
                <CheckSquare className="h-4 w-4" />
                Empresa Ativa
              </Label>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Salvando...
                </>
              ) : (
                'Salvar'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>

      {/* Address Search Modal */}
      <AddressSearchModal
        open={isAddressSearchOpen}
        onOpenChange={setIsAddressSearchOpen}
        onSelect={handleAddressSelect}
        onCreateNew={() => {
          setIsAddressSearchOpen(false);
          setIsAddressFormOpen(true);
        }}
      />

      {/* Address Form Modal */}
      <AddressFormModal
        open={isAddressFormOpen}
        onOpenChange={setIsAddressFormOpen}
        onSave={handleAddressCreate}
        companyId={company?.id || ''}
      />
    </Dialog>
  );
}
