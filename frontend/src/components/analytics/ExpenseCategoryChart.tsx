
import { useEffect, useState } from "react";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts";
import { <PERSON><PERSON><PERSON> } from "lucide-react";
import GlassCard from "../ui-custom/GlassCard";

interface ExpenseCategoryChartProps {
  selectedPeriod: string;
  selectedAccountId: string;
}

interface CategoryData {
  name: string;
  value: number;
  color: string;
}

const COLORS = ["#6366f1", "#06b6d4", "#f59e0b", "#f97316", "#8b5cf6"];

const ExpenseCategoryChart = ({ selectedPeriod, selectedAccountId }: ExpenseCategoryChartProps) => {
  // Removido dados mockados - componente agora mostra estado vazio
  // Em uma aplicação real, aqui seria feita uma chamada à API para buscar dados de despesas por categoria
  const data: CategoryData[] = [];

  return (
    <GlassCard className="relative overflow-hidden border-indigo-200/30 dark:border-indigo-800/30">
      <div className="absolute -left-8 -top-8 w-24 h-24 bg-gradient-to-br from-indigo-200/20 to-violet-200/20 rounded-full blur-xl"></div>
      <div className="relative z-10">
        <h2 className="font-semibold mb-4 flex items-center">
          <span className="inline-block w-3 h-3 bg-gradient-to-br from-indigo-500 to-violet-500 rounded-full mr-2"></span>
          Despesas por Categoria
        </h2>
        <div className="h-[260px] flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <div className="flex flex-col items-center gap-3">
              <PieChart className="h-12 w-12 text-muted-foreground/50" />
              <div>
                <p className="text-lg font-medium">Nenhuma despesa por categoria</p>
                <p className="text-sm">Os dados aparecerão aqui quando houver despesas cadastradas</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </GlassCard>
  );
};

export default ExpenseCategoryChart;
