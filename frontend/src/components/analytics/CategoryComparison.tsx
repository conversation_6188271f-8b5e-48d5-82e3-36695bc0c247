
import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Legend, ResponsiveContainer, <PERSON>ltip, XAxis, <PERSON><PERSON><PERSON><PERSON> } from "recharts";
import { BarChart3 } from "lucide-react";
import GlassCard from "../ui-custom/GlassCard";

interface CategoryComparisonProps {
  selectedPeriod: string;
  selectedAccountId: string;
}

const CategoryComparison = ({ selectedPeriod, selectedAccountId }: CategoryComparisonProps) => {
  // Removido dados mockados - componente agora mostra estado vazio
  // Em uma aplicação real, aqui seria feita uma chamada à API para buscar dados de comparação de categorias
  const data: any[] = [];
  const comparisonPeriod = "Período Anterior";

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  return (
    <GlassCard className="animate-fade-in">
      <h2 className="font-semibold mb-6">Comparativo de Categorias ({comparisonPeriod})</h2>
      <div className="h-[350px] flex items-center justify-center">
        <div className="text-center text-muted-foreground">
          <div className="flex flex-col items-center gap-3">
            <BarChart3 className="h-12 w-12 text-muted-foreground/50" />
            <div>
              <p className="text-lg font-medium">Nenhum comparativo de categorias</p>
              <p className="text-sm">Os dados aparecerão aqui quando houver histórico de despesas</p>
            </div>
          </div>
        </div>
      </div>
    </GlassCard>
  );
};

export default CategoryComparison;
