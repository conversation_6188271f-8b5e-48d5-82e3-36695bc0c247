
import { useEffect, useState } from "react";
import { Area, AreaChart, CartesianGrid, Legend, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";
import { TrendingUp } from "lucide-react";
import GlassCard from "../ui-custom/GlassCard";

interface FinancialTrendsChartProps {
  selectedPeriod: string;
  selectedAccountId: string;
}

const FinancialTrendsChart = ({ selectedPeriod, selectedAccountId }: FinancialTrendsChartProps) => {
  // Removido dados mockados - componente agora mostra estado vazio
  // Em uma aplicação real, aqui seria feita uma chamada à API para buscar tendências financeiras
  const data: any[] = [];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  return (
    <GlassCard className="h-full">
      <h2 className="font-semibold mb-6">Tendências Financeiras</h2>
      <div className="h-[400px] flex items-center justify-center">
        <div className="text-center text-muted-foreground">
          <div className="flex flex-col items-center gap-3">
            <TrendingUp className="h-12 w-12 text-muted-foreground/50" />
            <div>
              <p className="text-lg font-medium">Nenhuma tendência financeira</p>
              <p className="text-sm">Os dados aparecerão aqui quando houver histórico financeiro</p>
            </div>
          </div>
        </div>
      </div>
    </GlassCard>
  );
};

export default FinancialTrendsChart;
