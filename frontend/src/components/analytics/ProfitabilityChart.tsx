
import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts";
import { BarChart3 } from "lucide-react";
import GlassCard from "../ui-custom/GlassCard";

interface ProfitabilityChartProps {
  selectedPeriod: string;
  selectedAccountId: string;
}

const ProfitabilityChart = ({ selectedPeriod, selectedAccountId }: ProfitabilityChartProps) => {
  // Removido dados mockados - componente agora mostra estado vazio
  // Em uma aplicação real, aqui seria feita uma chamada à API para buscar dados de rentabilidade
  const data: any[] = [];

  return (
    <GlassCard className="relative overflow-hidden border-purple-200/30 dark:border-purple-800/30">
      <div className="absolute -right-8 -bottom-8 w-24 h-24 bg-gradient-to-tl from-purple-200/20 to-pink-200/20 rounded-full blur-xl"></div>
      <div className="relative z-10">
        <h2 className="font-semibold mb-4 flex items-center">
          <span className="inline-block w-3 h-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full mr-2"></span>
          Rentabilidade
        </h2>
        <div className="h-[260px] flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <div className="flex flex-col items-center gap-3">
              <BarChart3 className="h-12 w-12 text-muted-foreground/50" />
              <div>
                <p className="text-lg font-medium">Nenhum dado de rentabilidade</p>
                <p className="text-sm">Os dados aparecerão aqui quando houver receitas e despesas</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </GlassCard>
  );
};

export default ProfitabilityChart;
