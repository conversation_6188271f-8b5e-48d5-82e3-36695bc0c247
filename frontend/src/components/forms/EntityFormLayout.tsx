import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeft, Save, X, Edit } from 'lucide-react';
import { Layout } from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useFormUnsavedChanges } from '@/hooks/useUnsavedChanges';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface EntityFormLayoutProps {
  children: React.ReactNode;
  title: string;
  entityType: 'cliente' | 'fornecedor';
  mode: 'create' | 'edit' | 'view';
  entityName?: string;
  isLoading?: boolean;
  isSaving?: boolean;
  isDirty?: boolean;
  onSave?: () => void;
  onCancel?: () => void;
  onEdit?: () => void;
  canSave?: boolean;
}

export const EntityFormLayout: React.FC<EntityFormLayoutProps> = ({
  children,
  title,
  entityType,
  mode,
  entityName,
  isLoading = false,
  isSaving = false,
  isDirty = false,
  onSave,
  onCancel,
  onEdit,
  canSave = true
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const {
    showConfirmDialog,
    confirmNavigation,
    cancelNavigation,
    navigateWithConfirmation
  } = useFormUnsavedChanges(isDirty);

  const entityTypePlural = entityType === 'cliente' ? 'customers' : 'suppliers';
  const listPath = `/${entityTypePlural}`;

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      navigateWithConfirmation(listPath);
    }
  };

  const handleBackToList = () => {
    navigateWithConfirmation(listPath);
  };

  const getBreadcrumbItems = () => {
    const items = [
      {
        label: 'Entidades',
        href: undefined
      },
      {
        label: entityType === 'cliente' ? 'Clientes' : 'Fornecedores',
        href: listPath
      }
    ];

    if (mode === 'create') {
      items.push({
        label: `Novo ${entityType}`,
        href: undefined
      });
    } else if (mode === 'view') {
      items.push({
        label: entityName || `Visualizar ${entityType}`,
        href: undefined
      });
    } else {
      items.push({
        label: entityName || `Editar ${entityType}`,
        href: undefined
      });
    }

    return items;
  };

  const breadcrumbItems = getBreadcrumbItems();

  if (isLoading) {
    return (
      <Layout location={location}>
        <div className="container p-4 mx-auto">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="flex flex-col items-center gap-4">
              <LoadingSpinner size={32} />
              <p className="text-muted-foreground">Carregando...</p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout location={location}>
      <div className="container p-4 mx-auto max-w-4xl">
        {/* Breadcrumbs */}
        <div className="mb-6">
          <Breadcrumb>
            <BreadcrumbList>
              {breadcrumbItems.map((item, index) => (
                <div key={index} className="contents">
                  <BreadcrumbItem>
                    {item.href ? (
                      <BreadcrumbLink
                        onClick={() => navigateWithConfirmation(item.href!)}
                        className="cursor-pointer"
                      >
                        {item.label}
                      </BreadcrumbLink>
                    ) : (
                      <BreadcrumbPage>{item.label}</BreadcrumbPage>
                    )}
                  </BreadcrumbItem>
                  {index < breadcrumbItems.length - 1 && <BreadcrumbSeparator />}
                </div>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleBackToList}
              className="shrink-0"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-2xl font-bold">{title}</h1>
              {mode === 'edit' && entityName && (
                <p className="text-muted-foreground">{entityName}</p>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            {mode === 'view' ? (
              <>
                <Button
                  variant="outline"
                  onClick={handleCancel}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Voltar
                </Button>

                {onEdit && (
                  <Button
                    onClick={onEdit}
                    className="gap-2"
                  >
                    <Edit className="h-4 w-4" />
                    Editar
                  </Button>
                )}
              </>
            ) : (
              <>
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSaving}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancelar
                </Button>

                {onSave && (
                  <Button
                    onClick={onSave}
                    disabled={isSaving || !canSave}
                    className="gap-2"
                  >
                    {isSaving ? (
                      <LoadingSpinner size={16} />
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                    {isSaving ? 'Salvando...' : 'Salvar'}
                  </Button>
                )}
              </>
            )}
          </div>
        </div>

        {/* Form Content */}
        <Card>
          <CardHeader>
            <CardTitle>
              {mode === 'create'
                ? `Dados do ${entityType}`
                : mode === 'view'
                  ? `Dados do ${entityType}`
                  : `Editar ${entityType}`
              }
            </CardTitle>
          </CardHeader>
          <CardContent>
            {children}
          </CardContent>
        </Card>

        {/* Confirmation Dialog */}
        <AlertDialog open={showConfirmDialog} onOpenChange={cancelNavigation}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Alterações não salvas</AlertDialogTitle>
              <AlertDialogDescription>
                Você tem alterações não salvas no formulário. Deseja realmente sair sem salvar?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={cancelNavigation}>
                Continuar editando
              </AlertDialogCancel>
              <AlertDialogAction onClick={confirmNavigation}>
                Sair sem salvar
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </Layout>
  );
};
