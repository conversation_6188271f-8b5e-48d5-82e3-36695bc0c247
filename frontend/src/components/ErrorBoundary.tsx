import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { logger } from '@/utils/secureLogger';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Componente para capturar erros não tratados em componentes React
 * e registrá-los no sistema de monitoramento
 */
class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    logger.error('ErrorBoundary capturou erro', error);
    // Atualiza o estado para que a próxima renderização mostre a UI de fallback
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    logger.error('ErrorBoundary componentDidCatch', error, { errorInfo });
    
    this.setState({
      hasError: true,
      error,
      errorInfo
    });

    // Atrasar o registro do erro para garantir que o monitoringService esteja disponível
    setTimeout(() => {
      if (window.monitoringService) {
        try {
          window.monitoringService.recordError({
            message: error.message,
            stack: error.stack,
            componentName: this.getComponentName(errorInfo),
            context: {
              componentStack: errorInfo.componentStack,
            }
          });
        } catch (monitoringError) {
          // Erro ao registrar no monitoringService - falha silenciosa
        }
      }
    }, 100);
  }

  private getComponentName(errorInfo: ErrorInfo): string {
    // Tenta extrair o nome do componente a partir da stack de componentes
    const match = errorInfo.componentStack.match(/\s*in\s+([A-Za-z0-9]+)/);
    return match ? match[1] : 'Unknown';
  }

  private handleReset = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  }

  private handleReload = (): void => {
    window.location.reload();
  }

  public render(): ReactNode {
    if (this.state.hasError) {
      // Renderiza o fallback personalizado ou o fallback padrão
      if (this.props.fallback) {
        return this.props.fallback;
      }
      return (
        <div className="flex flex-col items-center justify-center min-h-[400px] p-6 bg-background border rounded-lg shadow-sm">
          <AlertCircle className="h-12 w-12 text-destructive mb-4" />
          <h2 className="text-xl font-semibold mb-2">Ocorreu um erro inesperado</h2>
          <p className="text-muted-foreground mb-6 text-center max-w-md">
            Desculpe, encontramos um problema ao carregar esta seção. Nossa equipe foi notificada automaticamente.
          </p>
          <div className="flex gap-4">
            <Button variant="outline" onClick={this.handleReset}>
              Tentar novamente
            </Button>
            <Button onClick={this.handleReload}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Recarregar página
            </Button>
          </div>
          {import.meta.env.DEV && this.state.error && (
            <div className="mt-6 p-4 bg-muted rounded-md w-full overflow-auto max-h-[300px]">
              <p className="font-mono text-sm mb-2">{this.state.error.toString()}</p>
              {this.state.errorInfo && (
                <pre className="font-mono text-xs whitespace-pre-wrap">
                  {this.state.errorInfo.componentStack}
                </pre>
              )}
            </div>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
