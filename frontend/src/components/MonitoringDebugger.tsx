import React, { useState, useEffect } from 'react';
// Importar o inicializador para garantir que o monitoringService está registrado
import '@/services/initializers/monitoringInitializer';
import { eventBus } from '@/services/events/eventBus';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

/**
 * Componente para depuração do sistema de monitoramento
 * Permite visualizar eventos em tempo real e testar o sistema
 */
const MonitoringDebugger: React.FC = () => {
  const [events, setEvents] = useState<Array<{ type: string; data: any; timestamp: string }>>([]);
  const [isDebugEnabled, setIsDebugEnabled] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    // Habilitar modo de depuração no EventBus
    eventBus.setDebug(isDebugEnabled);

    // Verificar disponibilidade do monitoringService a cada 100ms até encontrá-lo
    // para lidar com casos onde o componente é montado antes do serviço estar disponível
    let checkInterval: number | null = null;
    let subscriptions: Array<{unsubscribe: () => void}> = [];
    
    const setupMonitoring = () => {
      if (typeof window === 'undefined' || !window.monitoringService) {
        console.log('MonitoringService ainda não disponível, aguardando...');
        return false;
      }
      
      const monitoringService = window.monitoringService;
      console.log('MonitoringService encontrado, configurando listeners.');
      
      try {
        // Inscrever-se para todos os eventos disponíveis
        const eventTypes = monitoringService.getEventTypes?.() || [];
        
        subscriptions = eventTypes.map(eventType => {
          return monitoringService.subscribe(eventType, (data: any) => {
            setEvents(prev => [
              { 
                type: eventType, 
                data, 
                timestamp: new Date().toISOString() 
              },
              ...prev
            ].slice(0, 100)); // Limitar a 100 eventos para evitar problemas de performance
          });
        });
        
        // Limpar o intervalo quando o serviço for encontrado
        if (checkInterval) {
          window.clearInterval(checkInterval);
          checkInterval = null;
        }
        
        return true;
      } catch (error) {
        return false;
      }
    };
    
    // Tentar configurar imediatamente
    const success = setupMonitoring();
    
    // Se não for bem-sucedido, configurar um intervalo para tentar novamente
    if (!success) {
      checkInterval = window.setInterval(setupMonitoring, 100);
    }

    // Limpar inscrições e intervalo ao desmontar
    return () => {
      if (checkInterval) {
        window.clearInterval(checkInterval);
      }
      subscriptions.forEach(sub => sub.unsubscribe?.());
    };
  }, [isDebugEnabled]);

  // Filtrar eventos com base na aba ativa
  const filteredEvents = activeTab === 'all' 
    ? events 
    : events.filter(event => event.type.startsWith(activeTab.toUpperCase()));

  // Testar diferentes tipos de eventos
  const testApiEvent = () => {
    if (typeof window === 'undefined' || !window.monitoringService) {
      // MonitoringService não disponível - falha silenciosa
      return;
    }
    
    window.monitoringService.recordApiRequest({
      url: '/api/test',
      method: 'GET',
      requestId: `test-${Date.now()}`
    });
  };

  const testAuthEvent = () => {
    if (typeof window === 'undefined' || !window.monitoringService) {
      // MonitoringService não disponível - falha silenciosa
      return;
    }
    
    // Usar string literal em vez de enum para evitar dependência circular
    window.monitoringService.recordAuthEvent('AUTH_LOGIN', {
      action: 'login_test',
      email: '<EMAIL>',
      success: true
    });
  };

  const testErrorEvent = () => {
    if (typeof window === 'undefined' || !window.monitoringService) {
      // MonitoringService não disponível - falha silenciosa
      return;
    }
    
    window.monitoringService.recordError({
      message: 'Erro de teste',
      componentName: 'MonitoringDebugger',
      source: 'test',
      stack: new Error('Erro de teste').stack
    });
  };

  const clearEvents = () => {
    setEvents([]);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Depurador de Monitoramento</CardTitle>
        <CardDescription>
          Visualize e teste o sistema de monitoramento baseado em eventos
        </CardDescription>
        <div className="flex items-center space-x-2 mt-2">
          <Switch 
            id="debug-mode" 
            checked={isDebugEnabled}
            onCheckedChange={setIsDebugEnabled}
          />
          <Label htmlFor="debug-mode">Modo de depuração</Label>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">Todos</TabsTrigger>
            <TabsTrigger value="api">API</TabsTrigger>
            <TabsTrigger value="auth">Autenticação</TabsTrigger>
            <TabsTrigger value="error">Erros</TabsTrigger>
          </TabsList>
          
          <TabsContent value={activeTab} className="mt-0">
            <div className="flex flex-wrap gap-2 mb-4">
              <Button size="sm" variant="outline" onClick={testApiEvent}>
                Testar Evento API
              </Button>
              <Button size="sm" variant="outline" onClick={testAuthEvent}>
                Testar Evento Auth
              </Button>
              <Button size="sm" variant="outline" onClick={testErrorEvent}>
                Testar Evento Erro
              </Button>
              <Button size="sm" variant="outline" onClick={clearEvents} className="ml-auto">
                Limpar Eventos
              </Button>
            </div>
            
            <ScrollArea className="h-[400px] rounded-md border">
              {filteredEvents.length === 0 ? (
                <div className="p-4 text-center text-muted-foreground">
                  Nenhum evento registrado. Use os botões acima para testar.
                </div>
              ) : (
                <div className="p-4 space-y-4">
                  {filteredEvents.map((event, index) => (
                    <div key={index} className="p-3 rounded-md bg-muted/50">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant={getBadgeVariant(event.type)}>
                          {event.type}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {new Date(event.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <pre className="text-xs overflow-auto p-2 bg-background rounded max-h-[200px]">
                        {JSON.stringify(event.data, null, 2)}
                      </pre>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="text-xs text-muted-foreground">
        Total de eventos: {events.length} | Filtrados: {filteredEvents.length}
      </CardFooter>
    </Card>
  );
};

// Função auxiliar para determinar a variante do Badge com base no tipo de evento
const getBadgeVariant = (eventType: string): "default" | "secondary" | "destructive" | "outline" => {
  if (eventType.includes('ERROR')) return "destructive";
  if (eventType.startsWith('API_')) return "secondary";
  if (eventType.startsWith('AUTH_')) return "default";
  if (eventType.startsWith('PERFORMANCE_')) return "default";
  return "outline";
};

export default MonitoringDebugger;
