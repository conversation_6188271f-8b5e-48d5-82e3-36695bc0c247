import { AlertTriangle } from "lucide-react";
import GlassCard from "@/components/ui-custom/GlassCard";
import { useState, useEffect } from "react";

interface OverdueAccountsCardProps {
  filter: string;
  selectedPeriod: string;
  selectedEntityId: string;
}

const OverdueAccountsCard = ({ filter, selectedPeriod, selectedEntityId }: OverdueAccountsCardProps) => {
  // Removido dados mockados - componente agora mostra estado vazio
  // Em uma aplicação real, aqui seria feita uma chamada à API para buscar contas em atraso

  return (
    <GlassCard className="animate-scale-in" style={{ animationDelay: "0.2s" }}>
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <span className="h-2 w-2 rounded-full bg-gray-400"></span>
          <h3 className="text-sm font-medium text-muted-foreground">Em Atraso</h3>
        </div>
        <p className="text-3xl font-bold text-muted-foreground">--</p>
        <p className="text-xs text-muted-foreground">
          Aguardando dados reais
        </p>
      </div>
    </GlassCard>
  );
};

export default OverdueAccountsCard;
