import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { formatCurrency, parseCurrencyToNumber } from "@/utils/currencyUtils";
import Transaction<PERSON>mountField from "../transactions/TransactionAmountField";
import TransactionDateSelector from "../transactions/TransactionDateSelector";
import TransactionAccountSelector from "../transactions/TransactionAccountSelector";
import TransactionFinalValueDisplay from "../transactions/TransactionFinalValueDisplay";
import { transactionService } from "@/services/api";
import { toast } from "sonner";
import { logger } from "@/utils/secureLogger";

interface ReceiptFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  transaction: any;
}

import { useActiveCompany } from '@/hooks/useActiveCompany';
import { useBankAccounts } from '@/hooks/api/useBankAccounts';

export default function ReceiptForm({ open, onOpenChange, transaction }: ReceiptFormProps) {
  const activeCompanyId = useActiveCompany();
  const { data, isLoading } = useBankAccounts(1, 100, { companyId: activeCompanyId });
  const accounts = data?.data || [];

  const [receiptValue, setReceiptValue] = useState<string>("0,00");
  const [interestValue, setInterestValue] = useState<string>("0,00");
  const [discountValue, setDiscountValue] = useState<string>("0,00");
  const [selectedAccount, setSelectedAccount] = useState<string>("");
  const [receiptDate, setReceiptDate] = useState<Date | undefined>(new Date());
  const [finalValue, setFinalValue] = useState<number>(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (transaction && open) {
      // Initialize form with transaction data
      const amount = transaction.amount || 0;
      setReceiptValue(amount.toFixed(2).replace(".", ","));
      setInterestValue("0,00");
      setDiscountValue("0,00");
      setFinalValue(amount);
      setSelectedAccount(transaction.bankAccountId || "");
      setReceiptDate(new Date());
    }
  }, [transaction, open]);

  // Calculate final value when receipt, interest or discount changes
  useEffect(() => {
    const receipt = parseCurrencyToNumber(receiptValue) || 0;
    const interest = parseCurrencyToNumber(interestValue) || 0;
    const discount = parseCurrencyToNumber(discountValue) || 0;

    const calculatedValue = receipt + interest - discount;
    setFinalValue(calculatedValue);
  }, [receiptValue, interestValue, discountValue]);

  const handleConfirm = async () => {
    if (!selectedAccount) {
      toast.error("Selecione uma conta bancária para o recebimento");
      return;
    }

    if (parseCurrencyToNumber(receiptValue) <= 0) {
      toast.error("O valor do recebimento deve ser maior que zero");
      return;
    }

    try {
      setIsSubmitting(true);

      // Criar uma transação do tipo income vinculada à conta a receber
      await transactionService.createTransaction({
        type: 'income',
        amount: parseCurrencyToNumber(receiptValue),
        description: `Recebimento: ${transaction.description}`,
        transactionDate: receiptDate?.toISOString().split('T')[0],
        bankAccountId: selectedAccount,
        categoryId: transaction.categoryId,
        entityId: transaction.entityId,
        projectId: transaction.projectId,
        paymentMethodId: transaction.paymentMethodId,
        accountsReceivableId: transaction.id,
        notes: `Recebimento com juros de ${interestValue} e desconto de ${discountValue}. Valor final: ${formatCurrency(finalValue)}`
      });

      toast.success("Recebimento registrado com sucesso!");
      onOpenChange(false);
    } catch (error) {
      logger.error("Erro ao registrar recebimento", error);
      toast.error("Erro ao registrar recebimento. Tente novamente.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Registrar Recebimento</DialogTitle>
          <p className="text-muted-foreground mt-1">
            Informe o valor, a conta e a data do recebimento.
          </p>
        </DialogHeader>

        <div className="py-3">
          <h2 className="text-xl font-semibold">{transaction?.description || "Recebimento"}</h2>
          <p className="text-muted-foreground">
            Valor total: {formatCurrency(transaction?.amount || 0)}
          </p>
        </div>

        <div className="space-y-6 pt-3">
          <TransactionAmountField
            id="receiptValue"
            label="Valor do recebimento"
            value={receiptValue}
            onChange={setReceiptValue}
            isMainAmount={true}
          />

          <div className="grid grid-cols-2 gap-4">
            <TransactionAmountField
              id="interestValue"
              label="Juros (R$)"
              value={interestValue}
              onChange={setInterestValue}
            />
            <TransactionAmountField
              id="discountValue"
              label="Descontos (R$)"
              value={discountValue}
              onChange={setDiscountValue}
            />
          </div>

          <TransactionFinalValueDisplay
            finalValue={finalValue}
            isPayment={false}
            description="Valor do Recebimento + Juros - Descontos"
          />

          <TransactionAccountSelector
            accounts={accounts}
            selectedAccount={selectedAccount}
            onAccountChange={setSelectedAccount}
          />
          {isLoading && <div className="text-sm text-gray-500 mt-1">Carregando contas bancárias...</div>}
          {!isLoading && accounts.length === 0 && (
            <div className="text-sm text-gray-500 mt-1">Nenhuma conta bancária encontrada</div>
          )}

          <TransactionDateSelector
            id="receiptDate"
            label="Data do recebimento"
            date={receiptDate}
            onDateChange={setReceiptDate}
          />

          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancelar
            </Button>
            <Button
              className="bg-[#00A86B] hover:bg-[#008C58]"
              onClick={handleConfirm}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Processando..." : "Confirmar Recebimento"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
