import React from 'react';
import { MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Address, AddressType } from '@/types/api';
import { AddressViewCard } from './AddressViewCard';
import { useEntityAddresses } from '@/hooks/api/useEntityAddresses';
import { mapBackendAddressType } from '@/utils/addressUtils';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface EntityAddressViewSectionProps {
  entityId: string;
  className?: string;
}

export const EntityAddressViewSection: React.FC<EntityAddressViewSectionProps> = ({
  entityId,
  className
}) => {
  // Hook para buscar endereços da API
  const { 
    data: apiAddresses = [], 
    isLoading, 
    error 
  } = useEntityAddresses(entityId, {
    enabled: !!entityId,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    staleTime: 0
  });

  // Mapear endereços da API para o formato esperado pelo frontend
  const addresses = apiAddresses.map(addr => ({
    ...addr,
    type: mapBackendAddressType((addr as any).addressType),
  }));

  if (isLoading) {
    return (
      <div className={cn('space-y-6', className)}>
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Endereços
            </h3>
          </div>
          <div className="flex items-center justify-center p-8">
            <div className="flex flex-col items-center gap-4">
              <LoadingSpinner size={32} />
              <p className="text-muted-foreground">Carregando endereços...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn('space-y-6', className)}>
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Endereços
            </h3>
          </div>
          <div className="text-center p-4 border border-dashed rounded-md">
            <p className="text-sm text-destructive">Erro ao carregar endereços: {error.message}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Separador visual */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        {/* Header da Seção */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Endereços ({addresses.length})
          </h3>
        </div>

        {/* Lista de Endereços */}
        {addresses.length > 0 ? (
          <div className="space-y-4">
            {addresses.map((address) => (
              <AddressViewCard
                key={address.id}
                address={address}
                isDefault={address.isDefault}
              />
            ))}
          </div>
        ) : (
          /* Estado Vazio */
          <div className="text-center py-12 border border-dashed border-gray-300 rounded-lg bg-gray-50 dark:border-gray-600 dark:bg-gray-800/50">
            <MapPin className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Nenhum endereço cadastrado
            </h4>
            <p className="text-gray-500 dark:text-gray-400">
              Esta entidade não possui endereços cadastrados.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
