import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, Edit, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Address, AddressType } from '@/types/api';
import { getAddressTypeInfo, formatFullAddress } from '@/utils/addressUtils';

interface AddressCardProps {
  address: Address & { type?: AddressType };
  isDefault?: boolean;
  onSetDefault?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  className?: string;
}

export const AddressCard: React.FC<AddressCardProps> = ({
  address,
  isDefault = false,
  onSetDefault,
  onEdit,
  onDelete,
  className
}) => {
  const typeInfo = getAddressTypeInfo(address.type || 'main');
  const formattedAddress = formatFullAddress(address);

  return (
    <div
      className={cn(
        'relative rounded-lg border p-4 transition-all duration-200',
        isDefault 
          ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20' 
          : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800',
        'hover:shadow-md',
        className
      )}
    >
      {/* Área Principal */}
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          {/* Header com tipo e badge padrão */}
          <div className="flex items-center gap-2 mb-3">
            <span className="text-lg" role="img" aria-label={typeInfo.label}>
              {typeInfo.icon}
            </span>
            <span className="font-medium text-gray-900 dark:text-gray-100">
              {typeInfo.label}
            </span>
            {isDefault && (
              <Badge variant="secondary" className="text-xs">
                Padrão
              </Badge>
            )}
          </div>

          {/* Dados do endereço */}
          <div className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
            {address.street && (
              <div>
                <span className="font-medium">
                  {address.street}
                  {address.number && `, ${address.number}`}
                  {address.complement && ` - ${address.complement}`}
                </span>
              </div>
            )}
            
            {(address.district || address.neighborhood || address.city || address.state) && (
              <div>
                {(address.district || address.neighborhood) && (
                  <span>{address.district || address.neighborhood}</span>
                )}
                {(address.city || address.state) && (
                  <>
                    {(address.district || address.neighborhood) && ' - '}
                    {address.city}
                    {address.state && `/${address.state}`}
                  </>
                )}
              </div>
            )}
            
            {address.zipCode && (
              <div>
                <span className="font-mono">CEP: {address.zipCode}</span>
              </div>
            )}
          </div>
        </div>

        {/* Área de Ações */}
        <div className="flex flex-col gap-1 ml-4 min-w-[32px]">
          {/* Botão Definir como Padrão / Indicador de Padrão */}
          <div className="h-8 w-8 flex items-center justify-center">
            {!isDefault && onSetDefault ? (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-950/20"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onSetDefault();
                }}
                title="Definir como padrão"
              >
                <Star className="h-4 w-4" />
              </Button>
            ) : isDefault ? (
              <div
                className="h-8 w-8 flex items-center justify-center"
                title="Endereço padrão"
              >
                <Star className="h-4 w-4 text-blue-600 fill-current" />
              </div>
            ) : (
              // Espaço reservado para manter alinhamento
              <div className="h-8 w-8" />
            )}
          </div>

          {/* Botão Editar */}
          {onEdit && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-950/20"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onEdit();
              }}
              title="Editar endereço"
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}

          {/* Botão Excluir */}
          {onDelete && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-gray-400 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/20"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onDelete();
              }}
              title="Excluir endereço"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
