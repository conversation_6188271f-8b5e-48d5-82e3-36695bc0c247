# Nova Arquitetura de Cadastro de Endereços

## Visão Geral

Esta implementação segue a especificação detalhada para uma nova arquitetura de cadastro de endereços integrada, substituindo o sistema de modais por uma seção inline dentro dos formulários de entidades.

## Componentes Principais

### 1. AddressSection
**Arquivo:** `AddressSection.tsx`

Componente principal que gerencia toda a seção de endereços dentro do formulário de entidades.

**Características:**
- Seção integrada (não modal)
- Header com contador de endereços
- Botão "Adicionar Endereço" 
- Lista de cards de endereços
- Estado vazio com call-to-action
- Formulário inline para novos endereços

**Props:**
```typescript
interface AddressSectionProps {
  addresses: (Address & { type?: AddressType })[];
  onAddAddress: (address: CreateAddressRequest) => void;
  onUpdateAddress: (id: string, address: Partial<Address>) => void;
  onDeleteAddress: (id: string) => void;
  onSetDefaultAddress: (id: string) => void;
  className?: string;
}
```

### 2. AddressCard
**Arquivo:** `AddressCard.tsx`

Card individual para exibir um endereço cadastrado.

**Características:**
- Ícones por tipo (🏢 Principal, 💳 Cobrança, 📦 Entrega)
- Badge "Padrão" para endereço padrão
- Formatação completa do endereço
- Botões de ação (definir padrão, editar, excluir)
- Estados visuais diferenciados

### 3. AddressInlineForm
**Arquivo:** `AddressInlineForm.tsx`

Formulário inline para adicionar novos endereços.

**Características:**
- Seleção de tipo com cards visuais
- Busca automática de CEP
- Validação em tempo real
- Grid responsivo (2 colunas desktop, 1 mobile)
- Checkbox para endereço padrão
- Feedback visual durante busca de CEP

## Hooks e Utilitários

### useCepSearch
**Arquivo:** `useCepSearch.ts`

Hook para busca automática de CEP com fallback.

**Funcionalidades:**
- Busca primeiro no backend
- Fallback para ViaCEP
- Estados de loading e erro
- Validação de formato

### addressUtils
**Arquivo:** `addressUtils.ts`

Utilitários para manipulação de endereços.

**Funções principais:**
- `formatCep()` - Formatação de CEP
- `validateAddress()` - Validação completa
- `formatFullAddress()` - Formatação para exibição
- `getAddressTypeInfo()` - Informações do tipo

## Tipos TypeScript

### AddressType
```typescript
type AddressType = 'main' | 'billing' | 'shipping';
```

### AddressTypeInfo
```typescript
interface AddressTypeInfo {
  value: AddressType;
  label: string;
  icon: string;
}
```

## Fluxos de Interação

### 1. Adicionar Novo Endereço
1. Usuário clica em "Adicionar Endereço"
2. Formulário inline aparece
3. Usuário seleciona tipo de endereço
4. Digita CEP → busca automática
5. Preenche campos restantes
6. Clica "Adicionar Endereço"
7. Card é adicionado à lista

### 2. Busca Automática de CEP
1. Usuário digita CEP completo (8 dígitos)
2. Spinner aparece no campo CEP
3. Campos relacionados ficam desabilitados
4. API é chamada (backend → ViaCEP)
5. Campos são preenchidos automaticamente
6. Feedback visual de sucesso/erro

### 3. Gerenciar Endereços
- **Definir Padrão:** Clique na estrela vazia
- **Editar:** Clique no ícone de lápis (placeholder)
- **Excluir:** Clique no ícone de lixeira

## Responsividade

### Mobile (< 768px)
- Grid muda para 1 coluna
- Cards com padding reduzido
- Botões de ação otimizados

### Tablet (768px - 1024px)
- Mantém 2 colunas no grid
- Espaçamentos ajustados

### Desktop (> 1024px)
- Layout completo conforme especificado

## Estados e Validações

### Estados dos Campos
- **Normal:** Borda cinza-300
- **Foco:** Borda azul-500 com ring
- **Erro:** Borda vermelha-500
- **Desabilitado:** Fundo cinza-100
- **Loading:** Spinner animado

### Validações
- CEP: Formato XXXXX-XXX, 8 dígitos
- Campos obrigatórios: CEP, Rua, Número, Bairro, Cidade, Estado
- Estado: Exatamente 2 caracteres (UF)

## Acessibilidade

- **Labels:** Todos os campos com labels visíveis
- **ARIA:** Roles apropriados para elementos customizados
- **Tab Order:** Sequência lógica de navegação
- **Screen Readers:** Textos alternativos para ícones
- **Contraste:** Mínimo WCAG AA (4.5:1)
- **Teclado:** Todas ações acessíveis via teclado

## Integração

### Uso nos Modais de Entidades
```typescript
// CustomerModal.tsx / SupplierModal.tsx
<AddressSection
  addresses={addresses}
  onAddAddress={handleAddAddress}
  onUpdateAddress={handleUpdateAddress}
  onDeleteAddress={handleDeleteAddress}
  onSetDefaultAddress={handleSetDefaultAddress}
/>
```

### Gerenciamento de Estado
```typescript
const [addresses, setAddresses] = useState<(Address & { type?: AddressType })[]>([]);

const handleAddAddress = (addressData: CreateAddressRequest) => {
  // Lógica para adicionar endereço
};
```

## Demonstração

Acesse `/address-demo` para ver a implementação completa em funcionamento com dados de exemplo.

## Próximos Passos

1. **Edição Inline:** Implementar edição de endereços existentes
2. **Persistência:** Integrar com APIs do backend
3. **Validação de Duplicatas:** Verificar CEP + Número duplicados
4. **Limite de Endereços:** Implementar limite máximo (sugestão: 10)
5. **Analytics:** Trackear taxa de sucesso da busca de CEP
