import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Address, AddressType } from '@/types/api';
import { getAddressTypeInfo, formatFullAddress } from '@/utils/addressUtils';

interface AddressViewCardProps {
  address: Address & { type?: AddressType };
  isDefault?: boolean;
  className?: string;
}

export const AddressViewCard: React.FC<AddressViewCardProps> = ({
  address,
  isDefault = false,
  className
}) => {
  const typeInfo = getAddressTypeInfo(address.type || 'main');
  const formattedAddress = formatFullAddress(address);

  return (
    <div
      className={cn(
        'relative rounded-lg border p-4 transition-all duration-200',
        isDefault 
          ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20' 
          : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800',
        className
      )}
    >
      <div className="flex items-start justify-between">
        {/* Conteúdo Principal */}
        <div className="flex-1 min-w-0">
          {/* Header com tipo e badge de padrão */}
          <div className="flex items-center gap-2 mb-3">
            <Badge 
              variant="outline" 
              className={cn(
                'text-xs font-medium',
                typeInfo.color
              )}
            >
              {typeInfo.icon}
              {typeInfo.label}
            </Badge>
            
            {isDefault && (
              <Badge variant="default" className="text-xs">
                <Star className="h-3 w-3 mr-1 fill-current" />
                Padrão
              </Badge>
            )}
          </div>

          {/* Dados do endereço */}
          <div className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
            {address.street && (
              <div>
                <span className="font-medium">
                  {address.street}
                  {address.number && `, ${address.number}`}
                  {address.complement && ` - ${address.complement}`}
                </span>
              </div>
            )}
            
            {(address.district || address.neighborhood || address.city || address.state) && (
              <div>
                {(address.district || address.neighborhood) && (
                  <span>{address.district || address.neighborhood}</span>
                )}
                {(address.city || address.state) && (
                  <>
                    {(address.district || address.neighborhood) && ' - '}
                    {address.city}
                    {address.state && `/${address.state}`}
                  </>
                )}
              </div>
            )}
            
            {address.zipCode && (
              <div>
                <span className="font-mono">CEP: {address.zipCode}</span>
              </div>
            )}
          </div>
        </div>

        {/* Indicador de Padrão (apenas visual) */}
        <div className="flex flex-col gap-1 ml-4 min-w-[32px]">
          <div className="h-8 w-8 flex items-center justify-center">
            {isDefault ? (
              <div
                className="h-8 w-8 flex items-center justify-center"
                title="Endereço padrão"
              >
                <Star className="h-4 w-4 text-blue-600 fill-current" />
              </div>
            ) : (
              // Espaço reservado para manter alinhamento
              <div className="h-8 w-8" />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
