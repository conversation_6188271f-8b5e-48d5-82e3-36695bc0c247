import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Address, AddressType, CreateAddressRequest } from '@/types/api';
import { AddressCard } from './AddressCard';
import { AddressInlineForm } from './AddressInlineForm';
import { useCreateEntityAddress, useUpdateEntityAddress, useDeleteEntityAddress } from '@/hooks/api/useEntityAddresses';
import { toast } from 'sonner';

interface AddressSectionProps {
  addresses: (Address & { type?: AddressType })[];
  onAddAddress: (address: CreateAddressRequest) => void;
  onUpdateAddress: (id: string, address: Partial<Address>) => void;
  onDeleteAddress: (id: string) => void;
  onSetDefaultAddress: (id: string) => void;
  className?: string;
  entityId?: string; // Novo prop para permitir integração com API
  useApiIntegration?: boolean; // Flag para ativar integração com API
}

export const AddressSection: React.FC<AddressSectionProps> = ({
  addresses = [],
  onAddAddress,
  onUpdateAddress,
  onDeleteAddress,
  onSetDefaultAddress,
  className,
  entityId,
  useApiIntegration = false
}) => {
  const [showForm, setShowForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<string | null>(null);

  // Hooks para integração com API (apenas se entityId for fornecido)
  const createAddressMutation = useCreateEntityAddress();
  const updateAddressMutation = useUpdateEntityAddress();
  const deleteAddressMutation = useDeleteEntityAddress();

  // Função para adicionar novo endereço
  const handleAddAddress = async (addressData: CreateAddressRequest) => {
    try {
      if (useApiIntegration && entityId) {
        // Usar API para persistir endereço diretamente
        await createAddressMutation.mutateAsync({
          entityId,
          data: addressData
        });
        toast.success('Endereço adicionado com sucesso!');
      } else {
        // Usar callback tradicional (para compatibilidade)
        onAddAddress(addressData);
        toast.success('Endereço adicionado com sucesso!');
      }
      setShowForm(false);
    } catch (error) {
      console.error('Erro ao adicionar endereço:', error);
      toast.error('Erro ao adicionar endereço. Tente novamente.');
    }
  };

  // Função para definir endereço como padrão
  const handleSetDefault = async (addressId: string) => {
    try {
      if (useApiIntegration && entityId) {
        // Usar API para atualizar endereço padrão
        await updateAddressMutation.mutateAsync({
          entityId,
          addressId,
          data: { isDefault: true }
        });
        toast.success('Endereço padrão atualizado!');
      } else {
        // Usar callback tradicional
        onSetDefaultAddress(addressId);
        toast.success('Endereço padrão atualizado!');
      }
    } catch (error) {
      toast.error('Erro ao definir endereço padrão. Tente novamente.');
    }
  };

  // Função para excluir endereço
  const handleDeleteAddress = async (addressId: string) => {
    try {
      if (useApiIntegration && entityId) {
        // Usar API para deletar endereço
        await deleteAddressMutation.mutateAsync({
          entityId,
          addressId
        });
        toast.success('Endereço removido com sucesso!');
      } else {
        // Usar callback tradicional
        onDeleteAddress(addressId);
        toast.success('Endereço removido com sucesso!');
      }
    } catch (error) {
      toast.error('Erro ao remover endereço. Tente novamente.');
    }
  };

  // Função para editar endereço
  const handleEditAddress = (addressId: string) => {
    setEditingAddress(addressId);
    setShowForm(false); // Fecha o formulário de novo endereço se estiver aberto
  };

  // Função para salvar edição de endereço
  const handleSaveEditAddress = async (addressData: CreateAddressRequest) => {
    if (!editingAddress) return;

    try {
      if (useApiIntegration && entityId) {
        // Usar API para atualizar endereço
        await updateAddressMutation.mutateAsync({
          entityId,
          addressId: editingAddress,
          data: addressData
        });
        toast.success('Endereço atualizado com sucesso!');
      } else {
        // Usar callback tradicional
        onUpdateAddress(editingAddress, addressData);
        toast.success('Endereço atualizado com sucesso!');
      }
      setEditingAddress(null);
    } catch (error) {
      console.error('Erro ao atualizar endereço:', error);
      toast.error('Erro ao atualizar endereço. Tente novamente.');
    }
  };

  // Função para cancelar edição
  const handleCancelEdit = () => {
    setEditingAddress(null);
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Separador visual */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        {/* Header da Seção */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Endereços ({addresses.length})
          </h3>
          {!showForm && !editingAddress && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowForm(true)}
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              Adicionar Endereço
            </Button>
          )}
        </div>

        {/* Lista de Endereços Cadastrados */}
        {addresses.length > 0 && (
          <div className="space-y-4 mb-6">
            {addresses.map((address) => (
              editingAddress === address.id ? (
                // Formulário de edição inline
                <div key={address.id} className="border rounded-md p-4 bg-blue-50 dark:bg-blue-950/20">
                  <AddressInlineForm
                    onSave={handleSaveEditAddress}
                    onCancel={handleCancelEdit}
                    hasExistingAddresses={addresses.length > 1}
                    editingAddress={address}
                    isEditing={true}
                  />
                </div>
              ) : (
                // Card normal do endereço
                <AddressCard
                  key={address.id}
                  address={address}
                  isDefault={address.isDefault}
                  onSetDefault={() => handleSetDefault(address.id)}
                  onEdit={() => handleEditAddress(address.id)}
                  onDelete={() => handleDeleteAddress(address.id)}
                />
              )
            ))}
          </div>
        )}

        {/* Estado Vazio */}
        {addresses.length === 0 && !showForm && (
          <div className="text-center py-12 border border-dashed border-gray-300 rounded-lg bg-gray-50 dark:border-gray-600 dark:bg-gray-800/50">
            <MapPin className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Nenhum endereço cadastrado ainda
            </h4>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              Clique em "Adicionar Endereço" para começar.
            </p>
            <Button
              type="button"
              onClick={() => setShowForm(true)}
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              Adicionar Endereço
            </Button>
          </div>
        )}

        {/* Formulário de Novo Endereço */}
        {showForm && !editingAddress && (
          <div className="border rounded-md p-4 bg-muted/50">
            <AddressInlineForm
              onSave={handleAddAddress}
              onCancel={() => setShowForm(false)}
              hasExistingAddresses={addresses.length > 0}
            />
          </div>
        )}
      </div>
    </div>
  );
};
