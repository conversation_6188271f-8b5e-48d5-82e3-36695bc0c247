import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AddressType, CreateAddressRequest, Address } from '@/types/api';
import { ADDRESS_TYPES, formatCep, validateAddress, createEmptyAddress } from '@/utils/addressUtils';
import { useCepSearch } from '@/hooks/useCepSearch';

interface AddressInlineFormProps {
  onSave: (address: CreateAddressRequest) => void;
  onCancel: () => void;
  hasExistingAddresses?: boolean;
  className?: string;
  editingAddress?: Address; // Novo prop para edição
  isEditing?: boolean; // Flag para indicar se está editando
}

export const AddressInlineForm: React.FC<AddressInlineFormProps> = ({
  onSave,
  onCancel,
  hasExistingAddresses = false,
  className,
  editingAddress,
  isEditing = false
}) => {
  const { searchCep, isLoading: isSearchingCep } = useCepSearch();
  const [formData, setFormData] = useState<CreateAddressRequest>(
    editingAddress ? {
      id: editingAddress.id,
      entityId: editingAddress.entityId || '',
      zipCode: editingAddress.zipCode || '',
      street: editingAddress.street || '',
      number: editingAddress.number || '',
      complement: editingAddress.complement || '',
      district: editingAddress.district || editingAddress.neighborhood || '',
      neighborhood: editingAddress.neighborhood || editingAddress.district || '',
      city: editingAddress.city || '',
      state: editingAddress.state || '',
      country: editingAddress.country || 'Brasil',
      type: editingAddress.type || 'main',
      isDefault: editingAddress.isDefault || false,
    } : createEmptyAddress()
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [cepSearched, setCepSearched] = useState(false);

  // Função para lidar com mudanças nos campos
  const handleChange = (field: keyof CreateAddressRequest, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Remove erro do campo quando o usuário começa a digitar
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Função para buscar CEP automaticamente
  const handleCepChange = async (cep: string) => {
    const formattedCep = formatCep(cep);
    handleChange('zipCode', formattedCep);
    
    // Se o CEP está completo (8 dígitos), busca automaticamente
    const cleanCep = cep.replace(/\D/g, '');
    if (cleanCep.length === 8 && !cepSearched) {
      setCepSearched(true);
      const result = await searchCep(cleanCep);
      
      if (result) {
        setFormData(prev => ({
          ...prev,
          zipCode: result.zipCode,
          street: result.street,
          district: result.district,
          city: result.city,
          state: result.state
        }));
      }
      
      // Reset flag após um tempo para permitir nova busca
      setTimeout(() => setCepSearched(false), 2000);
    }
  };

  // Função para validar e salvar
  const handleSave = () => {
    const validation = validateAddress(formData);

    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    onSave(formData);
  };

  // Verifica se o formulário é válido
  const isFormValid = () => {
    const validation = validateAddress(formData);
    return validation.isValid;
  };

  return (
    <div
      className={cn(
        'border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 p-6 space-y-6',
        'dark:border-gray-600 dark:bg-gray-800/50',
        className
      )}
    >
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
        {isEditing ? 'Editar Endereço' : 'Novo Endereço'}
      </h3>

      {/* Tipo de Endereço */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">
          Tipo de Endereço <span className="text-red-500">*</span>
        </Label>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
          {ADDRESS_TYPES.map((type) => (
            <button
              key={type.value}
              type="button"
              onClick={() => handleChange('type', type.value)}
              className={cn(
                'flex items-center gap-3 p-3 rounded-lg border-2 transition-all',
                'hover:border-blue-300 hover:bg-blue-50 dark:hover:bg-blue-950/20',
                formData.type === type.value
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20'
                  : 'border-gray-200 bg-white dark:border-gray-600 dark:bg-gray-700',
                errors.type && !formData.type && 'border-red-500'
              )}
            >
              <span className="text-lg" role="img" aria-label={type.label}>
                {type.icon}
              </span>
              <span className="font-medium text-sm">{type.label}</span>
            </button>
          ))}
        </div>
        {errors.type && (
          <p className="text-xs text-red-500">{errors.type}</p>
        )}
      </div>

      {/* Grid de Campos */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* CEP */}
        <div className="space-y-2">
          <Label htmlFor="zipCode" className="text-sm font-medium">
            CEP <span className="text-red-500">*</span>
          </Label>
          <div className="relative">
            <Input
              id="zipCode"
              value={formData.zipCode}
              onChange={(e) => handleCepChange(e.target.value)}
              placeholder="00000-000"
              maxLength={9}
              className={cn(
                errors.zipCode && 'border-red-500',
                isSearchingCep && 'pr-10'
              )}
            />
            {isSearchingCep && (
              <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-gray-400" />
            )}
          </div>
          {errors.zipCode && (
            <p className="text-xs text-red-500">{errors.zipCode}</p>
          )}
          {!isSearchingCep && formData.zipCode.length < 9 && (
            <p className="text-xs text-gray-500">Digite o CEP para buscar automaticamente</p>
          )}
        </div>

        {/* Rua */}
        <div className="space-y-2">
          <Label htmlFor="street" className="text-sm font-medium">
            Rua <span className="text-red-500">*</span>
          </Label>
          <Input
            id="street"
            value={formData.street}
            onChange={(e) => handleChange('street', e.target.value)}
            placeholder="Nome da rua"
            disabled={isSearchingCep}
            className={cn(
              errors.street && 'border-red-500',
              isSearchingCep && 'bg-gray-100 dark:bg-gray-700'
            )}
          />
          {errors.street && (
            <p className="text-xs text-red-500">{errors.street}</p>
          )}
        </div>

        {/* Número */}
        <div className="space-y-2">
          <Label htmlFor="number" className="text-sm font-medium">
            Número <span className="text-red-500">*</span>
          </Label>
          <Input
            id="number"
            value={formData.number}
            onChange={(e) => handleChange('number', e.target.value)}
            placeholder="123"
            className={errors.number ? 'border-red-500' : ''}
          />
          {errors.number && (
            <p className="text-xs text-red-500">{errors.number}</p>
          )}
        </div>

        {/* Complemento */}
        <div className="space-y-2">
          <Label htmlFor="complement" className="text-sm font-medium">
            Complemento
          </Label>
          <Input
            id="complement"
            value={formData.complement}
            onChange={(e) => handleChange('complement', e.target.value)}
            placeholder="Apto, Sala, etc."
          />
        </div>

        {/* Bairro */}
        <div className="space-y-2">
          <Label htmlFor="district" className="text-sm font-medium">
            Bairro <span className="text-red-500">*</span>
          </Label>
          <Input
            id="district"
            value={formData.district}
            onChange={(e) => handleChange('district', e.target.value)}
            placeholder="Nome do bairro"
            disabled={isSearchingCep}
            className={cn(
              errors.district && 'border-red-500',
              isSearchingCep && 'bg-gray-100 dark:bg-gray-700'
            )}
          />
          {errors.district && (
            <p className="text-xs text-red-500">{errors.district}</p>
          )}
        </div>

        {/* Cidade */}
        <div className="space-y-2">
          <Label htmlFor="city" className="text-sm font-medium">
            Cidade <span className="text-red-500">*</span>
          </Label>
          <Input
            id="city"
            value={formData.city}
            onChange={(e) => handleChange('city', e.target.value)}
            placeholder="Nome da cidade"
            disabled={isSearchingCep}
            className={cn(
              errors.city && 'border-red-500',
              isSearchingCep && 'bg-gray-100 dark:bg-gray-700'
            )}
          />
          {errors.city && (
            <p className="text-xs text-red-500">{errors.city}</p>
          )}
        </div>
      </div>

      {/* Estado */}
      <div className="space-y-2">
        <Label htmlFor="state" className="text-sm font-medium">
          Estado <span className="text-red-500">*</span>
        </Label>
        <Input
          id="state"
          value={formData.state}
          onChange={(e) => handleChange('state', e.target.value.toUpperCase())}
          placeholder="SP"
          maxLength={2}
          disabled={isSearchingCep}
          className={cn(
            'w-20',
            errors.state && 'border-red-500',
            isSearchingCep && 'bg-gray-100 dark:bg-gray-700'
          )}
        />
        {errors.state && (
          <p className="text-xs text-red-500">{errors.state}</p>
        )}
      </div>

      {/* Checkbox Endereço Padrão */}
      {hasExistingAddresses && (
        <div className="flex items-center space-x-2">
          <Checkbox
            id="isDefault"
            checked={formData.isDefault || false}
            onCheckedChange={(checked) => handleChange('isDefault', checked as boolean)}
          />
          <Label htmlFor="isDefault" className="text-sm">
            Definir como endereço padrão
          </Label>
        </div>
      )}

      {/* Botões de Ação */}
      <div className="flex justify-end gap-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          Cancelar
        </Button>
        <Button
          type="button"
          onClick={handleSave}
          disabled={!isFormValid() || isSearchingCep}
        >
          {isEditing ? 'Salvar Alterações' : 'Adicionar Endereço'}
        </Button>
      </div>
    </div>
  );
};
