import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Address, AddressType, CreateAddressRequest } from '@/types/api';
import { AddressCard } from './AddressCard';
import { AddressInlineForm } from './AddressInlineForm';
import {
  useEntityAddresses,
  useCreateEntityAddress,
  useUpdateEntityAddress,
  useDeleteEntityAddress
} from '@/hooks/api/useEntityAddresses';
import { mapBackendAddressType } from '@/utils/addressUtils';
import { toast } from 'sonner';

interface EntityAddressSectionProps {
  entityId?: string; // ID da entidade (para modo de edição)
  addresses?: (Address & { type?: AddressType })[]; // Endereços locais (para modo de criação)
  onAddressesChange?: (addresses: (Address & { type?: AddressType })[]) => void; // Callback para mudanças
  className?: string;
  isEditMode?: boolean; // Flag para determinar se está em modo de edição
}

export const EntityAddressSection: React.FC<EntityAddressSectionProps> = ({
  entityId,
  addresses: localAddresses = [],
  onAddressesChange,
  className,
  isEditMode = false
}) => {
  const [showForm, setShowForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<string | null>(null);
  const [internalAddresses, setInternalAddresses] = useState<(Address & { type?: AddressType })[]>(localAddresses);

  // Hooks para API (apenas se estiver em modo de edição e tiver entityId)
  const { 
    data: apiAddresses = [], 
    isLoading, 
    error,
    refetch 
  } = useEntityAddresses(entityId || '', {
    enabled: isEditMode && !!entityId,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    staleTime: 0
  });

  const createAddressMutation = useCreateEntityAddress();
  const updateAddressMutation = useUpdateEntityAddress();
  const deleteAddressMutation = useDeleteEntityAddress();

  // Mapear endereços da API para o formato esperado pelo frontend
  const mappedApiAddresses = apiAddresses.map(addr => ({
    ...addr,
    type: mapBackendAddressType((addr as any).addressType),
  }));

  // Determinar quais endereços usar
  const addresses = isEditMode && entityId ? mappedApiAddresses : internalAddresses;

  // Sincronizar endereços locais quando mudarem
  useEffect(() => {
    if (!isEditMode) {
      setInternalAddresses(localAddresses);
    }
  }, [localAddresses, isEditMode]);

  // Notificar mudanças nos endereços
  const notifyAddressChange = (newAddresses: (Address & { type?: AddressType })[]) => {
    if (!isEditMode && onAddressesChange) {
      onAddressesChange(newAddresses);
    }
  };

  // Função para adicionar novo endereço
  const handleAddAddress = async (addressData: CreateAddressRequest) => {
    try {
      if (isEditMode && entityId) {
        // Modo de edição: usar API
        await createAddressMutation.mutateAsync({
          entityId,
          data: addressData
        });
        // Refetch para garantir dados atualizados
        setTimeout(() => refetch(), 500);
        toast.success('Endereço adicionado com sucesso!');
      } else {
        // Modo de criação: gerenciar localmente
        const newAddress: Address & { type?: AddressType } = {
          id: `temp-${Date.now()}`,
          entityId: entityId || '',
          ...addressData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        const newAddresses = [...internalAddresses, newAddress];
        setInternalAddresses(newAddresses);
        notifyAddressChange(newAddresses);
        toast.success('Endereço adicionado!');
      }
      setShowForm(false);
    } catch (error) {
      console.error('Erro ao adicionar endereço:', error);
      toast.error('Erro ao adicionar endereço. Tente novamente.');
    }
  };

  // Função para atualizar endereço
  const handleUpdateAddress = async (addressId: string, updates: Partial<Address>) => {
    try {
      if (isEditMode && entityId) {
        // Modo de edição: usar API
        await updateAddressMutation.mutateAsync({
          entityId,
          addressId,
          data: updates
        });
        setTimeout(() => refetch(), 500);
        toast.success('Endereço atualizado com sucesso!');
      } else {
        // Modo de criação: atualizar localmente
        const newAddresses = internalAddresses.map(addr =>
          addr.id === addressId ? { ...addr, ...updates } : addr
        );
        setInternalAddresses(newAddresses);
        notifyAddressChange(newAddresses);
        toast.success('Endereço atualizado!');
      }
      setEditingAddress(null);
    } catch (error) {
      console.error('Erro ao atualizar endereço:', error);
      toast.error('Erro ao atualizar endereço. Tente novamente.');
    }
  };

  // Função para deletar endereço
  const handleDeleteAddress = async (addressId: string) => {
    try {
      if (isEditMode && entityId) {
        // Modo de edição: usar API
        await deleteAddressMutation.mutateAsync({
          entityId,
          addressId
        });
        setTimeout(() => refetch(), 500);
        toast.success('Endereço removido com sucesso!');
      } else {
        // Modo de criação: remover localmente
        const newAddresses = internalAddresses.filter(addr => addr.id !== addressId);
        setInternalAddresses(newAddresses);
        notifyAddressChange(newAddresses);
        toast.success('Endereço removido!');
      }
    } catch (error) {
      console.error('Erro ao remover endereço:', error);
      toast.error('Erro ao remover endereço. Tente novamente.');
    }
  };

  // Função para definir endereço padrão
  const handleSetDefault = async (addressId: string) => {
    try {
      if (isEditMode && entityId) {
        // Modo de edição: usar API
        await updateAddressMutation.mutateAsync({
          entityId,
          addressId,
          data: { isDefault: true }
        });
        setTimeout(() => refetch(), 500);
        toast.success('Endereço padrão atualizado!');
      } else {
        // Modo de criação: atualizar localmente
        const newAddresses = internalAddresses.map(addr => ({
          ...addr,
          isDefault: addr.id === addressId
        }));
        setInternalAddresses(newAddresses);
        notifyAddressChange(newAddresses);
        toast.success('Endereço padrão atualizado!');
      }
    } catch (error) {
      console.error('Erro ao definir endereço padrão:', error);
      toast.error('Erro ao definir endereço padrão. Tente novamente.');
    }
  };

  // Função para editar endereço
  const handleEditAddress = (addressId: string) => {
    setEditingAddress(addressId);
    setShowForm(false);
  };

  // Função para cancelar edição
  const handleCancelEdit = () => {
    setEditingAddress(null);
  };

  if (isEditMode && isLoading) {
    return (
      <div className={cn('space-y-6', className)}>
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Endereços
            </h3>
          </div>
          <div className="text-center p-4 border border-dashed rounded-md">
            <p className="text-sm text-muted-foreground">Carregando endereços...</p>
          </div>
        </div>
      </div>
    );
  }

  if (isEditMode && error) {
    return (
      <div className={cn('space-y-6', className)}>
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Endereços
            </h3>
          </div>
          <div className="text-center p-4 border border-dashed rounded-md">
            <p className="text-sm text-destructive">Erro ao carregar endereços: {error.message}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Separador visual */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        {/* Header da Seção */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Endereços ({addresses.length})
          </h3>
          {!showForm && !editingAddress && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowForm(true)}
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              Adicionar Endereço
            </Button>
          )}
        </div>

        {/* Lista de Endereços Cadastrados */}
        {addresses.length > 0 && (
          <div className="space-y-4 mb-6">
            {addresses.map((address) => (
              editingAddress === address.id ? (
                // Formulário de edição inline
                <div key={address.id} className="border rounded-md p-4 bg-blue-50 dark:bg-blue-950/20">
                  <AddressInlineForm
                    onSave={(data) => handleUpdateAddress(address.id, data)}
                    onCancel={handleCancelEdit}
                    hasExistingAddresses={addresses.length > 1}
                    editingAddress={address}
                    isEditing={true}
                  />
                </div>
              ) : (
                // Card normal do endereço
                <AddressCard
                  key={address.id}
                  address={address}
                  isDefault={address.isDefault}
                  onSetDefault={() => handleSetDefault(address.id)}
                  onEdit={() => handleEditAddress(address.id)}
                  onDelete={() => handleDeleteAddress(address.id)}
                />
              )
            ))}
          </div>
        )}

        {/* Estado Vazio */}
        {addresses.length === 0 && !showForm && (
          <div className="text-center py-12 border border-dashed border-gray-300 rounded-lg bg-gray-50 dark:border-gray-600 dark:bg-gray-800/50">
            <MapPin className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Nenhum endereço cadastrado ainda
            </h4>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              Clique em "Adicionar Endereço" para começar.
            </p>
            <Button
              type="button"
              onClick={() => setShowForm(true)}
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              Adicionar Endereço
            </Button>
          </div>
        )}

        {/* Formulário de Novo Endereço */}
        {showForm && !editingAddress && (
          <div className="border rounded-md p-4 bg-muted/50">
            <AddressInlineForm
              onSave={handleAddAddress}
              onCancel={() => setShowForm(false)}
              hasExistingAddresses={addresses.length > 0}
            />
          </div>
        )}
      </div>
    </div>
  );
};
