import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Address, AddressType, CreateAddressRequest } from '@/types/api';
import { AddressCard } from './AddressCard';
import { AddressInlineForm } from './AddressInlineForm';
import { useEntityAddresses, useCreateEntityAddress, useUpdateEntityAddress, useDeleteEntityAddress, useRefreshEntityAddresses } from '@/hooks/api/useEntityAddresses';
import { mapBackendAddressType } from '@/utils/addressUtils';
import { toast } from 'sonner';

interface EntityAddressManagerProps {
  entityId: string;
  className?: string;
}

export const EntityAddressManager: React.FC<EntityAddressManagerProps> = ({
  entityId,
  className
}) => {
  const [showForm, setShowForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<string | null>(null);

  // Hooks para gerenciar endereços da entidade
  const { data: rawAddresses = [], isLoading, error, refetch } = useEntityAddresses(entityId, {
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    staleTime: 0 // Sempre buscar dados frescos para este componente
  });
  const createAddressMutation = useCreateEntityAddress();
  const updateAddressMutation = useUpdateEntityAddress();
  const deleteAddressMutation = useDeleteEntityAddress();
  const refreshEntityAddresses = useRefreshEntityAddresses();

  // Mapear endereços da API para o formato esperado pelo frontend
  const addresses = rawAddresses.map(addr => {
    const mappedType = mapBackendAddressType((addr as any).addressType);
    console.log('[EntityAddressManager] Mapeando endereço:', {
      id: addr.id,
      originalAddressType: (addr as any).addressType,
      mappedType,
      street: addr.street
    });

    return {
      ...addr,
      type: mappedType,
    };
  });

  // Efeito para forçar refresh quando o componente é montado
  useEffect(() => {
    if (entityId) {
      // Pequeno delay para garantir que o componente está totalmente montado
      const timeoutId = setTimeout(() => {
        refreshEntityAddresses(entityId);
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [entityId, refreshEntityAddresses]);

  // Função para adicionar novo endereço
  const handleAddAddress = async (addressData: CreateAddressRequest) => {
    try {
      await createAddressMutation.mutateAsync({
        entityId,
        data: addressData
      });
      setShowForm(false);

      // Forçar refresh adicional após sucesso
      setTimeout(() => {
        refreshEntityAddresses(entityId);
      }, 500);
    } catch (error) {
      console.error('Erro ao adicionar endereço:', error);
      // O toast de erro já é mostrado pelo hook
    }
  };

  // Função para atualizar endereço
  const handleUpdateAddress = async (addressId: string, updates: Partial<Address>) => {
    try {
      await updateAddressMutation.mutateAsync({
        entityId,
        addressId,
        data: updates
      });
      setEditingAddress(null);

      // Forçar refresh adicional após sucesso
      setTimeout(() => {
        refreshEntityAddresses(entityId);
      }, 500);
    } catch (error) {
      console.error('Erro ao atualizar endereço:', error);
      // O toast de erro já é mostrado pelo hook
    }
  };

  // Função para deletar endereço
  const handleDeleteAddress = async (addressId: string) => {
    try {
      await deleteAddressMutation.mutateAsync({
        entityId,
        addressId
      });

      // Forçar refresh adicional após sucesso
      setTimeout(() => {
        refreshEntityAddresses(entityId);
      }, 500);
    } catch (error) {
      console.error('Erro ao deletar endereço:', error);
      // O toast de erro já é mostrado pelo hook
    }
  };

  // Função para definir endereço padrão
  const handleSetDefault = async (addressId: string) => {
    try {
      await updateAddressMutation.mutateAsync({
        entityId,
        addressId,
        data: { isDefault: true }
      });
    } catch (error) {
      console.error('Erro ao definir endereço padrão:', error);
      // O toast de erro já é mostrado pelo hook
    }
  };

  // Função para editar endereço
  const handleEditAddress = (addressId: string) => {
    setEditingAddress(addressId);
    setShowForm(false); // Fecha o formulário de novo endereço se estiver aberto
  };

  // Função para salvar edição de endereço
  const handleSaveEditAddress = async (addressData: CreateAddressRequest) => {
    if (!editingAddress) return;

    try {
      await updateAddressMutation.mutateAsync({
        entityId,
        addressId: editingAddress,
        data: addressData
      });
      setEditingAddress(null);
    } catch (error) {
      console.error('Erro ao atualizar endereço:', error);
      // O toast de erro já é mostrado pelo hook
    }
  };

  // Função para cancelar edição
  const handleCancelEdit = () => {
    setEditingAddress(null);
  };

  if (isLoading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="flex justify-between items-center">
          <h3 className="text-base font-medium">Endereços</h3>
        </div>
        <div className="text-center p-4 border border-dashed rounded-md">
          <p className="text-sm text-muted-foreground">Carregando endereços...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="flex justify-between items-center">
          <h3 className="text-base font-medium">Endereços</h3>
        </div>
        <div className="text-center p-4 border border-dashed rounded-md">
          <p className="text-sm text-destructive">Erro ao carregar endereços: {error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header da Seção */}
      <div className="flex justify-between items-center">
        <h3 className="text-base font-medium">
          Endereços {addresses.length > 0 && `(${addresses.length})`}
        </h3>
        {!showForm && !editingAddress && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setShowForm(true)}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Adicionar Endereço
          </Button>
        )}
      </div>

      {/* Lista de Endereços Cadastrados */}
      {addresses.length > 0 && (
        <div className="space-y-4 mb-6">
          {addresses.map((address) => (
            editingAddress === address.id ? (
              // Formulário de edição inline
              <div key={address.id} className="border rounded-md p-4 bg-blue-50 dark:bg-blue-950/20">
                <AddressInlineForm
                  onSave={handleSaveEditAddress}
                  onCancel={handleCancelEdit}
                  hasExistingAddresses={addresses.length > 1}
                  editingAddress={address}
                  isEditing={true}
                />
              </div>
            ) : (
              // Card normal do endereço
              <AddressCard
                key={address.id}
                address={address}
                isDefault={address.isDefault}
                onSetDefault={() => handleSetDefault(address.id)}
                onEdit={() => handleEditAddress(address.id)}
                onDelete={() => handleDeleteAddress(address.id)}
              />
            )
          ))}
        </div>
      )}

      {/* Estado Vazio */}
      {addresses.length === 0 && !showForm && (
        <div className="text-center p-6 border border-dashed rounded-md">
          <MapPin className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
          <p className="text-sm text-muted-foreground mb-4">
            Nenhum endereço cadastrado para esta entidade.
          </p>
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowForm(true)}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Adicionar Primeiro Endereço
          </Button>
        </div>
      )}

      {/* Formulário Inline para Novo Endereço */}
      {showForm && !editingAddress && (
        <div className="border rounded-md p-4 bg-muted/50">
          <AddressInlineForm
            onSave={handleAddAddress}
            onCancel={() => setShowForm(false)}
            hasExistingAddresses={addresses.length > 0}
          />
        </div>
      )}
    </div>
  );
};
