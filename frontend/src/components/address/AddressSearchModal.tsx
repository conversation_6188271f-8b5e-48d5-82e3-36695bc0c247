
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Plus, Loader2 } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { addressService } from "@/services/api";
import { Address } from "@/types/api";
import { toast } from "sonner";

interface AddressSearchModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelect: (address: Address) => void;
  onCreateNew: () => void;
}

export function AddressSearchModal({
  open,
  onOpenChange,
  onSelect,
  onCreateNew
}: AddressSearchModalProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [results, setResults] = useState<Address[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    if (open) {
      setSearchTerm("");
      setResults([]);
    }
  }, [open]);

  const handleSearch = async () => {
    if (!searchTerm || searchTerm.length < 3) return;

    setIsSearching(true);
    try {
      // Buscar endereços usando o serviço real
      const response = await addressService.getAddresses(1, 10, searchTerm);
      setResults(response.items);
    } catch (error: any) {
      console.error("Failed to search addresses:", error);
      toast.error(`Erro ao buscar endereços: ${error.message || 'Tente novamente mais tarde'}`);
    } finally {
      setIsSearching(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Buscar Endereço</DialogTitle>
          <DialogDescription>
            Pesquise por endereços cadastrados ou adicione um novo.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <div className="flex gap-2">
            <div className="flex-1">
              <Input
                placeholder="Buscar por CEP, rua, cidade ou estado..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
              />
            </div>
            <Button
              onClick={handleSearch}
              disabled={isSearching || searchTerm.length < 3}
              className="gap-2"
            >
              <Search className="h-4 w-4" />
              Buscar
            </Button>
          </div>

          {isSearching ? (
            <div className="text-center py-8">
              <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">Buscando endereços...</p>
            </div>
          ) : results.length > 0 ? (
            <div className="border rounded-md overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>CEP</TableHead>
                    <TableHead>Endereço</TableHead>
                    <TableHead>Cidade/Estado</TableHead>
                    <TableHead className="w-[100px]">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {results.map((address) => (
                    <TableRow key={address.id}>
                      <TableCell>
                        <Badge variant="outline">{address.zipCode}</Badge>
                      </TableCell>
                      <TableCell>
                        {address.street}, {address.number || 'S/N'}
                        {address.complement && ` - ${address.complement}`}
                        <div className="text-xs text-gray-500">{address.district || address.neighborhood}</div>
                      </TableCell>
                      <TableCell>{address.city}/{address.state}</TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onSelect(address)}
                          className="w-full"
                        >
                          Selecionar
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : searchTerm.length > 0 && !isSearching ? (
            <div className="text-center py-8 border rounded-md">
              <p className="text-gray-500">Nenhum endereço encontrado para "{searchTerm}"</p>
              <Button
                variant="outline"
                className="mt-4 gap-2"
                onClick={onCreateNew}
              >
                <Plus className="h-4 w-4" />
                Cadastrar Novo Endereço
              </Button>
            </div>
          ) : null}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={onCreateNew} className="gap-2">
            <Plus className="h-4 w-4" />
            Novo Endereço
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
