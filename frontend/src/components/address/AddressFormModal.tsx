
import { useState } from "react";
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { MapPin, Search, Loader2 } from "lucide-react";
import { addressService } from "@/services/api";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Address, CreateAddressRequest } from "@/types/api";
import { toast } from "sonner";

interface AddressFormModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (address: Address) => void;
  entityId?: string;  // ID da entidade (empresa, cliente, fornecedor)
  address?: Address;
}

// Brazilian states list
const brazilianStates = [
  { value: 'AC', label: 'Acre' },
  { value: 'AL', label: 'Alagoas' },
  { value: 'AP', label: 'Amapá' },
  { value: 'AM', label: 'Amazonas' },
  { value: 'BA', label: 'Bahia' },
  { value: 'CE', label: 'Ceará' },
  { value: 'DF', label: 'Distrito Federal' },
  { value: 'ES', label: 'Espírito Santo' },
  { value: 'GO', label: 'Goiás' },
  { value: 'MA', label: 'Maranhão' },
  { value: 'MT', label: 'Mato Grosso' },
  { value: 'MS', label: 'Mato Grosso do Sul' },
  { value: 'MG', label: 'Minas Gerais' },
  { value: 'PA', label: 'Pará' },
  { value: 'PB', label: 'Paraíba' },
  { value: 'PR', label: 'Paraná' },
  { value: 'PE', label: 'Pernambuco' },
  { value: 'PI', label: 'Piauí' },
  { value: 'RJ', label: 'Rio de Janeiro' },
  { value: 'RN', label: 'Rio Grande do Norte' },
  { value: 'RS', label: 'Rio Grande do Sul' },
  { value: 'RO', label: 'Rondônia' },
  { value: 'RR', label: 'Roraima' },
  { value: 'SC', label: 'Santa Catarina' },
  { value: 'SP', label: 'São Paulo' },
  { value: 'SE', label: 'Sergipe' },
  { value: 'TO', label: 'Tocantins' }
];

export function AddressFormModal({
  open,
  onOpenChange,
  onSave,
  entityId = "",  // Provide a default empty string value
  address
}: AddressFormModalProps) {
  const [isSearchingCep, setIsSearchingCep] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const [formData, setFormData] = useState<CreateAddressRequest>({
    id: address?.id,
    entityId: entityId,
    zipCode: address?.zipCode || '',
    street: address?.street || '',
    number: address?.number || '',
    complement: address?.complement || '',
    district: address?.district || address?.neighborhood || '',
    neighborhood: address?.neighborhood || address?.district || '',
    city: address?.city || '',
    state: address?.state || '',
    country: address?.country || 'Brasil',
    isDefault: false,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({ ...formData, [name]: value });
  };

  const searchCep = async () => {
    if (!formData.zipCode || formData.zipCode.length < 8) return;

    setIsSearchingCep(true);
    try {
      // Buscar CEP usando o serviço de CEP
      const cepData = await fetch(`https://viacep.com.br/ws/${formData.zipCode.replace(/\D/g, '')}/json/`);
      const addressData = await cepData.json();

      if (addressData && !addressData.erro) {
        setFormData({
          ...formData,
          street: addressData.logradouro || formData.street,
          neighborhood: addressData.bairro || formData.neighborhood,
          city: addressData.localidade || formData.city,
          state: addressData.uf || formData.state,
        });
        toast.success('CEP encontrado com sucesso!');
      } else {
        toast.error('CEP não encontrado. Por favor, verifique o CEP informado.');
      }
    } catch (error: any) {
      console.error("Failed to search CEP:", error);
      toast.error(`Erro ao buscar CEP: ${error.message || 'Tente novamente mais tarde'}`);
    } finally {
      setIsSearchingCep(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setIsSaving(true);
    try {
      let savedAddress: Address;

      if (address?.id) {
        // Atualizar endereço existente
        savedAddress = await addressService.updateAddress(address.id, formData);
        toast.success('Endereço atualizado com sucesso!');
      } else {
        // Criar novo endereço
        savedAddress = await addressService.createAddress(formData);
        toast.success('Endereço criado com sucesso!');
      }

      onSave(savedAddress);
      onOpenChange(false);
    } catch (error: any) {
      console.error("Failed to save address:", error);
      toast.error(`Erro ao salvar endereço: ${error.message || 'Tente novamente mais tarde'}`);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">
              {address ? "Editar Endereço" : "Novo Endereço"}
            </DialogTitle>
            <DialogDescription>
              Preencha os dados do endereço a ser cadastrado.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            <div className="space-y-2">
              <Label htmlFor="zipCode">
                CEP<span className="text-red-500">*</span>
              </Label>
              <div className="flex gap-2">
                <Input
                  id="zipCode"
                  name="zipCode"
                  value={formData.zipCode}
                  onChange={handleChange}
                  placeholder="00000-000"
                  required
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={searchCep}
                  disabled={isSearchingCep || !formData.zipCode || formData.zipCode.length < 8}
                  className="min-w-[120px]"
                >
                  {isSearchingCep ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Search className="h-4 w-4 mr-2" />
                  )}
                  Buscar CEP
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="street">
                Rua/Avenida<span className="text-red-500">*</span>
              </Label>
              <Input
                id="street"
                name="street"
                value={formData.street}
                onChange={handleChange}
                placeholder="Nome da rua ou avenida"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="number">
                  Número<span className="text-red-500">*</span>
                </Label>
                <Input
                  id="number"
                  name="number"
                  value={formData.number}
                  onChange={handleChange}
                  placeholder="Número"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="complement">Complemento</Label>
                <Input
                  id="complement"
                  name="complement"
                  value={formData.complement}
                  onChange={handleChange}
                  placeholder="Apto, Sala, Conjunto, etc"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="neighborhood">
                Bairro<span className="text-red-500">*</span>
              </Label>
              <Input
                id="neighborhood"
                name="neighborhood"
                value={formData.neighborhood}
                onChange={handleChange}
                placeholder="Bairro"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">
                  Cidade<span className="text-red-500">*</span>
                </Label>
                <Input
                  id="city"
                  name="city"
                  value={formData.city}
                  onChange={handleChange}
                  placeholder="Cidade"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">
                  Estado<span className="text-red-500">*</span>
                </Label>
                <Select
                  value={formData.state}
                  onValueChange={(value) => handleSelectChange('state', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um estado" />
                  </SelectTrigger>
                  <SelectContent>
                    {brazilianStates.map((state) => (
                      <SelectItem key={state.value} value={state.value}>
                        {state.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="country">País</Label>
              <Input
                id="country"
                name="country"
                value={formData.country}
                onChange={handleChange}
                placeholder="País"
                readOnly
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancelar
            </Button>
            <Button type="submit" className="gap-2" disabled={isSaving}>
              {isSaving ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Salvando...
                </>
              ) : (
                <>
                  <MapPin className="h-4 w-4" />
                  Salvar Endereço
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
