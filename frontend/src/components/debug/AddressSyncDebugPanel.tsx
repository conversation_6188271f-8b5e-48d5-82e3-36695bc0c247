import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Bug, CheckCircle, XCircle, Info } from 'lucide-react';
import { testAddressSync, debugAddressCache, AddressSyncTestResult } from '@/utils/addressSyncTest';
import { toast } from 'sonner';

interface AddressSyncDebugPanelProps {
  entityId: string;
  entityName?: string;
}

export const AddressSyncDebugPanel: React.FC<AddressSyncDebugPanelProps> = ({
  entityId,
  entityName
}) => {
  const [testResults, setTestResults] = useState<AddressSyncTestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const runSyncTests = async () => {
    if (!entityId) {
      toast.error('ID da entidade é obrigatório para executar os testes');
      return;
    }

    setIsRunningTests(true);
    try {
      const results = await testAddressSync.runAllTests(entityId);
      setTestResults(results);
      
      const successCount = results.filter(r => r.success).length;
      const totalTests = results.length;
      
      if (successCount === totalTests) {
        toast.success(`Todos os ${totalTests} testes passaram! Sincronização funcionando corretamente.`);
      } else {
        toast.warning(`${successCount}/${totalTests} testes passaram. Verifique os resultados.`);
      }
    } catch (error) {
      toast.error(`Erro ao executar testes: ${error}`);
    } finally {
      setIsRunningTests(false);
    }
  };

  const forceRefresh = async () => {
    if (!entityId) {
      toast.error('ID da entidade é obrigatório');
      return;
    }

    try {
      const result = await testAddressSync.forceRefresh(entityId);
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error(`Erro ao forçar refresh: ${error}`);
    }
  };

  const showDebugInfo = () => {
    if (!entityId) {
      toast.error('ID da entidade é obrigatório');
      return;
    }

    debugAddressCache(entityId);
    toast.info('Informações de debug exibidas no console do navegador');
  };

  const getResultIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <XCircle className="h-4 w-4 text-red-600" />
    );
  };

  const getResultBadge = (success: boolean) => {
    return (
      <Badge variant={success ? "default" : "destructive"}>
        {success ? "Passou" : "Falhou"}
      </Badge>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bug className="h-5 w-5" />
          Debug - Sincronização de Endereços
          {entityName && (
            <Badge variant="outline" className="ml-2">
              {entityName}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Controles */}
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={runSyncTests}
            disabled={isRunningTests || !entityId}
            className="gap-2"
          >
            {isRunningTests ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <CheckCircle className="h-4 w-4" />
            )}
            Executar Testes de Sincronização
          </Button>
          
          <Button
            variant="outline"
            onClick={forceRefresh}
            disabled={!entityId}
            className="gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Forçar Refresh
          </Button>
          
          <Button
            variant="outline"
            onClick={showDebugInfo}
            disabled={!entityId}
            className="gap-2"
          >
            <Info className="h-4 w-4" />
            Debug Console
          </Button>
        </div>

        {/* Informações da Entidade */}
        {entityId && (
          <div className="p-3 bg-muted rounded-md">
            <p className="text-sm">
              <strong>ID da Entidade:</strong> {entityId}
            </p>
            {entityName && (
              <p className="text-sm">
                <strong>Nome:</strong> {entityName}
              </p>
            )}
          </div>
        )}

        {/* Resultados dos Testes */}
        {testResults.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Resultados dos Testes:</h4>
            {testResults.map((result, index) => (
              <div
                key={index}
                className="flex items-start gap-3 p-3 border rounded-md"
              >
                {getResultIcon(result.success)}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium">Teste {index + 1}</span>
                    {getResultBadge(result.success)}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {result.message}
                  </p>
                  {result.details && (
                    <details className="mt-2">
                      <summary className="text-xs cursor-pointer text-muted-foreground hover:text-foreground">
                        Ver detalhes
                      </summary>
                      <pre className="mt-1 text-xs bg-muted p-2 rounded overflow-auto">
                        {JSON.stringify(result.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Instruções */}
        <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-md">
          <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
            Como testar a correção de sincronização:
          </h4>
          <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1 list-decimal list-inside">
            <li>Execute os testes de sincronização acima</li>
            <li>Adicione um novo endereço usando o formulário</li>
            <li>Navegue para outra página e retorne</li>
            <li>Verifique se o endereço aparece sem precisar atualizar a página (F5)</li>
            <li>Execute os testes novamente para confirmar que tudo está funcionando</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
};
