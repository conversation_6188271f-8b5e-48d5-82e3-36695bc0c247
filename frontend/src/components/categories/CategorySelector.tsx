import { useState, useEffect } from "react";
import { RefreshCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Category } from "@/types/category";
import { useCategoryTree } from "@/hooks/api/useCategories";
import { toast } from "@/hooks/use-toast";

interface CategorySelectorProps {
  label?: string;
  value?: string;
  onValueChange: (value: string) => void;
  transactionType: "payable" | "receivable";
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  showRefreshButton?: boolean;
}

// Função para achatar a árvore de categorias em uma lista plana
const flattenCategoryTree = (categories: Category[]): Category[] => {
  const result: Category[] = [];
  
  const flatten = (cats: Category[], level = 0) => {
    cats.forEach(cat => {
      result.push({
        ...cat,
        level,
        // Adicionar indentação visual baseada no nível
        name: level > 0 ? `${'  '.repeat(level)}${cat.name}` : cat.name
      });
      
      if (cat.children && cat.children.length > 0) {
        flatten(cat.children, level + 1);
      }
    });
  };
  
  flatten(categories);
  return result;
};

export default function CategorySelector({
  label = "Categoria",
  value,
  onValueChange,
  transactionType,
  placeholder = "Selecione uma categoria...",
  disabled = false,
  className = "",
  showRefreshButton = true
}: CategorySelectorProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Buscar categorias da API filtradas por tipo
  const {
    data: categoriesTreeData,
    isLoading,
    refetch
  } = useCategoryTree(transactionType);

  // Converter árvore em lista plana para o select
  const categories = categoriesTreeData ? flattenCategoryTree(categoriesTreeData) : [];

  // Filtrar apenas categorias do tipo correto
  const filteredCategories = categories.filter(cat => cat.transactionType === transactionType);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refetch();
      toast({
        title: "Categorias atualizadas",
        description: "A lista de categorias foi atualizada com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro ao atualizar",
        description: "Não foi possível atualizar a lista de categorias.",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Verificar se a categoria selecionada ainda é válida
  useEffect(() => {
    if (value && filteredCategories.length > 0) {
      const selectedCategory = filteredCategories.find(cat => cat.id === value);
      if (!selectedCategory) {
        // Categoria selecionada não é mais válida, limpar seleção
        onValueChange("");
      }
    }
  }, [value, filteredCategories, onValueChange]);

  return (
    <div className={className}>
      {label && (
        <div className="flex items-center justify-between h-5 mb-1">
          <Label>{label}</Label>
          {showRefreshButton && (
            <Button 
              type="button"
              variant="ghost" 
              size="icon" 
              className="h-5 w-5" 
              onClick={handleRefresh}
              disabled={isRefreshing || isLoading}
              title="Atualizar categorias"
            >
              <RefreshCcw className={`h-3.5 w-3.5 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          )}
        </div>
      )}
      
      <Select 
        value={value || ""}
        onValueChange={onValueChange}
        disabled={disabled || isLoading}
      >
        <SelectTrigger>
          <SelectValue placeholder={isLoading ? "Carregando..." : placeholder} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="">Nenhuma categoria</SelectItem>
          {filteredCategories.map((category) => (
            <SelectItem key={category.id} value={category.id}>
              {category.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
