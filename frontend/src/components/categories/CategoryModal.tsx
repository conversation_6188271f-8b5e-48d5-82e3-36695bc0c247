import { useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { v4 as uuidv4 } from 'uuid';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Category, CategoryFormValues } from "@/types/category";

// Schema de validação do formulário
const categorySchema = z.object({
  name: z.string().min(2, { message: "Nome deve ter pelo menos 2 caracteres" }),
  description: z.string().optional(),
  transactionType: z.enum(["payable", "receivable"], {
    required_error: "Selecione um tipo para esta categoria"
  }),
  parentCategoryId: z.string().nullable(),
  isActive: z.boolean().default(true),
});

// Tipo derivado do schema
type FormData = z.infer<typeof categorySchema>;

interface CategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (category: CategoryFormValues) => void;
  category?: Category;
  allCategories?: Category[];
  parentCategory?: Category | null;
}

export default function CategoryModal({
  isOpen,
  onClose,
  onSave,
  category,
  allCategories = [],
  parentCategory = null
}: CategoryModalProps) {
  const form = useForm<FormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: "",
      description: "",
      transactionType: "payable",
      parentCategoryId: null,
      isActive: true,
    },
  });

  useEffect(() => {
    if (category) {
      form.reset({
        name: category.name,
        description: "", // Opcional - não existe no tipo Category
        transactionType: category.transactionType,
        parentCategoryId: category.parentCategoryId || null,
        isActive: true, // Opcional - não existe no tipo Category
      });
    } else {
      form.reset({
        name: "",
        description: "",
        transactionType: parentCategory?.transactionType || "payable",
        parentCategoryId: parentCategory?.id || null,
        isActive: true,
      });
    }
  }, [category, parentCategory, form, isOpen]);

  // Efeito para limpar a categoria pai quando o tipo de transação mudar
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "transactionType") {
        // Verificar se a categoria pai atual é compatível com o novo tipo
        const currentParentId = form.getValues("parentCategoryId");
        if (currentParentId) {
          const currentParent = allCategories.find(cat => cat.id === currentParentId);
          if (currentParent && currentParent.transactionType !== value.transactionType) {
            // Limpar a categoria pai se não for compatível
            form.setValue("parentCategoryId", null);
          }
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form, allCategories]);

  const onSubmit = (data: FormData) => {
    const newCategory: CategoryFormValues = {
      id: category?.id,
      name: data.name,
      transactionType: data.transactionType,
      parentCategoryId: data.parentCategoryId,
    };

    onSave(newCategory);
    onClose();
  };

  // Função para filtrar as opções de categoria pai
  // Para evitar que uma categoria seja pai dela mesma ou de seus descendentes
  // E garantir que apenas categorias do mesmo tipo sejam exibidas
  const filterParentOptions = () => {
    const selectedTransactionType = form.watch("transactionType");

    // Filtrar por tipo de transação primeiro
    let filteredByType = allCategories.filter(cat => cat.transactionType === selectedTransactionType);

    if (!category) return filteredByType;

    // Encontre todos os descendentes da categoria atual
    const descendants = findAllDescendants(category.id, allCategories);

    // Filtre para excluir a categoria atual e seus descendentes
    return filteredByType.filter(
      cat => cat.id !== category.id && !descendants.some(desc => desc.id === cat.id)
    );
  };

  // Função recursiva para encontrar todos os descendentes de uma categoria
  const findAllDescendants = (categoryId: string, categories: Category[]): Category[] => {
    const directChildren = categories.filter(cat => cat.parentCategoryId === categoryId);

    const allDescendants = [...directChildren];

    directChildren.forEach(child => {
      const childDescendants = findAllDescendants(child.id, categories);
      allDescendants.push(...childDescendants);
    });

    return allDescendants;
  };

  const parentOptions = filterParentOptions();

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {category
              ? "Editar Categoria"
              : parentCategory
                ? `Nova Subcategoria - ${parentCategory.name}`
                : "Nova Categoria"
            }
          </DialogTitle>
          <DialogDescription>
            {category
              ? "Modifique os detalhes da categoria conforme necessário."
              : parentCategory
                ? `Criar uma nova subcategoria dentro de "${parentCategory.name}".`
                : "Preencha os dados para criar uma nova categoria."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome</FormLabel>
                  <FormControl>
                    <Input placeholder="Nome da categoria" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Descrição da categoria (opcional)"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="transactionType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="receivable">Receita</SelectItem>
                        <SelectItem value="payable">Despesa</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="parentCategoryId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categoria Pai</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(value || null)}
                      value={field.value || "null"}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Categoria principal" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="null">Nenhuma (Categoria principal)</SelectItem>
                        {parentOptions.map((cat) => (
                          <SelectItem key={cat.id} value={cat.id}>
                            {cat.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>Ativo</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      Categorias inativas não serão exibidas em seleções
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <DialogFooter className="pt-4">
              <Button variant="outline" type="button" onClick={onClose}>
                Cancelar
              </Button>
              <Button type="submit">
                {category ? 'Atualizar' : 'Criar'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
