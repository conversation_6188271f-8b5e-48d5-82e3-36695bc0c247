import { ChevronRight, Home } from "lucide-react";
import { Button } from "@/components/ui/button";
import { CategoryBreadcrumb } from "@/types/category";

interface CategoryBreadcrumbsProps {
  breadcrumbs: CategoryBreadcrumb[];
  onNavigate: (categoryId: string | null, level: number) => void;
  className?: string;
}

export default function CategoryBreadcrumbs({
  breadcrumbs,
  onNavigate,
  className = ""
}: CategoryBreadcrumbsProps) {
  const handleNavigateToRoot = () => {
    onNavigate(null, 0);
  };

  const handleNavigateToCategory = (breadcrumb: CategoryBreadcrumb) => {
    onNavigate(breadcrumb.id, breadcrumb.level);
  };

  return (
    <div className={`flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400 ${className}`}>
      {/* Botão para voltar à raiz */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleNavigateToRoot}
        className="h-8 px-2 hover:bg-gray-100 dark:hover:bg-gray-800"
      >
        <Home className="h-4 w-4" />
        <span className="ml-1">Categorias</span>
      </Button>

      {/* Breadcrumbs das categorias */}
      {breadcrumbs.map((breadcrumb, index) => (
        <div key={breadcrumb.id} className="flex items-center">
          <ChevronRight className="h-4 w-4 text-gray-400" />
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleNavigateToCategory(breadcrumb)}
            className="h-8 px-2 hover:bg-gray-100 dark:hover:bg-gray-800"
            disabled={index === breadcrumbs.length - 1} // Desabilita o último item (atual)
          >
            {breadcrumb.name}
          </Button>
        </div>
      ))}
    </div>
  );
}
