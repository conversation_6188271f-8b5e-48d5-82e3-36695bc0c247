import { useMemo } from "react";
import { TrendingUp, TrendingDown, Layers, FolderTree } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Category } from "@/types/category";
import { 
  countTotalCategories, 
  countCategoriesByLevel, 
  getMaxDepth 
} from "@/utils/categoryHierarchy";

interface CategoryStatsProps {
  categories: Category[];
  className?: string;
}

export default function CategoryStats({ categories, className = "" }: CategoryStatsProps) {
  const stats = useMemo(() => {
    const total = countTotalCategories(categories);
    const byLevel = countCategoriesByLevel(categories);
    const maxDepth = getMaxDepth(categories);
    
    // Contar por tipo
    const payableCount = categories.filter(cat => cat.transactionType === 'payable').length;
    const receivableCount = categories.filter(cat => cat.transactionType === 'receivable').length;
    
    // Contar categorias raiz vs subcategorias
    const rootCategories = categories.length;
    const subcategories = total - rootCategories;
    
    return {
      total,
      byLevel,
      maxDepth,
      payableCount,
      receivableCount,
      rootCategories,
      subcategories
    };
  }, [categories]);

  if (stats.total === 0) {
    return null;
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {/* Total de Categorias */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total de Categorias</CardTitle>
          <FolderTree className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
          <p className="text-xs text-muted-foreground">
            {stats.rootCategories} principais, {stats.subcategories} subcategorias
          </p>
        </CardContent>
      </Card>

      {/* Profundidade Máxima */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Profundidade Máxima</CardTitle>
          <Layers className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.maxDepth + 1}</div>
          <p className="text-xs text-muted-foreground">
            de 5 níveis máximos
          </p>
        </CardContent>
      </Card>

      {/* Despesas */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Categorias de Despesa</CardTitle>
          <TrendingDown className="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{stats.payableCount}</div>
          <p className="text-xs text-muted-foreground">
            {stats.total > 0 ? Math.round((stats.payableCount / stats.total) * 100) : 0}% do total
          </p>
        </CardContent>
      </Card>

      {/* Receitas */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Categorias de Receita</CardTitle>
          <TrendingUp className="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{stats.receivableCount}</div>
          <p className="text-xs text-muted-foreground">
            {stats.total > 0 ? Math.round((stats.receivableCount / stats.total) * 100) : 0}% do total
          </p>
        </CardContent>
      </Card>

      {/* Distribuição por Nível */}
      {stats.maxDepth > 0 && (
        <Card className="md:col-span-2 lg:col-span-4">
          <CardHeader>
            <CardTitle className="text-sm font-medium">Distribuição por Nível</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {Object.entries(stats.byLevel).map(([level, count]) => (
                <Badge key={level} variant="outline" className="flex items-center space-x-1">
                  <span>Nível {parseInt(level) + 1}:</span>
                  <span className="font-semibold">{count}</span>
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
