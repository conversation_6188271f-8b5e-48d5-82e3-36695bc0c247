import { useState, useMemo } from "react";
import {
  ChevronR<PERSON>,
  Pencil,
  Trash2,
  Plus,
  Folder<PERSON><PERSON>,
  FileText,
  ArrowLeft,
  <PERSON>ers,
  Hash
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Category, CategoryBreadcrumb, CategoryDrilldownState, CategoryStatistics } from "@/types/category";
import { DeleteCategoryDialog } from "./DeleteCategoryDialog";
import CategoryBreadcrumbs from "./CategoryBreadcrumbs";
import { useCategoryStatistics } from "@/hooks/api/useCategories";
import { findCategoryById } from "@/utils/categoryHierarchy";

interface CategoryDrilldownViewProps {
  categories: Category[];
  onEdit: (category: Category) => void;
  onDelete: (categoryId: string) => void;
  onAddSubcategory: (parentCategory: Category | null) => void;
  searchTerm: string;
  filterType: string | null;
  isLoading?: boolean;
}

interface CategoryCardProps {
  category: Category;
  onEdit: (category: Category) => void;
  onDelete: (categoryId: string) => void;
  onAddSubcategory: (parentCategory: Category) => void;
  onNavigateToChildren: (category: Category) => void;
  searchTerm: string;
  level: number;
  statistics?: CategoryStatistics[];
}

function CategoryCard({
  category,
  onEdit,
  onDelete,
  onAddSubcategory,
  onNavigateToChildren,
  searchTerm,
  level,
  statistics
}: CategoryCardProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const hasChildren = category.children && category.children.length > 0;
  const canAddSubcategory = level < 4; // Máximo 5 níveis (0-4)

  // Buscar estatísticas para esta categoria
  const categoryStats = statistics?.find(stat => stat.categoryId === category.id);
  
  // Destacar texto da busca
  const highlightText = (text: string, search: string) => {
    if (!search) return text;
    
    const regex = new RegExp(`(${search})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <span key={index} className="bg-yellow-200 dark:bg-yellow-800 font-semibold">
          {part}
        </span>
      ) : part
    );
  };

  const handleEdit = () => {
    onEdit(category);
  };

  const handleDelete = () => {
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    onDelete(category.id);
    setIsDeleteDialogOpen(false);
  };

  const handleAddSubcategory = () => {
    onAddSubcategory(category);
  };

  const handleNavigateToChildren = () => {
    if (hasChildren) {
      onNavigateToChildren(category);
    }
  };

  return (
    <>
      <Card className="hover:shadow-md transition-shadow duration-200">
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3 flex-1 min-w-0">
              {/* Ícone da categoria */}
              <div className="mt-1">
                {hasChildren ? (
                  <FolderOpen className="h-5 w-5 text-blue-500" />
                ) : (
                  <FileText className="h-5 w-5 text-gray-500" />
                )}
              </div>

              {/* Informações da categoria */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <h3 className="font-medium text-gray-900 dark:text-white truncate">
                    {highlightText(category.name, searchTerm)}
                  </h3>
                  <Badge
                    className={`text-xs ${
                      category.transactionType === "payable"
                        ? "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"
                        : "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
                    }`}
                  >
                    {category.transactionType === "payable" ? "Despesa" : "Receita"}
                  </Badge>
                  {/* Badge de nível */}
                  <Badge
                    variant="outline"
                    className="text-xs bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800"
                  >
                    Nível {level + 1}
                  </Badge>
                </div>

                {/* Informações adicionais */}
                <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400 mb-2">
                  {/* Contador de transações */}
                  {categoryStats && (
                    <div className="flex items-center gap-1">
                      <Hash className="h-3 w-3" />
                      <span>{categoryStats.transactionCount} transaç{categoryStats.transactionCount !== 1 ? 'ões' : 'ão'}</span>
                    </div>
                  )}

                  {/* Contador de subcategorias */}
                  {hasChildren && (
                    <div className="flex items-center gap-1">
                      <Layers className="h-3 w-3" />
                      <span>{category.children!.length} subcategoria{category.children!.length !== 1 ? 's' : ''}</span>
                    </div>
                  )}
                </div>

                {hasChildren && (
                  <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleNavigateToChildren}
                      className="h-6 px-2 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                    >
                      Ver subcategorias
                      <ChevronRight className="h-3 w-3 ml-1" />
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Ações */}
            <div className="flex items-center space-x-1 ml-2">
              {canAddSubcategory && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleAddSubcategory}
                  className="h-8 w-8 p-0"
                  title="Adicionar subcategoria"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              )}
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEdit}
                className="h-8 w-8 p-0"
                title="Editar categoria"
              >
                <Pencil className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleDelete();
                }}
                className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                title="Excluir categoria"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <DeleteCategoryDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        categoryName={category.name}
        hasChildren={hasChildren}
      />
    </>
  );
}

export default function CategoryDrilldownView({
  categories,
  onEdit,
  onDelete,
  onAddSubcategory,
  searchTerm,
  filterType,
  isLoading = false
}: CategoryDrilldownViewProps) {
  const [drilldownState, setDrilldownState] = useState<CategoryDrilldownState>({
    currentLevel: 0,
    breadcrumbs: [],
    currentCategories: categories,
    parentId: null
  });

  // Buscar estatísticas das categorias
  const { data: statistics, isLoading: isLoadingStats } = useCategoryStatistics(filterType || undefined);

  // Usar a função utilitária importada

  // Navegar para uma categoria específica
  const handleNavigateToCategory = (categoryId: string | null, level: number) => {
    if (categoryId === null) {
      // Voltar para a raiz
      setDrilldownState({
        currentLevel: 0,
        breadcrumbs: [],
        currentCategories: categories,
        parentId: null
      });
    } else {
      // Navegar para uma categoria específica
      const category = findCategoryById(categoryId, categories);
      if (category && category.children) {
        const newBreadcrumbs = drilldownState.breadcrumbs.slice(0, level);
        newBreadcrumbs.push({
          id: category.id,
          name: category.name,
          level: level
        });

        setDrilldownState({
          currentLevel: level + 1,
          breadcrumbs: newBreadcrumbs,
          currentCategories: category.children,
          parentId: category.id
        });
      }
    }
  };

  // Navegar para os filhos de uma categoria
  const handleNavigateToChildren = (category: Category) => {
    if (category.children && category.children.length > 0) {
      const newBreadcrumbs = [...drilldownState.breadcrumbs, {
        id: category.id,
        name: category.name,
        level: drilldownState.currentLevel
      }];

      setDrilldownState({
        currentLevel: drilldownState.currentLevel + 1,
        breadcrumbs: newBreadcrumbs,
        currentCategories: category.children,
        parentId: category.id
      });
    }
  };

  // Voltar um nível
  const handleGoBack = () => {
    if (drilldownState.breadcrumbs.length > 0) {
      const newBreadcrumbs = drilldownState.breadcrumbs.slice(0, -1);
      const parentBreadcrumb = newBreadcrumbs[newBreadcrumbs.length - 1];
      
      if (parentBreadcrumb) {
        const parentCategory = findCategoryById(parentBreadcrumb.id, categories);
        setDrilldownState({
          currentLevel: parentBreadcrumb.level + 1,
          breadcrumbs: newBreadcrumbs,
          currentCategories: parentCategory?.children || [],
          parentId: parentCategory?.id || null
        });
      } else {
        setDrilldownState({
          currentLevel: 0,
          breadcrumbs: [],
          currentCategories: categories,
          parentId: null
        });
      }
    }
  };

  // Filtrar categorias baseado na busca e tipo
  const filteredCategories = useMemo(() => {
    let filtered = drilldownState.currentCategories;

    // Filtrar por tipo
    if (filterType) {
      filtered = filtered.filter(cat => cat.transactionType === filterType);
    }

    // Filtrar por termo de busca
    if (searchTerm) {
      filtered = filtered.filter(cat => 
        cat.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [drilldownState.currentCategories, searchTerm, filterType]);

  // Atualizar categorias quando a prop categories mudar
  useMemo(() => {
    if (drilldownState.currentLevel === 0) {
      setDrilldownState(prev => ({
        ...prev,
        currentCategories: categories
      }));
    }
  }, [categories, drilldownState.currentLevel]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-500 dark:text-gray-400">Carregando categorias...</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header com breadcrumbs e botão voltar */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {drilldownState.currentLevel > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleGoBack}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Voltar</span>
            </Button>
          )}
          
          <CategoryBreadcrumbs
            breadcrumbs={drilldownState.breadcrumbs}
            onNavigate={handleNavigateToCategory}
          />
        </div>

        {/* Botão para adicionar categoria no nível atual */}
        {drilldownState.currentLevel < 5 && (
          <Button
            onClick={() => {
              const parentCategory = drilldownState.parentId ? 
                findCategoryById(drilldownState.parentId, categories) : null;
              onAddSubcategory(parentCategory);
            }}
            className="bg-[#007FFF] hover:bg-[#0066CC] text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            Adicionar Categoria
          </Button>
        )}
      </div>

      {/* Grid de categorias */}
      {filteredCategories.length === 0 ? (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          {searchTerm || filterType ? 
            "Nenhuma categoria encontrada com os filtros aplicados." :
            "Nenhuma categoria neste nível. Adicione uma nova categoria!"
          }
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredCategories.map((category) => (
            <CategoryCard
              key={category.id}
              category={category}
              onEdit={onEdit}
              onDelete={onDelete}
              onAddSubcategory={onAddSubcategory}
              onNavigateToChildren={handleNavigateToChildren}
              searchTerm={searchTerm}
              level={drilldownState.currentLevel}
              statistics={statistics}
            />
          ))}
        </div>
      )}
    </div>
  );
}
