import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Category } from "@/types/category";

// Interface para compatibilidade com CategoriesTable
interface DeleteCategoryDialogPropsV1 {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  category: Category | null;
  onConfirm: () => void;
}

// Interface para compatibilidade com CategoryTreeView e CategoryDrilldownView
interface DeleteCategoryDialogPropsV2 {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  categoryName: string;
  hasChildren: boolean;
}

type DeleteCategoryDialogProps = DeleteCategoryDialogPropsV1 | DeleteCategoryDialogPropsV2;

// Type guard para verificar qual interface está sendo usada
function isV1Props(props: DeleteCategoryDialogProps): props is DeleteCategoryDialogPropsV1 {
  return 'open' in props && 'onOpenChange' in props && 'category' in props;
}

export function DeleteCategoryDialog(props: DeleteCategoryDialogProps) {
  if (isV1Props(props)) {
    // Versão para CategoriesTable
    const { open, onOpenChange, category, onConfirm } = props;

    return (
      <AlertDialog open={open} onOpenChange={onOpenChange}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir a categoria "{category?.name}"?
              {category?.children && category.children.length > 0 && (
                <span className="block mt-2 font-semibold text-orange-600 dark:text-orange-400">
                  ⚠️ Atenção: Esta categoria possui subcategorias que também serão excluídas.
                </span>
              )}
              <span className="block mt-2 text-sm text-gray-600 dark:text-gray-400">
                Esta ação não pode ser desfeita.
              </span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={onConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Excluir Categoria
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  } else {
    // Versão para CategoryTreeView e CategoryDrilldownView
    const { isOpen, onClose, onConfirm, categoryName, hasChildren } = props;

    return (
      <AlertDialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir a categoria "{categoryName}"?
              {hasChildren && (
                <span className="block mt-2 font-semibold text-orange-600 dark:text-orange-400">
                  ⚠️ Atenção: Esta categoria possui subcategorias que também serão excluídas.
                </span>
              )}
              <span className="block mt-2 text-sm text-gray-600 dark:text-gray-400">
                Esta ação não pode ser desfeita.
              </span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={onClose}>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={onConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Excluir Categoria
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }
}
