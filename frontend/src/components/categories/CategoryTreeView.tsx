import { useState, useMemo, useEffect } from "react";
import {
  ChevronDown,
  ChevronRight,
  Pencil,
  Trash2,
  Plus,
  Folder<PERSON><PERSON>,
  Folder,
  FileText,
  Layers,
  Hash
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Category, CategoryStatistics } from "@/types/category";
import { DeleteCategoryDialog } from "./DeleteCategoryDialog";
import { useCategoryStatistics } from "@/hooks/api/useCategories";
import {
  filterCategoriesBySearch,
  filterCategoriesByType,
  findExpandedIdsForSearch
} from "@/utils/categoryHierarchy";

interface CategoryTreeViewProps {
  categories: Category[];
  onEdit: (category: Category) => void;
  onDelete: (categoryId: string) => void;
  onAddSubcategory: (parentCategory: Category) => void;
  searchTerm: string;
  filterType: string | null;
  isLoading?: boolean;
}

interface CategoryTreeItemProps {
  category: Category;
  level: number;
  onEdit: (category: Category) => void;
  onDelete: (categoryId: string) => void;
  onAddSubcategory: (parentCategory: Category) => void;
  expandedItems: Set<string>;
  onToggleExpand: (categoryId: string) => void;
  searchTerm: string;
  statistics?: CategoryStatistics[];
}

function CategoryTreeItem({
  category,
  level,
  onEdit,
  onDelete,
  onAddSubcategory,
  expandedItems,
  onToggleExpand,
  searchTerm,
  statistics
}: CategoryTreeItemProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const hasChildren = category.children && category.children.length > 0;
  const isExpanded = expandedItems.has(category.id);
  const indentationLevel = level * 24; // 24px por nível

  // Buscar estatísticas para esta categoria
  const categoryStats = statistics?.find(stat => stat.categoryId === category.id);
  
  // Destacar texto da busca
  const highlightText = (text: string, search: string) => {
    if (!search) return text;
    
    const regex = new RegExp(`(${search})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <span key={index} className="bg-yellow-200 dark:bg-yellow-800 font-semibold">
          {part}
        </span>
      ) : part
    );
  };

  const handleToggleExpand = () => {
    if (hasChildren) {
      onToggleExpand(category.id);
    }
  };

  const handleEdit = () => {
    onEdit(category);
  };

  const handleDelete = () => {
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    onDelete(category.id);
    setIsDeleteDialogOpen(false);
  };

  const handleAddSubcategory = () => {
    onAddSubcategory(category);
  };

  return (
    <>
      <div 
        className="flex items-center py-2 px-3 hover:bg-gray-50 dark:hover:bg-gray-800 border-b border-gray-100 dark:border-gray-700"
        style={{ paddingLeft: `${12 + indentationLevel}px` }}
      >
        {/* Ícone de expansão/colapso */}
        <div className="w-6 h-6 flex items-center justify-center mr-2">
          {hasChildren ? (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={handleToggleExpand}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          ) : (
            <div className="w-4 h-4" />
          )}
        </div>

        {/* Ícone da categoria */}
        <div className="mr-3">
          {hasChildren ? (
            isExpanded ? (
              <FolderOpen className="h-4 w-4 text-blue-500" />
            ) : (
              <Folder className="h-4 w-4 text-blue-500" />
            )
          ) : (
            <FileText className="h-4 w-4 text-gray-500" />
          )}
        </div>

        {/* Nome da categoria */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <div className="font-medium text-gray-900 dark:text-white truncate">
              {highlightText(category.name, searchTerm)}
            </div>
            {/* Badge de nível */}
            <Badge
              variant="outline"
              className="text-xs bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800"
            >
              Nível {level + 1}
            </Badge>
          </div>

          {/* Informações adicionais */}
          <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
            {/* Contador de transações */}
            {categoryStats && (
              <div className="flex items-center gap-1">
                <Hash className="h-3 w-3" />
                <span>{categoryStats.transactionCount} transaç{categoryStats.transactionCount !== 1 ? 'ões' : 'ão'}</span>
              </div>
            )}

            {/* Contador de subcategorias */}
            {hasChildren && (
              <div className="flex items-center gap-1">
                <Layers className="h-3 w-3" />
                <span>{category.children!.length} subcategoria{category.children!.length !== 1 ? 's' : ''}</span>
              </div>
            )}
          </div>
        </div>

        {/* Badge do tipo */}
        <div className="mr-3">
          <Badge
            className={`text-xs ${
              category.transactionType === "payable"
                ? "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"
                : "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
            }`}
          >
            {category.transactionType === "payable" ? "Despesa" : "Receita"}
          </Badge>
        </div>

        {/* Ações */}
        <div className="flex items-center space-x-1">
          {level < 4 && ( // Máximo 5 níveis (0-4)
            <Button
              variant="ghost"
              size="sm"
              onClick={handleAddSubcategory}
              className="h-8 w-8 p-0"
              title="Adicionar subcategoria"
            >
              <Plus className="h-4 w-4" />
            </Button>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEdit}
            className="h-8 w-8 p-0"
            title="Editar categoria"
          >
            <Pencil className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleDelete();
            }}
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
            title="Excluir categoria"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Subcategorias */}
      {hasChildren && isExpanded && (
        <div>
          {category.children!.map((child) => (
            <CategoryTreeItem
              key={child.id}
              category={child}
              level={level + 1}
              onEdit={onEdit}
              onDelete={onDelete}
              onAddSubcategory={onAddSubcategory}
              expandedItems={expandedItems}
              onToggleExpand={onToggleExpand}
              searchTerm={searchTerm}
              statistics={statistics}
            />
          ))}
        </div>
      )}

      <DeleteCategoryDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        categoryName={category.name}
        hasChildren={hasChildren}
      />
    </>
  );
}

export default function CategoryTreeView({
  categories,
  onEdit,
  onDelete,
  onAddSubcategory,
  searchTerm,
  filterType,
  isLoading = false
}: CategoryTreeViewProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  // Buscar estatísticas das categorias
  const { data: statistics, isLoading: isLoadingStats } = useCategoryStatistics(filterType || undefined);

  // Filtrar categorias baseado na busca e tipo
  const filteredCategories = useMemo(() => {
    let filtered = categories;

    // Filtrar por tipo
    if (filterType) {
      filtered = filterCategoriesByType(filtered, filterType);
    }

    // Filtrar por termo de busca
    if (searchTerm) {
      filtered = filterCategoriesBySearch(filtered, searchTerm);
    }

    return filtered;
  }, [categories, searchTerm, filterType]);

  // Expandir automaticamente nós que contêm resultados de busca
  useEffect(() => {
    if (searchTerm) {
      const expandedIds = findExpandedIdsForSearch(filteredCategories, searchTerm);
      setExpandedItems(expandedIds);
    }
  }, [searchTerm, filteredCategories]);

  const handleToggleExpand = (categoryId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedItems(newExpanded);
  };

  const handleExpandAll = () => {
    const getAllIds = (cats: Category[]): string[] => {
      const ids: string[] = [];
      cats.forEach(cat => {
        ids.push(cat.id);
        if (cat.children) {
          ids.push(...getAllIds(cat.children));
        }
      });
      return ids;
    };
    
    setExpandedItems(new Set(getAllIds(filteredCategories)));
  };

  const handleCollapseAll = () => {
    setExpandedItems(new Set());
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-500 dark:text-gray-400">Carregando categorias...</div>
      </div>
    );
  }

  if (filteredCategories.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        {searchTerm || filterType ? 
          "Nenhuma categoria encontrada com os filtros aplicados." :
          "Nenhuma categoria encontrada. Crie sua primeira categoria!"
        }
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
      {/* Controles de expansão */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Visualização em Árvore
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExpandAll}
            className="text-xs"
          >
            Expandir Tudo
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleCollapseAll}
            className="text-xs"
          >
            Recolher Tudo
          </Button>
        </div>
      </div>

      {/* Árvore de categorias */}
      <div>
        {filteredCategories.map((category) => (
          <CategoryTreeItem
            key={category.id}
            category={category}
            level={0}
            onEdit={onEdit}
            onDelete={onDelete}
            onAddSubcategory={onAddSubcategory}
            expandedItems={expandedItems}
            onToggleExpand={handleToggleExpand}
            searchTerm={searchTerm}
            statistics={statistics}
          />
        ))}
      </div>
    </div>
  );
}
