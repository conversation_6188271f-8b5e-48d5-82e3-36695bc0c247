import { useState } from "react";
import { <PERSON><PERSON><PERSON>cle, TreePine, Grid3X3, Plus, Search, Filter, Layers } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

export default function CategoryHelpDialog() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center space-x-2">
          <HelpCircle className="h-4 w-4" />
          <span>Ajuda</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Layers className="h-5 w-5" />
            <span>Sistema Hierárquico de Categorias</span>
          </DialogTitle>
          <DialogDescription>
            Aprenda a usar o sistema de categorias com até 5 níveis de profundidade
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Visão Geral */}
          <section>
            <h3 className="text-lg font-semibold mb-3">Visão Geral</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              O sistema de categorias permite organizar suas transações em uma estrutura hierárquica 
              com até 5 níveis de profundidade. Isso facilita a organização e análise de suas finanças.
            </p>
            <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
              <p className="text-sm font-medium mb-2">Exemplo de Hierarquia:</p>
              <div className="text-sm space-y-1 font-mono">
                <div>📁 Alimentação</div>
                <div className="ml-4">📁 Restaurantes</div>
                <div className="ml-8">📄 Fast Food</div>
                <div className="ml-8">📄 Restaurantes Finos</div>
                <div className="ml-4">📄 Supermercado</div>
              </div>
            </div>
          </section>

          <Separator />

          {/* Modos de Visualização */}
          <section>
            <h3 className="text-lg font-semibold mb-3">Modos de Visualização</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <TreePine className="h-4 w-4 text-green-600" />
                  <span className="font-medium">Visualização em Árvore</span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Mostra toda a hierarquia em uma estrutura expansível/colapsável.
                </p>
                <ul className="text-sm space-y-1">
                  <li>• Veja toda a estrutura de uma vez</li>
                  <li>• Expanda/recolha categorias</li>
                  <li>• Ideal para visão geral</li>
                </ul>
              </div>
              
              <div className="border rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Grid3X3 className="h-4 w-4 text-blue-600" />
                  <span className="font-medium">Navegação por Camadas</span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Navega nível por nível com breadcrumbs para mostrar o caminho.
                </p>
                <ul className="text-sm space-y-1">
                  <li>• Navegação intuitiva</li>
                  <li>• Foco em um nível por vez</li>
                  <li>• Ideal para dispositivos móveis</li>
                </ul>
              </div>
            </div>
          </section>

          <Separator />

          {/* Funcionalidades */}
          <section>
            <h3 className="text-lg font-semibold mb-3">Funcionalidades Principais</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Plus className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium">Adicionar Categorias e Subcategorias</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Crie categorias principais ou subcategorias dentro de categorias existentes. 
                    Máximo de 5 níveis de profundidade.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <Search className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="font-medium">Busca Inteligente</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    A busca funciona em toda a hierarquia e expande automaticamente 
                    os nós que contêm resultados.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <Filter className="h-5 w-5 text-purple-600 mt-0.5" />
                <div>
                  <p className="font-medium">Filtros por Tipo</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Filtre por tipo de transação (Receita/Despesa) mantendo a estrutura hierárquica.
                  </p>
                </div>
              </div>
            </div>
          </section>

          <Separator />

          {/* Tipos de Categoria */}
          <section>
            <h3 className="text-lg font-semibold mb-3">Tipos de Categoria</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-3 p-3 border rounded-lg">
                <Badge className="text-xs bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800">Despesa</Badge>
                <div>
                  <p className="font-medium">Contas a Pagar</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Para categorizar gastos e despesas
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 p-3 border rounded-lg">
                <Badge className="text-xs bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">Receita</Badge>
                <div>
                  <p className="font-medium">Contas a Receber</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Para categorizar receitas e entradas
                  </p>
                </div>
              </div>
            </div>
          </section>

          <Separator />

          {/* Regras e Limitações */}
          <section>
            <h3 className="text-lg font-semibold mb-3">Regras e Limitações</h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Máximo de 5 níveis de profundidade na hierarquia</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Subcategorias devem ter o mesmo tipo da categoria pai</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Não é possível criar referências circulares</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Para excluir uma categoria com subcategorias, exclua primeiro as subcategorias</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Nomes de categoria devem ter pelo menos 2 caracteres</span>
              </div>
            </div>
          </section>

          <Separator />

          {/* Dicas */}
          <section>
            <h3 className="text-lg font-semibold mb-3">Dicas de Uso</h3>
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg space-y-2 text-sm">
              <p><strong>💡 Planejamento:</strong> Pense na estrutura antes de criar muitas categorias</p>
              <p><strong>💡 Consistência:</strong> Use nomes descritivos e consistentes</p>
              <p><strong>💡 Simplicidade:</strong> Não crie níveis desnecessários - mantenha simples</p>
              <p><strong>💡 Organização:</strong> Agrupe categorias relacionadas sob uma categoria pai</p>
              <p><strong>💡 Flexibilidade:</strong> Você pode reorganizar a hierarquia a qualquer momento</p>
            </div>
          </section>
        </div>
      </DialogContent>
    </Dialog>
  );
}
