import { useState, useCallback, useMemo } from "react";
import {
  Penci<PERSON>,
  Trash2,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Category } from "@/types/category";
import { DeleteCategoryDialog } from "./DeleteCategoryDialog";
import { useCategoryManagement } from "@/hooks/useCategoryManagement";

// Dígito usado para identar categorias na tabela
const INDENTATION = "　";

interface CategoriesTableProps {
  onEdit: (category: Category) => void;
  searchTerm: string;
  filterType: string | null;
}

const CategoriesTable = ({ onEdit, searchTerm, filterType }: CategoriesTableProps) => {
  const { categories, isLoading, deleteCategory } = useCategoryManagement();
  const [deletingCategory, setDeletingCategory] = useState<Category | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Filtrar categorias com base na pesquisa e tipo
  const filteredCategories = useMemo(() => {
    if (!categories || categories.length === 0) return [];

    // Se não há filtros, retorna todas as categorias
    if (!searchTerm && !filterType) {
      return categories;
    }

    // Aplica filtros
    const filtered = categories.filter(category => {
      const matchesSearch = !searchTerm ||
        category.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = !filterType || category.transactionType === filterType;
      return matchesSearch && matchesType;
    });

    return filtered.sort((a, b) => a.name.localeCompare(b.name));
  }, [categories, searchTerm, filterType]);

  // Função para encontrar o nome da categoria pai por ID
  const getParentCategoryName = (parentId: string | null | undefined): string => {
    if (!parentId) return "—";

    const parentCategory = categories.find(c => c.id === parentId);
    return parentCategory ? parentCategory.name : "—";
  };

  // Renderizar uma linha de categoria
  const renderCategoryRow = useCallback((category: Category, level = 0) => {
    const indent = INDENTATION.repeat(level);

    // Para destacar categorias que correspondem à pesquisa
    const matchesSearch = searchTerm &&
      category.name.toLowerCase().includes(searchTerm.toLowerCase());

    return (
      <TableRow
        key={category.id}
        className={`border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50 ${matchesSearch ? "bg-gray-50 dark:bg-gray-800/50" : ""}`}
      >
        <TableCell className="py-4">
          <div className="flex items-center">
            <span className="w-7" />
            {indent}
            <span className="font-medium text-gray-900 dark:text-white">{category.name}</span>
          </div>
        </TableCell>
        <TableCell>
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              category.transactionType === "payable"
                ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                : "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
            }`}
          >
            {category.transactionType === "payable" ? "Despesa" : "Receita"}
          </span>
        </TableCell>
        <TableCell className="py-4">
          <span className="text-gray-600 dark:text-gray-400">
            {getParentCategoryName(category.parentCategoryId) || "—"}
          </span>
        </TableCell>
        <TableCell className="text-right py-4">
          <div className="flex justify-end space-x-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onEdit(category)}
              className="h-8 w-8 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setDeletingCategory(category);
                setDeleteDialogOpen(true);
              }}
              className="h-8 w-8 text-gray-400 hover:text-red-600 dark:text-gray-500 dark:hover:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </TableCell>
      </TableRow>
    );
  }, [searchTerm, onEdit]);

  const handleConfirmDelete = async () => {
    if (deletingCategory) {
      try {
        await deleteCategory(deletingCategory.id);
        setDeleteDialogOpen(false);
        setDeletingCategory(null);
      } catch (error) {
        console.error("Erro ao excluir categoria:", error);
      }
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="bg-gray-50 dark:bg-gray-900/50 border-b border-gray-200 dark:border-gray-700">
            <TableHead className="text-gray-600 dark:text-gray-400 font-medium">Nome</TableHead>
            <TableHead className="text-gray-600 dark:text-gray-400 font-medium">Tipo</TableHead>
            <TableHead className="text-gray-600 dark:text-gray-400 font-medium">Categoria Pai</TableHead>
            <TableHead className="text-right text-gray-600 dark:text-gray-400 font-medium">Ações</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={4} className="h-24 text-center text-gray-500 dark:text-gray-400">
                Carregando categorias...
              </TableCell>
            </TableRow>
          ) : filteredCategories.length === 0 ? (
            <TableRow>
              <TableCell colSpan={4} className="h-24 text-center text-gray-500 dark:text-gray-400">
                {searchTerm || filterType
                  ? "Nenhuma categoria encontrada para os filtros selecionados."
                  : "Nenhuma categoria encontrada. Crie uma nova categoria para começar."}
              </TableCell>
            </TableRow>
          ) : (
            filteredCategories.map(category => renderCategoryRow(category, category.level || 0))
          )}
        </TableBody>
      </Table>

      <DeleteCategoryDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        category={deletingCategory}
        onConfirm={handleConfirmDelete}
      />
    </div>
  );
};

export default CategoriesTable;
