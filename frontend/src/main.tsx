// Importar o inicializador do monitoringService primeiro
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { QueryClientContext } from './contexts/QueryClientContext';

import './index.css';



const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Elemento root não encontrado');
}

const root = ReactDOM.createRoot(rootElement);



// Aguardar a inicialização do monitoringService
import('./services/initializers/monitoringInitializer')
  .then(({ initializeMonitoringService }) => {

    return initializeMonitoringService();
  })
  .then(() => {

    root.render(
      <React.StrictMode>
        <QueryClientContext>
          <App />
        </QueryClientContext>
      </React.StrictMode>
    );

  })
  .catch(() => {
    // Erro na inicialização - renderizar versão básica da aplicação
    root.render(
      <div className="p-4">
        <h1>Erro na inicialização</h1>
        <p>Por favor, recarregue a página.</p>
      </div>
    );
  });
