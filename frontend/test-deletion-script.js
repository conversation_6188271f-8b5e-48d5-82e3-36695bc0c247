// Script de teste para verificar a funcionalidade de exclusão de categorias
// Execute este script no console do navegador na página de categorias

console.log("🧪 Iniciando teste de exclusão de categorias...");

// Função para aguardar um elemento aparecer
function waitForElement(selector, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const element = document.querySelector(selector);
    if (element) {
      resolve(element);
      return;
    }

    const observer = new MutationObserver(() => {
      const element = document.querySelector(selector);
      if (element) {
        observer.disconnect();
        resolve(element);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Elemento ${selector} não encontrado em ${timeout}ms`));
    }, timeout);
  });
}

// Função para simular clique
function simulateClick(element) {
  const event = new MouseEvent('click', {
    view: window,
    bubbles: true,
    cancelable: true
  });
  element.dispatchEvent(event);
}

// Teste principal
async function testCategoryDeletion() {
  try {
    console.log("1. Procurando botões de exclusão...");
    
    // Procurar por botões de exclusão (ícone de lixeira)
    const deleteButtons = document.querySelectorAll('button[title="Excluir categoria"]');
    
    if (deleteButtons.length === 0) {
      console.error("❌ Nenhum botão de exclusão encontrado!");
      return;
    }
    
    console.log(`✅ Encontrados ${deleteButtons.length} botões de exclusão`);
    
    // Clicar no primeiro botão de exclusão
    console.log("2. Clicando no primeiro botão de exclusão...");
    simulateClick(deleteButtons[0]);
    
    // Aguardar o diálogo aparecer
    console.log("3. Aguardando diálogo de confirmação...");
    const dialog = await waitForElement('[role="alertdialog"]', 3000);
    console.log("✅ Diálogo de confirmação apareceu!");
    
    // Procurar pelo botão de confirmar
    const confirmButton = dialog.querySelector('button:last-child');
    if (!confirmButton) {
      console.error("❌ Botão de confirmação não encontrado!");
      return;
    }
    
    console.log("4. Botão de confirmação encontrado:", confirmButton.textContent);
    
    // Simular clique no botão de cancelar para não excluir realmente
    const cancelButton = dialog.querySelector('button:first-child');
    if (cancelButton) {
      console.log("5. Clicando em cancelar para não excluir...");
      simulateClick(cancelButton);
      console.log("✅ Teste concluído com sucesso! A funcionalidade está funcionando.");
    }
    
  } catch (error) {
    console.error("❌ Erro durante o teste:", error.message);
  }
}

// Executar o teste
testCategoryDeletion();
