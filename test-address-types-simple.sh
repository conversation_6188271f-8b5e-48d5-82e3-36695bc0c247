#!/bin/bash

# Script simples para testar se os tipos de endereço estão disponíveis
echo "=== Teste Simples dos Tipos de Endereço ==="
echo ""

API_BASE_URL="http://localhost:3000/api"

echo "1. Testando se a API está funcionando..."
HEALTH_RESPONSE=$(curl -s "$API_BASE_URL/health")
echo "Health check: $HEALTH_RESPONSE"
echo ""

echo "2. Testando se os tipos de endereço estão disponíveis (sem autenticação)..."
ADDRESS_TYPES_RESPONSE=$(curl -s "$API_BASE_URL/address-types?limit=100")

if echo "$ADDRESS_TYPES_RESPONSE" | grep -q "Unauthorized"; then
  echo "❌ Endpoint requer autenticação. Não é possível testar sem credenciais válidas."
  echo "Resposta: $ADDRESS_TYPES_RESPONSE"
else
  echo "✅ Tipos de endereço disponíveis:"
  echo $ADDRESS_TYPES_RESPONSE | jq '.' 2>/dev/null || echo $ADDRESS_TYPES_RESPONSE
  
  # Verificar se os tipos esperados estão presentes
  if echo "$ADDRESS_TYPES_RESPONSE" | grep -q "Comercial"; then
    echo "✅ Tipo 'Comercial' encontrado"
  else
    echo "❌ Tipo 'Comercial' não encontrado"
  fi
  
  if echo "$ADDRESS_TYPES_RESPONSE" | grep -q "Faturamento"; then
    echo "✅ Tipo 'Faturamento' encontrado"
  else
    echo "❌ Tipo 'Faturamento' não encontrado"
  fi
  
  if echo "$ADDRESS_TYPES_RESPONSE" | grep -q "Entrega"; then
    echo "✅ Tipo 'Entrega' encontrado"
  else
    echo "❌ Tipo 'Entrega' não encontrado"
  fi
fi

echo ""
echo "=== Fim do Teste Simples ==="
