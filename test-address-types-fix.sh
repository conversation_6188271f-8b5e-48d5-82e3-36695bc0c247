#!/bin/bash

# Script para testar se o bug dos tipos de endereço foi corrigido
# Este script testa se os endereços retornados do backend incluem corretamente o addressType

echo "=== Teste de Correção do Bug dos Tipos de Endereço ==="
echo ""

# Configurações
API_BASE_URL="http://localhost:3000/api"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="password123"

echo "1. Fazendo login como administrador..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"email\": \"$ADMIN_EMAIL\",
    \"password\": \"$ADMIN_PASSWORD\"
  }")

# Extrair token de acesso
ACCESS_TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"accessToken":"[^"]*' | cut -d'"' -f4)

if [ -z "$ACCESS_TOKEN" ]; then
  echo "❌ Erro: Não foi possível fazer login. Resposta:"
  echo $LOGIN_RESPONSE
  exit 1
fi

echo "✅ Login realizado com sucesso!"
echo ""

echo "2. Buscando tipos de endereço disponíveis..."
ADDRESS_TYPES_RESPONSE=$(curl -s -X GET "$API_BASE_URL/address-types?limit=100" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json")

echo "Tipos de endereço disponíveis:"
echo $ADDRESS_TYPES_RESPONSE | jq '.' 2>/dev/null || echo $ADDRESS_TYPES_RESPONSE
echo ""

# Extrair IDs dos tipos de endereço
COMERCIAL_ID=$(echo $ADDRESS_TYPES_RESPONSE | grep -o '"id":"[^"]*","name":"Comercial"' | cut -d'"' -f4)
FATURAMENTO_ID=$(echo $ADDRESS_TYPES_RESPONSE | grep -o '"id":"[^"]*","name":"Faturamento"' | cut -d'"' -f4)
ENTREGA_ID=$(echo $ADDRESS_TYPES_RESPONSE | grep -o '"id":"[^"]*","name":"Entrega"' | cut -d'"' -f4)

echo "IDs dos tipos de endereço:"
echo "  - Comercial (main): $COMERCIAL_ID"
echo "  - Faturamento (billing): $FATURAMENTO_ID"
echo "  - Entrega (shipping): $ENTREGA_ID"
echo ""

echo "3. Buscando entidades existentes..."
ENTITIES_RESPONSE=$(curl -s -X GET "$API_BASE_URL/entities?limit=10" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json")

# Extrair ID da primeira entidade
ENTITY_ID=$(echo $ENTITIES_RESPONSE | grep -o '"id":"[^"]*' | head -1 | cut -d'"' -f4)

if [ -z "$ENTITY_ID" ]; then
  echo "❌ Nenhuma entidade encontrada. Criando uma entidade de teste..."
  
  # Criar entidade de teste
  CREATE_ENTITY_RESPONSE=$(curl -s -X POST "$API_BASE_URL/entities" \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
      "name": "Entidade Teste Endereços",
      "entityType": "customer",
      "personType": "company",
      "cnpj": "12.345.678/0001-90",
      "email": "<EMAIL>",
      "mobile": "(11) 99999-9999",
      "status": "active",
      "creditLimit": 0,
      "paymentTermDays": 30,
      "discountPercentage": 0,
      "icmsTaxpayer": false,
      "simpleNational": true,
      "withholdTaxes": false,
      "sendNfe": false
    }')
  
  ENTITY_ID=$(echo $CREATE_ENTITY_RESPONSE | grep -o '"id":"[^"]*' | head -1 | cut -d'"' -f4)
  
  if [ -z "$ENTITY_ID" ]; then
    echo "❌ Erro: Não foi possível criar entidade de teste. Resposta:"
    echo $CREATE_ENTITY_RESPONSE
    exit 1
  fi
  
  echo "✅ Entidade de teste criada com ID: $ENTITY_ID"
else
  echo "✅ Usando entidade existente com ID: $ENTITY_ID"
fi
echo ""

echo "4. Testando criação de endereços com diferentes tipos..."

# Criar endereço Comercial (main)
echo "4.1. Criando endereço Comercial..."
CREATE_ADDRESS_COMERCIAL=$(curl -s -X POST "$API_BASE_URL/entities/$ENTITY_ID/addresses" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "main",
    "street": "Rua Comercial",
    "number": "123",
    "district": "Centro",
    "city": "São Paulo",
    "state": "SP",
    "zipCode": "01310-100",
    "isDefault": true
  }')

COMERCIAL_ADDRESS_ID=$(echo $CREATE_ADDRESS_COMERCIAL | grep -o '"id":"[^"]*' | head -1 | cut -d'"' -f4)
echo "Endereço Comercial criado: $COMERCIAL_ADDRESS_ID"

# Criar endereço Faturamento (billing)
echo "4.2. Criando endereço Faturamento..."
CREATE_ADDRESS_FATURAMENTO=$(curl -s -X POST "$API_BASE_URL/entities/$ENTITY_ID/addresses" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "billing",
    "street": "Rua Faturamento",
    "number": "456",
    "district": "Vila Madalena",
    "city": "São Paulo",
    "state": "SP",
    "zipCode": "05433-000",
    "isDefault": false
  }')

FATURAMENTO_ADDRESS_ID=$(echo $CREATE_ADDRESS_FATURAMENTO | grep -o '"id":"[^"]*' | head -1 | cut -d'"' -f4)
echo "Endereço Faturamento criado: $FATURAMENTO_ADDRESS_ID"

# Criar endereço Entrega (shipping)
echo "4.3. Criando endereço Entrega..."
CREATE_ADDRESS_ENTREGA=$(curl -s -X POST "$API_BASE_URL/entities/$ENTITY_ID/addresses" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "shipping",
    "street": "Rua Entrega",
    "number": "789",
    "district": "Pinheiros",
    "city": "São Paulo",
    "state": "SP",
    "zipCode": "05422-000",
    "isDefault": false
  }')

ENTREGA_ADDRESS_ID=$(echo $CREATE_ADDRESS_ENTREGA | grep -o '"id":"[^"]*' | head -1 | cut -d'"' -f4)
echo "Endereço Entrega criado: $ENTREGA_ADDRESS_ID"
echo ""

echo "5. Testando se os endereços são retornados com os tipos corretos..."

# Buscar todos os endereços da entidade
ADDRESSES_RESPONSE=$(curl -s -X GET "$API_BASE_URL/entities/$ENTITY_ID/addresses" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json")

echo "Resposta da API para endereços da entidade:"
echo $ADDRESSES_RESPONSE | jq '.' 2>/dev/null || echo $ADDRESSES_RESPONSE
echo ""

echo "6. Verificando se os tipos de endereço estão corretos..."

# Verificar se cada endereço tem o addressType correto
COMERCIAL_TYPE=$(echo $ADDRESSES_RESPONSE | jq -r ".[] | select(.id==\"$COMERCIAL_ADDRESS_ID\") | .addressType.name" 2>/dev/null)
FATURAMENTO_TYPE=$(echo $ADDRESSES_RESPONSE | jq -r ".[] | select(.id==\"$FATURAMENTO_ADDRESS_ID\") | .addressType.name" 2>/dev/null)
ENTREGA_TYPE=$(echo $ADDRESSES_RESPONSE | jq -r ".[] | select(.id==\"$ENTREGA_ADDRESS_ID\") | .addressType.name" 2>/dev/null)

echo "Tipos retornados:"
echo "  - Endereço Comercial: $COMERCIAL_TYPE (esperado: Comercial)"
echo "  - Endereço Faturamento: $FATURAMENTO_TYPE (esperado: Faturamento)"
echo "  - Endereço Entrega: $ENTREGA_TYPE (esperado: Entrega)"
echo ""

# Verificar se os tipos estão corretos
ERRORS=0

if [ "$COMERCIAL_TYPE" != "Comercial" ]; then
  echo "❌ ERRO: Endereço Comercial retornou tipo '$COMERCIAL_TYPE' em vez de 'Comercial'"
  ERRORS=$((ERRORS + 1))
else
  echo "✅ Endereço Comercial com tipo correto"
fi

if [ "$FATURAMENTO_TYPE" != "Faturamento" ]; then
  echo "❌ ERRO: Endereço Faturamento retornou tipo '$FATURAMENTO_TYPE' em vez de 'Faturamento'"
  ERRORS=$((ERRORS + 1))
else
  echo "✅ Endereço Faturamento com tipo correto"
fi

if [ "$ENTREGA_TYPE" != "Entrega" ]; then
  echo "❌ ERRO: Endereço Entrega retornou tipo '$ENTREGA_TYPE' em vez de 'Entrega'"
  ERRORS=$((ERRORS + 1))
else
  echo "✅ Endereço Entrega com tipo correto"
fi

echo ""

if [ $ERRORS -eq 0 ]; then
  echo "🎉 SUCESSO: Todos os tipos de endereço estão sendo retornados corretamente!"
  echo "✅ O bug foi corrigido com sucesso!"
else
  echo "❌ FALHA: $ERRORS erro(s) encontrado(s). O bug ainda não foi totalmente corrigido."
fi

echo ""
echo "=== Fim do Teste ==="
