#!/bin/bash

# Script para testar diretamente a API sem autenticação
echo "=== Teste Direto da API ===\n"

API_BASE_URL="http://localhost:3000/api"

echo "1. Testando endpoint de tipos de endereço (sem auth)..."
curl -s "$API_BASE_URL/address-types?limit=100" | jq '.' 2>/dev/null || echo "Endpoint requer autenticação"

echo ""
echo "2. Testando endpoint de health..."
curl -s "$API_BASE_URL/health" | jq '.' 2>/dev/null || echo "Erro no health check"

echo ""
echo "=== Fim do Teste ==="
