#!/bin/bash

# Function to display help message
show_help() {
  echo "FluxoMax Development Environment Script"
  echo ""
  echo "Usage: ./dev.sh [command]"
  echo ""
  echo "Commands:"
  echo "  start       Start the development environment"
  echo "  stop        Stop the development environment"
  echo "  restart     Restart the development environment"
  echo "  logs        Show logs from all services"
  echo "  logs:backend Show logs from backend service"
  echo "  logs:frontend Show logs from frontend service"
  echo "  logs:db     Show logs from database service"
  echo "  rebuild     Rebuild and restart the development environment"
  echo "  exec        Execute a command in a service container (e.g., ./dev.sh exec backend 'npm run test')"
  echo "  db:reset    Reset the database and optionally consolidate migrations"
  echo "  db:consolidate Consolidate all migrations into a single file"
  echo "  db:export-seed Export current database data to a seed script"
  echo "  help        Show this help message"
  echo ""
}

# Function to ask about resetting the database and migrations
ask_reset() {
  echo "\n=== FluxoMax Development Environment ===\n"
  echo "Deseja resetar completamente o banco de dados e as migrações?"
  echo "Isso irá apagar todos os dados e consolidar as migrações em um único arquivo."
  echo "1) Sim, resetar tudo e consolidar migrações"
  echo "2) Sim, resetar apenas o banco de dados (manter migrações)"
  echo "3) Não, continuar normalmente"
  echo "\nEscolha uma opção (3): "
  read -r choice

  case "${choice:-3}" in
    1)
      echo "\nResetando banco de dados e consolidando migrações..."
      # Parar os contêineres primeiro
      docker compose -f docker-compose.dev.yaml down
      # Remover volumes
      docker volume rm fluxo-max_db_data fluxo-max_prisma_migrations 2>/dev/null || true
      # Criar script para consolidar migrações
      create_migration_consolidation_script
      # Executar script de consolidação
      cd backend && bash ./consolidate_migrations.sh && cd ..
      return 1
      ;;
    2)
      echo "\nResetando apenas o banco de dados..."
      # Parar os contêineres primeiro
      docker compose -f docker-compose.dev.yaml down
      # Remover apenas o volume do banco de dados
      docker volume rm fluxo-max_db_data 2>/dev/null || true
      return 1
      ;;
    3|"")
      echo "\nContinuando normalmente..."
      return 0
      ;;
    *)
      echo "\nOpção inválida. Continuando normalmente..."
      return 0
      ;;
  esac
}

# Function to create migration consolidation script
create_migration_consolidation_script() {
  cat > backend/consolidate_migrations.sh << 'EOF'
#!/bin/bash
set -e

echo "=== Consolidando migrações do Prisma ==="

# Verificar se o diretório de migrações existe
if [ ! -d "prisma/migrations" ]; then
  echo "Diretório de migrações não encontrado."
  exit 1
fi

# Criar backup das migrações atuais
echo "Criando backup das migrações atuais..."
mkdir -p prisma/migrations_backup
cp -r prisma/migrations/* prisma/migrations_backup/ 2>/dev/null || true

# Limpar diretório de migrações
echo "Limpando diretório de migrações..."
rm -rf prisma/migrations/*

# Gerar uma nova migração consolidada
echo "Gerando migração consolidada..."

# Criar timestamp para o nome da migração
TIMESTAMP=$(date +%Y%m%d%H%M%S)
MIGRATION_NAME="${TIMESTAMP}_consolidated_schema"

# Criar diretório para a nova migração
mkdir -p "prisma/migrations/${MIGRATION_NAME}"

# Gerar SQL da migração
echo "Gerando SQL da migração..."
npx prisma migrate diff \
  --from-empty \
  --to-schema-datamodel prisma/schema.prisma \
  --script > "prisma/migrations/${MIGRATION_NAME}/migration.sql"

# Adicionar correções de valores padrão ao final da migração consolidada
echo "Adicionando correções de valores padrão..."
cat >> "prisma/migrations/${MIGRATION_NAME}/migration.sql" << 'DEFAULTS_EOF'

-- Add missing DEFAULT values for UUID and timestamp fields
-- This ensures that all required fields have proper default values

-- Fix roles table
ALTER TABLE roles ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE roles ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

-- Fix user_company_roles table
ALTER TABLE user_company_roles ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE user_company_roles ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

-- Fix other critical tables
ALTER TABLE users ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE users ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE companies ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE companies ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE permissions ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE permissions ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE system_permissions ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE system_permissions ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;
DEFAULTS_EOF

echo "Migração consolidada criada com sucesso em prisma/migrations/${MIGRATION_NAME}/migration.sql"
echo "=== Consolidação de migrações concluída ==="
EOF

  chmod +x backend/consolidate_migrations.sh
  echo "Script de consolidação de migrações criado em backend/consolidate_migrations.sh"
}

# Function to start the development environment
start_dev() {
  # Ask about resetting before starting
  ask_reset
  reset_requested=$?

  echo "Starting development environment..."
  # Use --build to ensure changes in Dockerfile/entrypoint are picked up
  docker compose -f docker-compose.dev.yaml up --build -d
  echo "Development environment started!"
  echo "Backend running at: http://localhost:3000"
  echo "Frontend running at: http://localhost:3001"

  # If reset was requested, we need to wait for the database to be ready
  if [ $reset_requested -eq 1 ]; then
    echo "Aguardando o banco de dados inicializar..."
    sleep 10
    echo "Aplicando migrações..."
    docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma migrate deploy"
    echo "Migrações aplicadas com sucesso!"

    # Apply RBAC triggers
    echo "Aplicando triggers RBAC..."
    docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && make db-apply-rbac-triggers"
    echo "Triggers RBAC aplicados com sucesso!"
  fi
}

# Function to stop the development environment
stop_dev() {
  echo "Stopping development environment..."
  docker compose -f docker-compose.dev.yaml down
  echo "Development environment stopped!"
}

# Function to show logs
show_logs() {
  if [ "$1" == "backend" ]; then
    docker compose -f docker-compose.dev.yaml logs -f backend
  elif [ "$1" == "frontend" ]; then
    docker compose -f docker-compose.dev.yaml logs -f frontend
  elif [ "$1" == "db" ]; then
    docker compose -f docker-compose.dev.yaml logs -f database
  else
    docker compose -f docker-compose.dev.yaml logs -f
  fi
}

# Function to rebuild and restart the environment
rebuild_dev() {
  # Ask about resetting before rebuilding
  ask_reset
  reset_requested=$?

  echo "Rebuilding development environment..."
  docker compose -f docker-compose.dev.yaml down
  docker compose -f docker-compose.dev.yaml build
  docker compose -f docker-compose.dev.yaml up -d
  echo "Development environment rebuilt and started!"

  # If reset was requested, we need to wait for the database to be ready
  if [ $reset_requested -eq 1 ]; then
    echo "Aguardando o banco de dados inicializar..."
    sleep 10
    echo "Aplicando migrações..."
    docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma migrate deploy"
    echo "Migrações aplicadas com sucesso!"
  fi
}

# Function to execute a command in a service container
exec_command() {
  if [ -z "$1" ] || [ -z "$2" ]; then
    echo "Error: Missing service name or command"
    echo "Usage: ./dev.sh exec <service> '<command>'"
    echo "Example: ./dev.sh exec backend 'npm run test'"
    exit 1
  fi

  echo "Executing command in $1 container: $2"
  docker compose -f docker-compose.dev.yaml exec $1 sh -c "$2"
}

# Function to reset the database
db_reset() {
  echo "\n=== Reset do Banco de Dados ===\n"
  echo "Escolha uma opção:"
  echo "1) Resetar banco de dados e consolidar migrações"
  echo "2) Resetar apenas o banco de dados (manter migrações)"
  echo "3) Cancelar"
  echo "\nEscolha uma opção (3): "
  read -r choice

  case "${choice:-3}" in
    1)
      echo "\nResetando banco de dados e consolidando migrações..."
      # Parar os contêineres primeiro
      docker compose -f docker-compose.dev.yaml down
      # Remover volumes
      docker volume rm fluxo-max_db_data fluxo-max_prisma_migrations 2>/dev/null || true
      # Criar script para consolidar migrações
      create_migration_consolidation_script
      # Executar script de consolidação
      cd backend && bash ./consolidate_migrations.sh && cd ..
      # Reiniciar os contêineres
      docker compose -f docker-compose.dev.yaml up --build -d
      echo "Aguardando o banco de dados inicializar..."
      sleep 10
      echo "Aplicando migrações..."
      docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma migrate deploy"
      echo "Migrações aplicadas com sucesso!"
      ;;
    2)
      echo "\nResetando apenas o banco de dados..."
      # Parar os contêineres primeiro
      docker compose -f docker-compose.dev.yaml down
      # Remover apenas o volume do banco de dados
      docker volume rm fluxo-max_db_data 2>/dev/null || true
      # Reiniciar os contêineres
      docker compose -f docker-compose.dev.yaml up --build -d
      echo "Aguardando o banco de dados inicializar..."
      sleep 10
      echo "Aplicando migrações..."
      docker compose -f docker-compose.dev.yaml exec backend sh -c "cd /app && npx prisma migrate deploy"
      echo "Migrações aplicadas com sucesso!"
      ;;
    3|"")
      echo "\nOperação cancelada."
      ;;
    *)
      echo "\nOpção inválida. Operação cancelada."
      ;;
  esac
}

# Function to consolidate migrations
db_consolidate() {
  echo "\n=== Consolidação de Migrações ===\n"
  echo "Esta operação irá consolidar todas as migrações em um único arquivo."
  echo "Deseja continuar? (s/N): "
  read -r confirm

  if [[ "$confirm" =~ ^[Ss]$ ]]; then
    echo "\nConsolidando migrações..."
    # Criar script para consolidar migrações
    create_migration_consolidation_script
    # Executar script de consolidação
    cd backend && bash ./consolidate_migrations.sh && cd ..
    echo "Migrações consolidadas com sucesso!"
  else
    echo "\nOperação cancelada."
  fi
}

# Function to export seed data
db_export_seed() {
  echo "\n=== Exportação de Dados para Seed ===\n"
  echo "Esta operação irá exportar os dados atuais do banco de dados para um script de seed."
  echo "Deseja continuar? (s/N): "
  read -r confirm

  if [[ "$confirm" =~ ^[Ss]$ ]]; then
    echo "\nExportando dados para script de seed..."
    # Executar o comando no backend
    docker compose -f docker-compose.dev.yaml exec backend bash -c "cd /app && make db-export-seed docker=1"
    echo "\nDados exportados com sucesso!"
  else
    echo "\nOperação cancelada."
  fi
}

# Main script logic
case "$1" in
  start)
    start_dev
    ;;
  stop)
    stop_dev
    ;;
  restart)
    stop_dev
    start_dev
    ;;
  logs)
    show_logs
    ;;
  logs:backend)
    show_logs "backend"
    ;;
  logs:frontend)
    show_logs "frontend"
    ;;
  logs:db)
    show_logs "db"
    ;;
  rebuild)
    rebuild_dev
    ;;
  exec)
    exec_command "$2" "$3"
    ;;
  db:reset)
    db_reset
    ;;
  db:consolidate)
    db_consolidate
    ;;
  db:export-seed)
    db_export_seed
    ;;
  help|*)
    show_help
    ;;
esac
