// Teste para verificar se o getAddressTypeInfo está funcionando corretamente
// Execute com: node test-address-type-info.js

// Simular as constantes do frontend
const ADDRESS_TYPES = [
  { value: 'main', label: 'Principal', icon: '🏢' },
  { value: 'billing', label: 'Cobran<PERSON>', icon: '💳' },
  { value: 'shipping', label: 'Entrega', icon: '📦' }
];

// Simular a função getAddressTypeInfo
function getAddressTypeInfo(type) {
  return ADDRESS_TYPES.find(t => t.value === type) || ADDRESS_TYPES[0];
}

console.log('=== Teste do getAddressTypeInfo ===\n');

console.log('ADDRESS_TYPES disponíveis:');
ADDRESS_TYPES.forEach(type => {
  console.log(`  ${type.value} -> ${type.label} ${type.icon}`);
});

console.log('\nTestando mapeamentos:');

const testCases = [
  { input: 'main', expected: 'Principal' },
  { input: 'billing', expected: 'Cobran<PERSON>' },
  { input: 'shipping', expected: 'Entrega' },
  { input: 'invalid', expected: 'Principal' }, // Deve retornar o primeiro (fallback)
  { input: undefined, expected: 'Principal' }, // Deve retornar o primeiro (fallback)
  { input: null, expected: 'Principal' }, // Deve retornar o primeiro (fallback)
];

testCases.forEach(testCase => {
  const result = getAddressTypeInfo(testCase.input);
  const success = result.label === testCase.expected;
  console.log(`  ${testCase.input} -> ${result.label} ${success ? '✅' : '❌'} (esperado: ${testCase.expected})`);
});

console.log('\n=== Simulando o fluxo completo ===');

// Simular dados que vêm do backend
const mockBackendData = [
  {
    id: 'addr1',
    addressType: { name: 'Comercial' },
    street: 'Rua Comercial'
  },
  {
    id: 'addr2', 
    addressType: { name: 'Faturamento' },
    street: 'Rua Faturamento'
  }
];

// Simular mapBackendAddressType
function mapBackendAddressType(addressType) {
  if (!addressType?.name) return 'main';
  
  switch (addressType.name) {
    case 'Comercial': return 'main';
    case 'Faturamento': return 'billing';
    case 'Entrega': return 'shipping';
    default: return 'main';
  }
}

console.log('Processando endereços do backend:');
mockBackendData.forEach((addr, index) => {
  const mappedType = mapBackendAddressType(addr.addressType);
  const typeInfo = getAddressTypeInfo(mappedType);
  
  console.log(`\nEndereço ${index + 1}:`);
  console.log(`  Backend: ${addr.addressType?.name}`);
  console.log(`  Mapeado para: ${mappedType}`);
  console.log(`  Label exibido: ${typeInfo.label}`);
  console.log(`  Icon: ${typeInfo.icon}`);
});

console.log('\n=== Verificação Final ===');
const comercialMapped = mapBackendAddressType({ name: 'Comercial' });
const comercialInfo = getAddressTypeInfo(comercialMapped);

const faturamentoMapped = mapBackendAddressType({ name: 'Faturamento' });
const faturamentoInfo = getAddressTypeInfo(faturamentoMapped);

console.log(`Comercial -> ${comercialMapped} -> ${comercialInfo.label}`);
console.log(`Faturamento -> ${faturamentoMapped} -> ${faturamentoInfo.label}`);

if (comercialInfo.label === 'Principal' && faturamentoInfo.label === 'Cobrança') {
  console.log('\n✅ Mapeamento e exibição estão funcionando corretamente!');
  console.log('❓ O problema deve estar na comunicação entre backend e frontend...');
} else {
  console.log('\n❌ Problema encontrado no mapeamento ou exibição!');
}
