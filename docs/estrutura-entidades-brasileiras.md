# Estrutura de Entidades para Empresas Brasileiras

## Índice

1. [An<PERSON><PERSON><PERSON> da Estrutura Atual](#análise-da-estrutura-atual)
   - [Situação Atual do Sistema](#situação-atual-do-sistema)
   - [Limitações Identificadas](#limitações-identificadas)

2. [Diagramas de Arquitetura](#diagramas-de-arquitetura)
   - [Modelo de Dados](#1-modelo-de-dados---estrutura-de-entidades-brasileiras)
   - [Fluxo de Validações](#2-fluxo-de-validações-para-entidades-brasileiras)
   - [Arquitetura de Três Perspectivas](#3-arquitetura-de-três-perspectivas)

3. [Estrutura Proposta](#estrutura-proposta-para-entidades-brasileiras)
   - [Dados Básicos de Identificação](#1-dados-básicos-de-identificação)
   - [Documentos Fi<PERSON>](#2-documentos-fiscais)
   - [Informações de Contato](#3-informações-de-contato)
   - [Classificação Comercial](#4-classificação-comercial)
   - [Informações Bancárias](#5-informações-bancárias-opcional)
   - [Configurações Comerciais](#6-configurações-comerciais)

4. [Validações Específicas Brasileiras](#validações-específicas-brasileiras)

5. [Estrutura de Endereços](#estrutura-de-endereços)

6. [Campos Obrigatórios por Tipo](#campos-obrigatórios-por-tipo)

7. [Melhorias Propostas](#melhorias-propostas-na-estrutura-atual)

8. [Considerações de Implementação](#considerações-de-implementação)

9. [Exemplos Práticos](#exemplos-práticos-de-implementação)

10. [Casos de Uso e Exemplos](#casos-de-uso-e-exemplos)

11. [Testes Automatizados](#testes-automatizados)

12. [Considerações de Performance](#considerações-de-performance)

13. [Próximos Passos](#próximos-passos)

---

## Análise da Estrutura Atual

### Situação Atual do Sistema

O sistema FluxoMax atualmente possui uma estrutura básica para entidades (clientes e fornecedores) que precisa ser expandida para atender completamente às necessidades fiscais e comerciais brasileiras.

#### Estrutura Atual - Tabela `entities`
```sql
CREATE TABLE entities (
    id UUID PRIMARY KEY,
    company_id UUID NOT NULL,
    type VARCHAR(50) CHECK (type IN ('customer', 'supplier')),
    name VARCHAR(255) NOT NULL,
    cnpj VARCHAR(18),
    phone VARCHAR(20),
    contact VARCHAR(100),
    email VARCHAR(255),
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ
);
```

#### Limitações Identificadas

1. **Falta de distinção entre Pessoa Física e Jurídica**
2. **Ausência de campos fiscais obrigatórios** (IE, IM, CPF)
3. **Não suporta entidades mistas** (cliente E fornecedor)
4. **Campos de endereço separados** (boa prática mantida)
5. **Ausência de validações específicas brasileiras**
6. **Falta de campos bancários e comerciais**

## Diagramas de Arquitetura

### 1. Modelo de Dados - Estrutura de Entidades Brasileiras

```mermaid
erDiagram
    COMPANIES {
        uuid id PK
        varchar name
        varchar cnpj
        varchar phone
        varchar email
        uuid address_id FK
        boolean active
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    ENTITIES {
        uuid id PK
        uuid company_id FK
        varchar entity_type "customer|supplier|both"
        varchar person_type "individual|company"
        varchar name
        varchar trade_name "nullable"
        varchar cnpj "nullable"
        varchar cpf "nullable"
        varchar state_registration "nullable"
        varchar municipal_registration "nullable"
        varchar rg "nullable"
        varchar rg_issuer "nullable"
        varchar tax_regime "nullable"
        varchar status "active|inactive|suspended|blocked"
        varchar phone "nullable"
        varchar mobile "nullable"
        varchar email
        varchar website "nullable"
        decimal credit_limit
        integer payment_term_days
        boolean icms_taxpayer
        boolean simple_national
        boolean withhold_taxes
        varchar bank_code "nullable"
        varchar bank_name "nullable"
        varchar agency "nullable"
        varchar account "nullable"
        varchar account_digit "nullable"
        varchar pix_key "nullable"
        text notes "nullable"
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    ADDRESSES {
        uuid id PK
        uuid entity_id FK "nullable"
        uuid company_id FK "nullable"
        uuid address_type_id FK
        varchar street
        varchar number "nullable"
        varchar complement "nullable"
        varchar district
        varchar city
        varchar state
        varchar zip_code
        varchar country
        boolean is_default
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    ADDRESS_TYPES {
        uuid id PK
        varchar name "main|billing|shipping|correspondence"
        varchar description "nullable"
    }

    COMPANIES ||--o{ ENTITIES : "has many"
    ENTITIES ||--o{ ADDRESSES : "has many"
    ADDRESS_TYPES ||--o{ ADDRESSES : "categorizes"
    COMPANIES ||--o{ ADDRESSES : "has many"
```

### 2. Fluxo de Validações para Entidades Brasileiras

```mermaid
flowchart TD
    A[Criar/Editar Entidade] --> B{Tipo de Pessoa?}

    B -->|Pessoa Física| C[Validar CPF]
    B -->|Pessoa Jurídica| D[Validar CNPJ]

    C --> E[CPF Obrigatório]
    C --> F[RG Opcional]
    C --> G[Regime: Individual]

    D --> H[CNPJ Obrigatório]
    D --> I[Inscrição Estadual Obrigatória]
    D --> J[Inscrição Municipal Opcional]
    D --> K{Regime Tributário?}

    K --> L[Simples Nacional]
    K --> M[Lucro Presumido]
    K --> N[Lucro Real]

    E --> O[Validar Formato CPF]
    H --> P[Validar Formato CNPJ]
    I --> Q[Validar IE por Estado]

    O --> R{CPF Válido?}
    P --> S{CNPJ Válido?}
    Q --> T{IE Válida?}

    R -->|Não| U[Erro: CPF Inválido]
    R -->|Sim| V[Validar Unicidade]

    S -->|Não| W[Erro: CNPJ Inválido]
    S -->|Sim| X[Validar Unicidade]

    T -->|Não| Y[Erro: IE Inválida]
    T -->|Sim| Z[Continuar Validação]

    V --> AA[Validar Endereço]
    X --> AA
    Z --> AA

    AA --> BB[Validar CEP]
    BB --> CC[Validar Estado]
    CC --> DD[Validar Telefone]
    DD --> EE[Validar Email]

    EE --> FF{Todas Validações OK?}
    FF -->|Sim| GG[Salvar Entidade]
    FF -->|Não| HH[Retornar Erros]

    U --> HH
    W --> HH
    Y --> HH

    GG --> II[Entidade Criada/Atualizada]
    HH --> JJ[Corrigir Dados]
    JJ --> A
```

### 3. Arquitetura de Três Perspectivas

```mermaid
graph TB
    subgraph "🏗️ PERSPECTIVA DO ARQUITETO DE SOFTWARE"
        A1[Camada de Apresentação<br/>React + TypeScript]
        A2[Camada de API<br/>NestJS + Prisma]
        A3[Camada de Dados<br/>PostgreSQL]
        A4[Serviços Externos<br/>ViaCEP, ReceitaWS]

        A1 --> A2
        A2 --> A3
        A2 --> A4
    end

    subgraph "👨‍💻 PERSPECTIVA DO DESENVOLVEDOR"
        B1[Validações Frontend<br/>Máscaras + Formatação]
        B2[DTOs + Validadores<br/>class-validator]
        B3[Services + Repositories<br/>Prisma ORM]
        B4[Migrations + Seeds<br/>Prisma Migrate]

        B1 --> B2
        B2 --> B3
        B3 --> B4
    end

    subgraph "📊 PERSPECTIVA DO GERENTE DE PRODUTO"
        C1[Experiência do Usuário<br/>Formulários Intuitivos]
        C2[Compliance Fiscal<br/>Documentos Brasileiros]
        C3[Eficiência Operacional<br/>Auto-preenchimento]
        C4[Relatórios e Analytics<br/>Dashboards]

        C1 --> C2
        C2 --> C3
        C3 --> C4
    end

    subgraph "🔄 FLUXOS DE INTEGRAÇÃO"
        D1[Cadastro de Entidade]
        D2[Validação de Documentos]
        D3[Busca de Endereço por CEP]
        D4[Consulta CNPJ/CPF]

        D1 --> D2
        D2 --> D3
        D3 --> D4
    end

    subgraph "📋 REGRAS DE NEGÓCIO"
        E1[Pessoa Física: CPF Obrigatório]
        E2[Pessoa Jurídica: CNPJ + IE]
        E3[Validação por Estado]
        E4[Unicidade por Empresa]

        E1 --> E2
        E2 --> E3
        E3 --> E4
    end

    subgraph "🛡️ SEGURANÇA E COMPLIANCE"
        F1[Validação de Entrada]
        F2[Sanitização de Dados]
        F3[Auditoria de Alterações]
        F4[LGPD Compliance]

        F1 --> F2
        F2 --> F3
        F3 --> F4
    end

    A2 -.-> B2
    B3 -.-> C3
    C1 -.-> D1
    D2 -.-> E1
    E4 -.-> F1
```

## Estrutura Proposta para Entidades Brasileiras

### 1. Dados Básicos de Identificação

#### Campos Obrigatórios
- **`name`** (VARCHAR 255) - Nome/Razão Social
- **`entity_type`** (ENUM) - Tipo de relacionamento comercial
- **`person_type`** (ENUM) - Tipo de pessoa (física/jurídica)
- **`status`** (ENUM) - Status da entidade (ativo/inativo/suspenso)

#### Campos Opcionais
- **`trade_name`** (VARCHAR 255) - Nome fantasia (apenas PJ)
- **`contact_person`** (VARCHAR 100) - Pessoa de contato
- **`notes`** (TEXT) - Observações gerais

### 2. Documentos Fiscais

#### Para Pessoa Jurídica (Obrigatórios)
- **`cnpj`** (VARCHAR 18) - CNPJ formatado
- **`state_registration`** (VARCHAR 20) - Inscrição Estadual
- **`municipal_registration`** (VARCHAR 20) - Inscrição Municipal (opcional)

#### Para Pessoa Física (Obrigatórios)
- **`cpf`** (VARCHAR 14) - CPF formatado
- **`rg`** (VARCHAR 20) - RG (opcional)
- **`rg_issuer`** (VARCHAR 10) - Órgão emissor do RG (opcional)

#### Campos Fiscais Adicionais
- **`tax_regime`** (ENUM) - Regime tributário
- **`icms_taxpayer`** (BOOLEAN) - Contribuinte de ICMS
- **`simple_national`** (BOOLEAN) - Optante pelo Simples Nacional

### 3. Informações de Contato

#### Obrigatórios
- **`email`** (VARCHAR 255) - Email principal

#### Opcionais
- **`phone`** (VARCHAR 20) - Telefone principal
- **`mobile`** (VARCHAR 20) - Celular
- **`fax`** (VARCHAR 20) - Fax
- **`website`** (VARCHAR 255) - Site

### 4. Classificação Comercial

#### Tipo de Entidade (Enum)
```typescript
enum EntityType {
  CUSTOMER = 'customer',           // Apenas cliente
  SUPPLIER = 'supplier',           // Apenas fornecedor
  CUSTOMER_SUPPLIER = 'both'       // Cliente e fornecedor
}
```

#### Tipo de Pessoa (Enum)
```typescript
enum PersonType {
  INDIVIDUAL = 'individual',       // Pessoa Física
  COMPANY = 'company'             // Pessoa Jurídica
}
```

#### Status da Entidade (Enum)
```typescript
enum EntityStatus {
  ACTIVE = 'active',              // Ativo
  INACTIVE = 'inactive',          // Inativo
  SUSPENDED = 'suspended',        // Suspenso
  BLOCKED = 'blocked'             // Bloqueado
}
```

#### Regime Tributário (Enum)
```typescript
enum TaxRegime {
  SIMPLE_NATIONAL = 'simple_national',     // Simples Nacional
  PRESUMED_PROFIT = 'presumed_profit',     // Lucro Presumido
  REAL_PROFIT = 'real_profit',             // Lucro Real
  INDIVIDUAL = 'individual'                // Pessoa Física
}
```

### 5. Informações Bancárias (Opcional)

- **`bank_code`** (VARCHAR 10) - Código do banco
- **`bank_name`** (VARCHAR 100) - Nome do banco
- **`agency`** (VARCHAR 10) - Agência
- **`account`** (VARCHAR 20) - Conta
- **`account_digit`** (VARCHAR 2) - Dígito da conta
- **`pix_key`** (VARCHAR 255) - Chave PIX

### 6. Configurações Comerciais

#### Limites e Prazos
- **`credit_limit`** (DECIMAL 12,2) - Limite de crédito
- **`payment_term_days`** (INTEGER) - Prazo de pagamento padrão
- **`discount_percentage`** (DECIMAL 5,2) - Desconto padrão

#### Configurações Fiscais
- **`withhold_taxes`** (BOOLEAN) - Retém impostos
- **`send_nfe`** (BOOLEAN) - Enviar NFe por email
- **`nfe_email`** (VARCHAR 255) - Email para NFe

## Validações Específicas Brasileiras

### 1. Validação de CNPJ
```regex
/^\d{2}\.\d{3}\.\d{3}\/\d{4}\-\d{2}$/
```

### 2. Validação de CPF
```regex
/^\d{3}\.\d{3}\.\d{3}\-\d{2}$/
```

### 3. Validação de CEP
```regex
/^\d{5}-\d{3}$/
```

### 4. Validação de Telefone
```regex
/^\(\d{2}\)\s\d{4,5}\-\d{4}$/
```

### 5. Estados Brasileiros Válidos
```typescript
const BRAZILIAN_STATES = [
  'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO',
  'MA', 'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI',
  'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
];
```

## Estrutura de Endereços

### Tipos de Endereço Suportados
- **Principal** - Endereço comercial principal
- **Cobrança** - Endereço para envio de boletos/faturas
- **Entrega** - Endereço para entrega de produtos
- **Correspondência** - Endereço para correspondência

### Campos de Endereço
- **`street`** (VARCHAR 255) - Logradouro
- **`number`** (VARCHAR 10) - Número
- **`complement`** (VARCHAR 100) - Complemento
- **`district`** (VARCHAR 100) - Bairro
- **`city`** (VARCHAR 100) - Cidade
- **`state`** (CHAR 2) - Estado (UF)
- **`zip_code`** (VARCHAR 9) - CEP
- **`country`** (VARCHAR 50) - País (padrão: Brasil)
- **`is_default`** (BOOLEAN) - Endereço padrão

## Campos Obrigatórios por Tipo

### Para Pessoa Física (Cliente)
**Obrigatórios:**
- name, person_type, entity_type, cpf, email

**Recomendados:**
- phone, rg, endereço principal

### Para Pessoa Física (Fornecedor)
**Obrigatórios:**
- name, person_type, entity_type, cpf, email

**Recomendados:**
- phone, dados bancários, endereço principal

### Para Pessoa Jurídica (Cliente)
**Obrigatórios:**
- name, person_type, entity_type, cnpj, state_registration, email

**Recomendados:**
- trade_name, phone, tax_regime, endereço principal

### Para Pessoa Jurídica (Fornecedor)
**Obrigatórios:**
- name, person_type, entity_type, cnpj, state_registration, email

**Recomendados:**
- trade_name, phone, tax_regime, dados bancários, endereço principal

## Melhorias Propostas na Estrutura Atual

### 1. Expansão da Tabela `entities`
```sql
ALTER TABLE entities 
ADD COLUMN person_type VARCHAR(20) CHECK (person_type IN ('individual', 'company')),
ADD COLUMN cpf VARCHAR(14),
ADD COLUMN state_registration VARCHAR(20),
ADD COLUMN municipal_registration VARCHAR(20),
ADD COLUMN trade_name VARCHAR(255),
ADD COLUMN tax_regime VARCHAR(30),
ADD COLUMN status VARCHAR(20) DEFAULT 'active',
ADD COLUMN credit_limit DECIMAL(12,2) DEFAULT 0,
ADD COLUMN payment_term_days INTEGER DEFAULT 30,
ADD COLUMN icms_taxpayer BOOLEAN DEFAULT false,
ADD COLUMN simple_national BOOLEAN DEFAULT false,
ADD COLUMN withhold_taxes BOOLEAN DEFAULT false;
```

### 2. Modificação do Campo `type`
```sql
ALTER TABLE entities 
DROP CONSTRAINT entities_type_check,
ADD CONSTRAINT entities_type_check 
CHECK (type IN ('customer', 'supplier', 'both'));
```

### 3. Adição de Índices para Performance
```sql
CREATE INDEX idx_entities_cnpj ON entities(cnpj) WHERE cnpj IS NOT NULL;
CREATE INDEX idx_entities_cpf ON entities(cpf) WHERE cpf IS NOT NULL;
CREATE INDEX idx_entities_company_type ON entities(company_id, type);
CREATE INDEX idx_entities_person_type ON entities(person_type);
```

## Considerações de Implementação

### 1. Migração de Dados Existentes
- Definir `person_type` como 'company' para registros com CNPJ
- Definir `person_type` como 'individual' para registros sem CNPJ
- Manter compatibilidade com API atual

### 2. Validações no Backend
- Implementar validadores específicos para CPF/CNPJ
- Validar obrigatoriedade baseada no tipo de pessoa
- Implementar validação de unicidade por empresa

### 3. Interface do Usuário
- Formulários dinâmicos baseados no tipo de pessoa
- Auto-preenchimento via APIs de CEP e CNPJ
- Máscaras de entrada para documentos brasileiros

### 4. Integração com APIs Externas
- **ViaCEP** - Preenchimento automático de endereço
- **ReceitaWS** - Validação e dados de CNPJ
- **Bancos** - Validação de dados bancários

## Exemplos Práticos de Implementação

### 1. Schema Prisma Atualizado
```prisma
model Entity {
  id                    String    @id @default(uuid()) @db.Uuid
  companyId            String    @map("company_id") @db.Uuid
  entityType           String    @map("entity_type") @db.VarChar(20) // customer, supplier, both
  personType           String    @map("person_type") @db.VarChar(20) // individual, company
  name                 String    @db.VarChar(255)
  tradeName            String?   @map("trade_name") @db.VarChar(255)
  cnpj                 String?   @db.VarChar(18)
  cpf                  String?   @db.VarChar(14)
  stateRegistration    String?   @map("state_registration") @db.VarChar(20)
  municipalRegistration String?  @map("municipal_registration") @db.VarChar(20)
  rg                   String?   @db.VarChar(20)
  rgIssuer             String?   @map("rg_issuer") @db.VarChar(10)
  taxRegime            String?   @map("tax_regime") @db.VarChar(30)
  status               String    @default("active") @db.VarChar(20)
  phone                String?   @db.VarChar(20)
  mobile               String?   @db.VarChar(20)
  email                String    @db.VarChar(255)
  website              String?   @db.VarChar(255)
  creditLimit          Decimal   @default(0) @map("credit_limit") @db.Decimal(12, 2)
  paymentTermDays      Int       @default(30) @map("payment_term_days")
  icmsTaxpayer         Boolean   @default(false) @map("icms_taxpayer")
  simpleNational       Boolean   @default(false) @map("simple_national")
  withholdTaxes        Boolean   @default(false) @map("withhold_taxes")
  bankCode             String?   @map("bank_code") @db.VarChar(10)
  bankName             String?   @map("bank_name") @db.VarChar(100)
  agency               String?   @db.VarChar(10)
  account              String?   @db.VarChar(20)
  accountDigit         String?   @map("account_digit") @db.VarChar(2)
  pixKey               String?   @map("pix_key") @db.VarChar(255)
  notes                String?   @db.Text
  createdAt            DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt            DateTime  @updatedAt @map("updated_at") @db.Timestamptz
  deletedAt            DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  company              Company           @relation(fields: [companyId], references: [id], onDelete: Cascade)
  addresses            Address[]         @relation("EntityAddresses")
  accountsPayable      AccountsPayable[]
  accountsReceivable   AccountsReceivable[]
  transactions         Transaction[]
  recurringSchedules   RecurringSchedule[]

  @@map("entities")
}
```

### 2. DTOs TypeScript
```typescript
// Enums
export enum EntityType {
  CUSTOMER = 'customer',
  SUPPLIER = 'supplier',
  BOTH = 'both'
}

export enum PersonType {
  INDIVIDUAL = 'individual',
  COMPANY = 'company'
}

export enum EntityStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  BLOCKED = 'blocked'
}

export enum TaxRegime {
  SIMPLE_NATIONAL = 'simple_national',
  PRESUMED_PROFIT = 'presumed_profit',
  REAL_PROFIT = 'real_profit',
  INDIVIDUAL = 'individual'
}

// DTO para criação
export class CreateEntityDto {
  @IsString()
  @MaxLength(255)
  name: string;

  @IsEnum(EntityType)
  entityType: EntityType;

  @IsEnum(PersonType)
  personType: PersonType;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  tradeName?: string;

  @ValidateIf(o => o.personType === PersonType.COMPANY)
  @IsNotEmpty()
  @IsString()
  @Matches(/^\d{2}\.\d{3}\.\d{3}\/\d{4}\-\d{2}$/)
  cnpj?: string;

  @ValidateIf(o => o.personType === PersonType.INDIVIDUAL)
  @IsNotEmpty()
  @IsString()
  @Matches(/^\d{3}\.\d{3}\.\d{3}\-\d{2}$/)
  cpf?: string;

  @ValidateIf(o => o.personType === PersonType.COMPANY)
  @IsNotEmpty()
  @IsString()
  @MaxLength(20)
  stateRegistration?: string;

  @IsOptional()
  @IsString()
  @MaxLength(20)
  municipalRegistration?: string;

  @IsOptional()
  @IsEnum(TaxRegime)
  taxRegime?: TaxRegime;

  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  @Matches(/^\(\d{2}\)\s\d{4,5}\-\d{4}$/)
  phone?: string;

  @IsOptional()
  @IsDecimal()
  @Min(0)
  creditLimit?: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  paymentTermDays?: number;
}
```

### 3. Validadores Customizados
```typescript
// Validador de CPF
export function isValidCPF(cpf: string): boolean {
  const cleanCPF = cpf.replace(/\D/g, '');

  if (cleanCPF.length !== 11) return false;
  if (/^(\d)\1{10}$/.test(cleanCPF)) return false;

  // Validação dos dígitos verificadores
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (10 - i);
  }
  let digit = 11 - (sum % 11);
  if (digit === 10 || digit === 11) digit = 0;
  if (digit !== parseInt(cleanCPF.charAt(9))) return false;

  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (11 - i);
  }
  digit = 11 - (sum % 11);
  if (digit === 10 || digit === 11) digit = 0;
  if (digit !== parseInt(cleanCPF.charAt(10))) return false;

  return true;
}

// Validador de CNPJ
export function isValidCNPJ(cnpj: string): boolean {
  const cleanCNPJ = cnpj.replace(/\D/g, '');

  if (cleanCNPJ.length !== 14) return false;
  if (/^(\d)\1{13}$/.test(cleanCNPJ)) return false;

  // Validação dos dígitos verificadores
  const weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
  const weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];

  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(cleanCNPJ.charAt(i)) * weights1[i];
  }
  let digit = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  if (digit !== parseInt(cleanCNPJ.charAt(12))) return false;

  sum = 0;
  for (let i = 0; i < 13; i++) {
    sum += parseInt(cleanCNPJ.charAt(i)) * weights2[i];
  }
  digit = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  if (digit !== parseInt(cleanCNPJ.charAt(13))) return false;

  return true;
}
```

### 4. Migration SQL
```sql
-- Migration para expandir tabela entities
ALTER TABLE entities
ADD COLUMN entity_type VARCHAR(20) DEFAULT 'customer',
ADD COLUMN person_type VARCHAR(20),
ADD COLUMN trade_name VARCHAR(255),
ADD COLUMN cpf VARCHAR(14),
ADD COLUMN state_registration VARCHAR(20),
ADD COLUMN municipal_registration VARCHAR(20),
ADD COLUMN rg VARCHAR(20),
ADD COLUMN rg_issuer VARCHAR(10),
ADD COLUMN tax_regime VARCHAR(30),
ADD COLUMN status VARCHAR(20) DEFAULT 'active',
ADD COLUMN mobile VARCHAR(20),
ADD COLUMN website VARCHAR(255),
ADD COLUMN credit_limit DECIMAL(12,2) DEFAULT 0,
ADD COLUMN payment_term_days INTEGER DEFAULT 30,
ADD COLUMN icms_taxpayer BOOLEAN DEFAULT false,
ADD COLUMN simple_national BOOLEAN DEFAULT false,
ADD COLUMN withhold_taxes BOOLEAN DEFAULT false,
ADD COLUMN bank_code VARCHAR(10),
ADD COLUMN bank_name VARCHAR(100),
ADD COLUMN agency VARCHAR(10),
ADD COLUMN account VARCHAR(20),
ADD COLUMN account_digit VARCHAR(2),
ADD COLUMN pix_key VARCHAR(255),
ADD COLUMN notes TEXT;

-- Atualizar constraint do tipo
ALTER TABLE entities
DROP CONSTRAINT IF EXISTS entities_type_check,
ADD CONSTRAINT entities_entity_type_check
CHECK (entity_type IN ('customer', 'supplier', 'both'));

-- Adicionar constraint para person_type
ALTER TABLE entities
ADD CONSTRAINT entities_person_type_check
CHECK (person_type IN ('individual', 'company'));

-- Adicionar constraint para status
ALTER TABLE entities
ADD CONSTRAINT entities_status_check
CHECK (status IN ('active', 'inactive', 'suspended', 'blocked'));

-- Migrar dados existentes
UPDATE entities
SET person_type = CASE
  WHEN cnpj IS NOT NULL THEN 'company'
  ELSE 'individual'
END,
entity_type = type;

-- Tornar person_type obrigatório após migração
ALTER TABLE entities
ALTER COLUMN person_type SET NOT NULL;

-- Criar índices para performance
CREATE INDEX idx_entities_cnpj ON entities(cnpj) WHERE cnpj IS NOT NULL;
CREATE INDEX idx_entities_cpf ON entities(cpf) WHERE cpf IS NOT NULL;
CREATE INDEX idx_entities_company_entity_type ON entities(company_id, entity_type);
CREATE INDEX idx_entities_person_type ON entities(person_type);
CREATE INDEX idx_entities_status ON entities(status);
```

## Casos de Uso e Exemplos

### 1. Cadastro de Cliente Pessoa Física
```json
{
  "name": "João Silva Santos",
  "entityType": "customer",
  "personType": "individual",
  "cpf": "123.456.789-01",
  "rg": "12.345.678-9",
  "rgIssuer": "SSP-SP",
  "email": "<EMAIL>",
  "phone": "(11) 99999-9999",
  "creditLimit": 5000.00,
  "paymentTermDays": 30,
  "addresses": [
    {
      "type": "main",
      "street": "Rua das Flores",
      "number": "123",
      "district": "Centro",
      "city": "São Paulo",
      "state": "SP",
      "zipCode": "01234-567",
      "isDefault": true
    }
  ]
}
```

### 2. Cadastro de Fornecedor Pessoa Jurídica
```json
{
  "name": "Empresa ABC Ltda",
  "tradeName": "ABC Comércio",
  "entityType": "supplier",
  "personType": "company",
  "cnpj": "12.345.678/0001-90",
  "stateRegistration": "123.456.789.012",
  "municipalRegistration": "********",
  "taxRegime": "simple_national",
  "email": "<EMAIL>",
  "phone": "(11) 3333-4444",
  "website": "https://www.empresaabc.com.br",
  "creditLimit": 50000.00,
  "paymentTermDays": 45,
  "icmsTaxpayer": true,
  "simpleNational": true,
  "bankCode": "001",
  "bankName": "Banco do Brasil",
  "agency": "1234",
  "account": "56789",
  "accountDigit": "0",
  "pixKey": "12.345.678/0001-90",
  "addresses": [
    {
      "type": "main",
      "street": "Avenida Paulista",
      "number": "1000",
      "complement": "Sala 123",
      "district": "Bela Vista",
      "city": "São Paulo",
      "state": "SP",
      "zipCode": "01310-100",
      "isDefault": true
    },
    {
      "type": "billing",
      "street": "Rua da Cobrança",
      "number": "456",
      "district": "Vila Madalena",
      "city": "São Paulo",
      "state": "SP",
      "zipCode": "05433-000",
      "isDefault": false
    }
  ]
}
```

### 3. Entidade Mista (Cliente e Fornecedor)
```json
{
  "name": "Distribuidora XYZ S.A.",
  "tradeName": "XYZ Distribuidora",
  "entityType": "both",
  "personType": "company",
  "cnpj": "98.765.432/0001-10",
  "stateRegistration": "987.654.321.098",
  "taxRegime": "presumed_profit",
  "email": "<EMAIL>",
  "phone": "(11) 2222-3333",
  "creditLimit": 100000.00,
  "paymentTermDays": 60,
  "icmsTaxpayer": true,
  "simpleNational": false
}
```

## Testes Automatizados

### 1. Testes de Validação
```typescript
describe('Entity Validation', () => {
  describe('CPF Validation', () => {
    it('should accept valid CPF', () => {
      expect(isValidCPF('123.456.789-09')).toBe(true);
    });

    it('should reject invalid CPF', () => {
      expect(isValidCPF('111.111.111-11')).toBe(false);
      expect(isValidCPF('123.456.789-00')).toBe(false);
    });
  });

  describe('CNPJ Validation', () => {
    it('should accept valid CNPJ', () => {
      expect(isValidCNPJ('11.222.333/0001-81')).toBe(true);
    });

    it('should reject invalid CNPJ', () => {
      expect(isValidCNPJ('11.111.111/1111-11')).toBe(false);
    });
  });

  describe('Entity Type Validation', () => {
    it('should require CPF for individual person', async () => {
      const dto = {
        name: 'João Silva',
        entityType: 'customer',
        personType: 'individual',
        email: '<EMAIL>'
        // CPF missing
      };

      const errors = await validate(plainToClass(CreateEntityDto, dto));
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('cpf');
    });

    it('should require CNPJ and IE for company', async () => {
      const dto = {
        name: 'Empresa ABC',
        entityType: 'supplier',
        personType: 'company',
        email: '<EMAIL>'
        // CNPJ and stateRegistration missing
      };

      const errors = await validate(plainToClass(CreateEntityDto, dto));
      expect(errors.length).toBeGreaterThan(0);
    });
  });
});
```

### 2. Testes de Integração
```typescript
describe('Entity Service Integration', () => {
  it('should create individual customer successfully', async () => {
    const entityData = {
      name: 'Maria Santos',
      entityType: EntityType.CUSTOMER,
      personType: PersonType.INDIVIDUAL,
      cpf: '123.456.789-09',
      email: '<EMAIL>'
    };

    const result = await entityService.create(entityData, mockUser);

    expect(result.id).toBeDefined();
    expect(result.name).toBe('Maria Santos');
    expect(result.personType).toBe('individual');
  });

  it('should prevent duplicate CNPJ in same company', async () => {
    const entityData = {
      name: 'Empresa Duplicada',
      entityType: EntityType.SUPPLIER,
      personType: PersonType.COMPANY,
      cnpj: '11.222.333/0001-81',
      stateRegistration: '********9',
      email: '<EMAIL>'
    };

    // Create first entity
    await entityService.create(entityData, mockUser);

    // Try to create duplicate
    await expect(
      entityService.create(entityData, mockUser)
    ).rejects.toThrow('Já existe uma entidade com o CNPJ');
  });
});
```

## Considerações de Performance

### 1. Índices Recomendados
```sql
-- Índices para busca rápida por documentos
CREATE INDEX CONCURRENTLY idx_entities_cnpj_active
ON entities(cnpj) WHERE cnpj IS NOT NULL AND deleted_at IS NULL;

CREATE INDEX CONCURRENTLY idx_entities_cpf_active
ON entities(cpf) WHERE cpf IS NOT NULL AND deleted_at IS NULL;

-- Índice composto para filtros comuns
CREATE INDEX CONCURRENTLY idx_entities_company_type_status
ON entities(company_id, entity_type, status) WHERE deleted_at IS NULL;

-- Índice para busca por nome
CREATE INDEX CONCURRENTLY idx_entities_name_trgm
ON entities USING gin(name gin_trgm_ops);
```

### 2. Otimizações de Query
```typescript
// Busca otimizada com filtros
async findEntitiesOptimized(filters: EntityFilters) {
  const where: Prisma.EntityWhereInput = {
    companyId: filters.companyId,
    deletedAt: null,
    ...(filters.entityType && { entityType: filters.entityType }),
    ...(filters.personType && { personType: filters.personType }),
    ...(filters.status && { status: filters.status }),
    ...(filters.search && {
      OR: [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { tradeName: { contains: filters.search, mode: 'insensitive' } },
        { email: { contains: filters.search, mode: 'insensitive' } }
      ]
    })
  };

  return this.prisma.entity.findMany({
    where,
    include: {
      addresses: {
        where: { isDefault: true },
        take: 1
      }
    },
    orderBy: { name: 'asc' },
    skip: (filters.page - 1) * filters.limit,
    take: filters.limit
  });
}
```

## Próximos Passos

1. **Criar migration** para expansão da tabela entities
2. **Atualizar modelos** Prisma e DTOs
3. **Implementar validações** específicas brasileiras
4. **Atualizar interface** para suportar novos campos
5. **Criar testes** para validações e regras de negócio
6. **Documentar APIs** atualizadas
7. **Implementar integração** com APIs externas (ViaCEP, ReceitaWS)
8. **Criar dashboards** para análise de entidades
9. **Implementar auditoria** de alterações
10. **Configurar monitoramento** de performance
