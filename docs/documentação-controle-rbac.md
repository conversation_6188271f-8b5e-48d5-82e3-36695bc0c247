# Documentação do Sistema de Controle de Acesso RBAC Multi-Tenant

Este documento detalha a implementação de um sistema de controle de acesso baseado em papéis (RBAC) em um ambiente multi-tenant, onde várias empresas (tenants) utilizam o mesmo sistema. O objetivo é garantir que os usuários tenham acesso apenas às empresas e recursos para os quais possuem permissão, com papéis específicos definidos por empresa. A tabela `user_company_roles` é o núcleo do controle de acesso multi-tenant, associando usuários a empresas e seus respectivos papéis.

## Contexto

O sistema suporta múltiplas empresas, cada uma com seus próprios dados e usuários. Um usuário pode estar associado a uma ou mais empresas e desempenhar diferentes papéis em cada uma delas, permitindo um controle de acesso granular e flexível. A tabela `users` define a empresa principal do usuário, enquanto `user_company_roles` gerencia associações adicionais e papéis.

## Esquema do Banco de Dados

O esquema foi projetado para suportar um sistema RBAC multi-tenant eficiente e seguro. Abaixo estão as definições das tabelas:

### Tabela `companies`

Armazena informações sobre as empresas (tenants).

```sql
CREATE TABLE IF NOT EXISTS public.companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    cnpj TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    address_id UUID,
    logo TEXT,
    active BOOLEAN DEFAULT true,
    calendar_type TEXT DEFAULT 'standard' CHECK (calendar_type IN ('standard', 'custom')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Tabela `users`

Armazena informações dos usuários, incluindo autenticação e associação a uma empresa principal.

```sql
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE
);
```

### Tabela `profiles`

Armazena informações adicionais dos usuários.

```sql
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    username TEXT UNIQUE NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    avatar_url VARCHAR(255),
    preferences JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Tabela `roles`

Define os papéis disponíveis, associados a uma empresa específica.

```sql
CREATE TABLE IF NOT EXISTS public.roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_admin BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE
);
```

### Tabela `permissions`

Define permissões granulares associadas às empresas.

```sql
CREATE TABLE IF NOT EXISTS public.permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (company_id) REFERENCES "companies"(id) ON DELETE CASCADE
);
```

### Tabela `role_permissions`

Associa papéis a permissões, definindo quais permissões cada papel possui.

```sql
CREATE TABLE IF NOT EXISTS public.role_permissions (
    role_id UUID NOT NULL,
    permission_id UUID NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES "roles"(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES "permissions"(id) ON DELETE CASCADE
);
```

### Tabela `user_company_roles`

Associa usuários a empresas e define seus papéis em cada empresa, sendo o núcleo do controle de acesso multi-tenant.

```sql
CREATE TABLE IF NOT EXISTS public.user_company_roles (
    user_id UUID NOT NULL,
    company_id UUID NOT NULL,
    role_id UUID NOT NULL,
    PRIMARY KEY (user_id, company_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);
```

## Fluxo de Sequência do Esquema

O fluxo de sequência descreve como os dados são criados, associados e utilizados no sistema para gerenciar usuários, empresas e permissões.

### Fluxo: Criação de Empresa, Papéis e Usuário

```mermaid
sequenceDiagram
    participant Admin
    participant Sistema
    participant DB as Banco de Dados

    Admin->>Sistema: Criar Empresa A
    Sistema->>DB: INSERT INTO companies (name, cnpj)
    DB-->>Sistema: companyA_id
    Sistema-->>Admin: Empresa criada

    Admin->>Sistema: Criar Papel "Admin" para Empresa A
    Sistema->>DB: INSERT INTO roles (company_id, name)
    DB-->>Sistema: admin_role_id
    Sistema-->>Admin: Papel criado

    Admin->>Sistema: Criar Permissão "delete_entity"
    Sistema->>DB: INSERT INTO permissions (company_id, action)
    DB-->>Sistema: delete_entity_permission_id
    Sistema-->>Admin: Permissão criada

    Sistema->>DB: INSERT INTO role_permissions (role_id, permission_id)
    DB-->>Sistema: Associação concluída

    Admin->>Sistema: Criar Usuário user1
    Sistema->>DB: INSERT INTO users (company_id, email, password)
    DB-->>Sistema: user1_id
    Sistema->>DB: INSERT INTO profiles (id, username, first_name, last_name)
    DB-->>Sistema: Perfil criado
    Sistema->>DB: INSERT INTO user_company_roles (user_id, company_id, role_id)
    DB-->>Sistema: Associação concluída
    Sistema-->>Admin: Usuário criado e associado
```

### Fluxo: Login e Verificação de Permissões

```mermaid
sequenceDiagram
    participant User as Usuário
    participant Frontend
    participant Backend
    participant DB as Banco de Dados

    User->>Frontend: Login (email, senha)
    Frontend->>Backend: POST /login
    Backend->>DB: SELECT * FROM users WHERE email = '<EMAIL>'
    DB-->>Backend: Dados do usuário
    Backend->>Backend: Verifica senha
    Backend->>DB: SELECT company_id, role_id FROM user_company_roles WHERE user_id = 'user1_id'
    DB-->>Backend: Lista de empresas e papéis
    Backend-->>Frontend: Token JWT com user_id e permissões
    Frontend-->>User: Login bem-sucedido

    User->>Frontend: Solicita exclusão de entidade
    Frontend->>Backend: DELETE /entities/{entityId}?companyId=companyA_id
    Backend->>DB: Verifica permissão "delete_entity" para user1_id em companyA_id
    DB-->>Backend: True
    Backend->>DB: DELETE FROM entities WHERE id = 'entityId'
    DB-->>Backend: Entidade excluída
    Backend-->>Frontend: Sucesso
    Frontend-->>User: Entidade excluída
```

## Implementação no Backend e Frontend

### Backend

No backend, as permissões são verificadas antes de ações sensíveis. Exemplo em pseudo-código:

```javascript
function deleteEntity(entityId, userId, companyId) {
    if (!hasPermission(userId, companyId, 'delete_entity')) {
        throw new Error('403: Forbidden');
    }
    // Executa a exclusão
}

function hasPermission(userId, companyId, action) {
    // Consulta SQL para verificar permissão
    const query = `
        SELECT EXISTS (
            SELECT 1
            FROM user_company_roles ucr
            JOIN role_permissions rp ON ucr.role_id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
            WHERE ucr.user_id = $1
            AND ucr.company_id = $2
            AND p.action = $3
        );
    `;
    // Executa a query com parâmetros userId, companyId, action
}
```

### Frontend

No frontend, a interface é ajustada com base nas permissões. Exemplo em React:

```javascript
function EntityActions({ userPermissions, companyId }) {
    return (
        <div>
            {userPermissions[companyId]?.includes('create_entity') && <button>Criar Entidade</button>}
            {userPermissions[companyId]?.includes('delete_entity') && <button>Deletar Entidade</button>}
        </div>
    );
}
```

## Segurança em Nível de Linha (RLS)

Para bancos PostgreSQL, use RLS para restringir o acesso aos dados. Exemplo para a tabela `companies`:

```sql
CREATE POLICY company_access ON companies
    USING (id IN (
        SELECT company_id
        FROM user_company_roles
        WHERE user_id = auth.uid()
    ));
```

Exemplo para `users`, considerando a empresa principal:

```sql
CREATE POLICY user_access ON users
    USING (company_id IN (
        SELECT company_id
        FROM user_company_roles
        WHERE user_id = auth.uid()
    ));
```

## Alternativa com Supabase

Se o sistema for implementado usando Supabase, o gerenciamento de cadastro de usuários pode ser delegado à tabela `auth.users`, nativa do Supabase para autenticação. Abaixo estão os ajustes necessários:

### Esquema com Supabase

1. **Remoção da Tabela `users`:**
   - A tabela `users` é substituída por `auth.users`, que contém `id` (UUID), `email`, `password` (hash), `created_at`, `updated_at`, e outros campos de autenticação.
   - O campo `company_id` será movido para `profiles` ou gerenciado via metadados em `auth.users`.

2. **Tabela `profiles` Ajustada:**

```sql
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    username TEXT UNIQUE NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    avatar_url VARCHAR(255),
    preferences JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

3. **Tabela `user_company_roles` Ajustada:**

```sql
CREATE TABLE IF NOT EXISTS public.user_company_roles (
    user_id UUID NOT NULL,
    company_id UUID NOT NULL,
    role_id UUID NOT NULL,
    PRIMARY KEY (user_id, company_id, role_id),
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);
```

4. **Outras Tabelas:**
   - As tabelas `companies`, `roles`, `permissions`, e `role_permissions` permanecem inalteradas.

### Fluxo com Supabase

```mermaid
sequenceDiagram
    participant User as Usuário
    participant Frontend
    participant Supabase as Supabase Auth
    participant Backend
    participant DB as Banco de Dados

    User->>Frontend: Login (email, senha)
    Frontend->>Supabase: supabase.auth.signInWithPassword
    Supabase-->>Frontend: Token JWT com user_id
    Frontend->>Backend: GET /user-companies (com token)
    Backend->>DB: SELECT company_id, role_id FROM user_company_roles WHERE user_id = 'user1_id'
    DB-->>Backend: Lista de empresas e papéis
    Backend-->>Frontend: Dados de acesso
    Frontend-->>User: Login bem-sucedido
```

### RLS com Supabase

Exemplo para `profiles`:

```sql
CREATE POLICY profile_access ON profiles
    USING (id = auth.uid());
```

Exemplo para `companies`:

```sql
CREATE POLICY company_access ON companies
    USING (id IN (
        SELECT company_id
        FROM user_company_roles
        WHERE user_id = auth.uid()
    ));
```

### Considerações

- **Autenticação:** Supabase gerencia login, logout e recuperação de senha via `auth.users`.
- **Sincronização:** Use gatilhos ou funções para sincronizar `profiles` com `auth.users` após criação de usuários.
- **Vantagens:** Reduz a manutenção da autenticação e aproveita as funcionalidades integradas do Supabase.

## Implementação Avançada do Sistema de Permissões

Para tornar o sistema mais robusto e facilitar a manutenção, implementamos um sistema de permissões em duas camadas que centraliza as definições de permissões e automatiza sua criação para novas empresas.

### Arquitetura de Permissões em Duas Camadas

#### 1. Tabela `system_permissions` (Global)

```sql
CREATE TABLE IF NOT EXISTS public.system_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    module VARCHAR(50) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

Esta tabela define um "catálogo" centralizado de todas as permissões possíveis no sistema, com:
- `code`: identificador único (ex: "accounts_payable.create")
- `name`: nome amigável para exibição (ex: "Criar contas a pagar")
- `module`: agrupamento lógico das funcionalidades (ex: "finance", "users")
- `description`: descrição detalhada da permissão

#### 2. Relação com a tabela `permissions` existente

A tabela `permissions` é atualizada para referenciar as permissões do sistema:

```sql
ALTER TABLE public.permissions ADD COLUMN IF NOT EXISTS system_permission_id UUID;
ALTER TABLE public.permissions ADD CONSTRAINT fk_system_permission 
    FOREIGN KEY (system_permission_id) REFERENCES system_permissions(id) ON DELETE CASCADE;
```

#### 3. Automação com Triggers

Um trigger é criado para automaticamente gerar todas as permissões necessárias quando uma nova empresa é registrada:

```sql
CREATE OR REPLACE FUNCTION create_company_permissions()
RETURNS TRIGGER AS $$
BEGIN
    -- Inserir permissões da empresa com base nas permissões do sistema
    INSERT INTO public.permissions (company_id, action, description, system_permission_id)
    SELECT 
        NEW.id, 
        sp.code, 
        sp.description, 
        sp.id
    FROM 
        system_permissions sp;
    
    -- Criar papel de administrador com todas as permissões
    INSERT INTO public.roles (company_id, name, description, is_admin)
    VALUES (NEW.id, 'Administrador', 'Administrador com acesso completo', true);
    
    -- Adicionar todas as permissões ao papel de administrador
    INSERT INTO public.role_permissions (role_id, permission_id)
    SELECT 
        (SELECT id FROM public.roles WHERE company_id = NEW.id AND is_admin = true LIMIT 1),
        id
    FROM 
        public.permissions 
    WHERE 
        company_id = NEW.id;
        
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_create_company_permissions
AFTER INSERT ON public.companies
FOR EACH ROW
EXECUTE FUNCTION create_company_permissions();
```

### Fluxo de Trabalho das Permissões

```mermaid
sequenceDiagram
    participant Dev as Desenvolvedor
    participant System as Sistema
    participant CompanyAdmin as Admin da Empresa
    participant DB as Banco de Dados

    Dev->>DB: Define novas permissões em system_permissions
    
    CompanyAdmin->>System: Cria nova empresa
    System->>DB: INSERT INTO companies
    DB->>DB: Trigger create_company_permissions
    DB->>DB: Cria todas as permissões para a empresa
    DB->>DB: Cria papel de administrador
    DB->>DB: Associa todas as permissões ao administrador
    
    CompanyAdmin->>System: Define papéis customizados
    System->>DB: INSERT INTO roles
    CompanyAdmin->>System: Associa permissões aos papéis
    System->>DB: INSERT INTO role_permissions
    
    CompanyAdmin->>System: Associa usuários a papéis
    System->>DB: INSERT INTO user_company_roles
```

### Benefícios do Sistema de Duas Camadas

1. **Padronização**: Todas as empresas usam o mesmo conjunto de permissões, garantindo consistência
2. **Facilidade de manutenção**: Para adicionar uma nova permissão ao sistema, basta inseri-la em `system_permissions` e ela será disponibilizada automaticamente nas novas empresas
3. **Organização por módulos**: Permissões são agrupadas logicamente por área funcional
4. **Evolução do sistema**: Interfaces de administração podem ser criadas facilmente com base na estrutura de módulos
5. **Controle centralizado**: Mudanças na lógica de permissões podem ser feitas de forma centralizada
6. **Documentação embutida**: As permissões são autodocumentadas com descrições claras

## Políticas de Controle de Acesso Avançadas (RLS)

Implementamos políticas abrangentes de Row Level Security (RLS) para todas as tabelas do sistema, garantindo isolamento completo entre empresas e controle granular de acesso aos dados.

### Ativação de RLS para Todas as Tabelas

```sql
-- Habilitar RLS para todas as tabelas
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_company_roles ENABLE ROW LEVEL SECURITY;
-- ... e outras tabelas
```

### Políticas Implementadas

#### Acesso a Perfis de Usuário

Política que permite acesso ao próprio perfil ou a perfis de usuários nas mesmas empresas:

```sql
CREATE POLICY profiles_access ON public.profiles
    USING (
        id = auth.uid() OR -- Próprio perfil
        id IN (
            SELECT u.user_id FROM user_company_roles u
            WHERE u.company_id IN (
                SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()
            )
        )
    );
```

#### Acesso a Dados de Empresa

```sql
CREATE POLICY permissions_access ON public.permissions
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));
```

#### Acesso a Papéis/Permissões

```sql
CREATE POLICY roles_access ON public.roles
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

CREATE POLICY role_permissions_access ON public.role_permissions
    USING (role_id IN (SELECT id FROM roles WHERE company_id IN 
        (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid())));
```

#### Acesso a Dados Financeiros

```sql
CREATE POLICY accounts_payable_access ON public.accounts_payable
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));

CREATE POLICY transactions_access ON public.transactions
    USING (company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()));
```

#### Controle de Acesso para Notificações

```sql
CREATE POLICY notifications_access ON public.notifications
    USING (
        company_id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid())
        OR user_id = auth.uid()
    );
```

### Integração com Soft Delete

Para tabelas que implementam soft delete (exclusão lógica), as políticas RLS também garantem que registros "excluídos" não sejam exibidos:

```sql
CREATE OR REPLACE POLICY companies_access ON companies
    USING (id IN (SELECT company_id FROM user_company_roles WHERE user_id = auth.uid()) 
           AND deleted_at IS NULL);
```

### Considerações sobre RLS

- **Desempenho**: Políticas RLS podem impactar o desempenho de consultas complexas, então é importante otimizar as políticas e criar índices adequados nas colunas frequentemente utilizadas (company_id, user_id, etc.).
- **Consistência**: Todas as consultas diretas ao banco de dados passam automaticamente pelas políticas RLS, garantindo que não seja possível "contornar" o controle de acesso.
- **Manutenção**: As políticas devem ser atualizadas sempre que novos tipos de relacionamentos entre usuários e dados forem criados.
- **Testes**: É fundamental testar as políticas RLS em diferentes cenários para garantir que não haja acesso indevido ou bloqueio acidental a dados legítimos.

## Conclusão

O esquema suporta um sistema RBAC multi-tenant eficiente, com `user_company_roles` gerenciando associações e papéis por empresa, e `users.company_id` definindo a empresa principal na implementação padrão. Os diagramas de fluxo ilustram a criação e uso dos dados, enquanto a alternativa com Supabase oferece uma opção simplificada para autenticação. As implementações de backend, frontend e RLS garantem um controle de acesso seguro e funcional.