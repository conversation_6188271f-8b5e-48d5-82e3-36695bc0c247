# Correção do Erro 400 (Bad Request) ao Salvar Entidades

## Problema Identificado

**Erro**: `POST http://localhost:3000/api/entities 400 (Bad Request)`

**Causa Principal**: Mismatch entre a estrutura de dados enviada pelo frontend e a esperada pelo backend.

## Problemas Específicos Encontrados

### 1. **Campo `companyId` Indevido no Body**

**Problema**: 
- Frontend enviava `companyId` no body da requisição
- Backend obtém `companyId` do token JWT, não do body
- `CreateEntityDto` não possui campo `companyId`

**Solução**:
```typescript
// ❌ ANTES (frontend/src/types/api.ts)
export type CreateEntityRequest = {
  name: string;
  type: EntityType;
  companyId: string; // ← ERRO: Campo não existe no backend
};

// ✅ DEPOIS
export type CreateEntityRequest = {
  name: string;
  type: EntityType;
  cnpj?: string;
  email?: string;
  phone?: string;
  contact?: string;
  // companyId vem do token JWT, não do body
};
```

### 2. **Validação de Campos Obrigatórios Inconsistente**

**Problema**:
- Frontend exigia campos como `email`, `phone`, `contact` como obrigatórios
- Backend permite esses campos como opcionais

**Solução**:
```typescript
// ❌ ANTES (CustomerModal.tsx)
const customerSchema = z.object({
  name: z.string().min(2),
  email: z.string().email(), // ← Obrigatório no frontend
  phone: z.string().min(8),   // ← Obrigatório no frontend
  contact: z.string().min(2), // ← Obrigatório no frontend
});

// ✅ DEPOIS
const customerSchema = z.object({
  name: z.string().min(2),
  email: z.string().email().optional().or(z.literal("")),
  phone: z.string().optional(),
  contact: z.string().optional(),
});
```

### 3. **Estrutura de Dados Incorreta**

**Problema**:
- Frontend enviava campos vazios como strings vazias
- Backend esperava `undefined` para campos opcionais

**Solução**:
```typescript
// ❌ ANTES
const entityRequest: CreateEntityRequest = {
  name: customerData.name,
  type: 'customer',
  email: customerData.email,     // ← Podia ser string vazia
  phone: customerData.phone,     // ← Podia ser string vazia
  contact: customerData.contact, // ← Podia ser string vazia
  companyId: companyId,          // ← Campo indevido
};

// ✅ DEPOIS
const entityRequest: CreateEntityRequest = {
  name: customerData.name,
  type: 'customer',
  email: customerData.email || undefined,     // ← undefined se vazio
  phone: customerData.phone || undefined,     // ← undefined se vazio
  contact: customerData.contact || undefined, // ← undefined se vazio
  // companyId removido
};
```

## Arquitetura Correta Backend vs Frontend

### Backend (CreateEntityDto)
```typescript
export class CreateEntityDto {
  @IsString()
  @MaxLength(255)
  name: string; // ← OBRIGATÓRIO

  @IsEnum(EntityType)
  type: EntityType; // ← OBRIGATÓRIO

  @IsOptional()
  @IsString()
  @MaxLength(18)
  cnpj?: string; // ← OPCIONAL

  @IsOptional()
  @IsString()
  @MaxLength(20)
  phone?: string; // ← OPCIONAL

  @IsOptional()
  @IsString()
  @MaxLength(100)
  contact?: string; // ← OPCIONAL

  @IsOptional()
  @IsEmail()
  @MaxLength(255)
  email?: string; // ← OPCIONAL
}
```

### Frontend (CreateEntityRequest)
```typescript
export type CreateEntityRequest = {
  name: string;        // ← OBRIGATÓRIO
  type: EntityType;    // ← OBRIGATÓRIO
  cnpj?: string;       // ← OPCIONAL
  email?: string;      // ← OPCIONAL
  phone?: string;      // ← OPCIONAL
  contact?: string;    // ← OPCIONAL
  // companyId vem do JWT token
};
```

## Como o Backend Obtém o CompanyId

### Fluxo de Autenticação
1. **Token JWT** contém `companyId` no payload
2. **JwtStrategy** extrai dados do token:
```typescript
async validate(payload: TokenPayload) {
  return {
    id: payload.sub,
    userId: payload.sub,
    email: payload.email,
    companyId: payload.companyId, // ← Vem do token
  };
}
```

3. **Controller** mapeia dados do usuário:
```typescript
create(@Body() createEntityDto: CreateEntityDto, @Req() req: RequestWithUser) {
  const user = {
    userId: req.user.userId,
    email: req.user.email,
    companyId: req.user.companyId, // ← Vem do token JWT
  };
  return this.entitiesService.create(createEntityDto, user);
}
```

4. **Service** usa companyId do usuário:
```typescript
async create(createEntityDto: CreateEntityDto, user: AuthenticatedUser) {
  const { companyId } = user; // ← Obtido do token
  
  return await this.prisma.entity.create({
    data: {
      ...createEntityDto,
      company: { connect: { id: companyId } }, // ← Conecta à empresa
    },
  });
}
```

## Correções Aplicadas

### 1. Tipos TypeScript
- ✅ Removido `companyId` de `CreateEntityRequest`
- ✅ Tornado campos opcionais conforme backend
- ✅ Adicionado comentário explicativo

### 2. Páginas (Customers.tsx / Suppliers.tsx)
- ✅ Removido envio de `companyId` no body
- ✅ Convertido strings vazias para `undefined`
- ✅ Adicionado logs para debug

### 3. Modais (CustomerModal.tsx / SupplierModal.tsx)
- ✅ Ajustado schema Zod para campos opcionais
- ✅ Permitido email vazio ou válido
- ✅ Tornado phone e contact opcionais

## Teste de Verificação

### Dados Enviados (Corretos)
```json
{
  "name": "Cliente Teste",
  "type": "customer",
  "email": "<EMAIL>",
  "phone": "11999999999",
  "contact": "João Silva"
}
```

### Headers da Requisição
```
Content-Type: application/json
Authorization: Bearer <JWT_TOKEN_COM_COMPANY_ID>
```

### Resposta Esperada
```json
{
  "data": {
    "id": "uuid-gerado",
    "name": "Cliente Teste",
    "type": "customer",
    "email": "<EMAIL>",
    "phone": "11999999999",
    "contact": "João Silva",
    "companyId": "company-id-do-token",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

## Status da Correção

✅ **Erro 400 CORRIGIDO**

### Funcionalidades Testadas:
- ✅ Criação de cliente com dados completos
- ✅ Criação de cliente com dados mínimos (só nome)
- ✅ Criação de fornecedor com dados completos
- ✅ Validação de campos obrigatórios
- ✅ Integração com token JWT
- ✅ Persistência no banco de dados

### Próximos Passos:
1. Testar criação de endereços após entidade criada
2. Verificar se listagem de entidades funciona
3. Implementar edição de entidades
4. Adicionar validação de CNPJ (se necessário)
