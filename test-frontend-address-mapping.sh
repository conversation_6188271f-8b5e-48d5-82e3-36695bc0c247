#!/bin/bash

# Script de teste para verificar o mapeamento de tipos de endereço do frontend
# Execute com: chmod +x test-frontend-address-mapping.sh && ./test-frontend-address-mapping.sh

API_BASE_URL="http://localhost:3000/api"
EMAIL="<EMAIL>"
PASSWORD="Admin123"

echo "🚀 Testando mapeamento de tipos de endereço do frontend..."
echo ""

# 1. Login
echo "🔐 Fazendo login..."
LOGIN_RESPONSE=$(curl -s -X POST "${API_BASE_URL}/auth/login" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"${EMAIL}\",\"password\":\"${PASSWORD}\"}")

TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)

if [[ -z "$TOKEN" ]]; then
  echo "❌ Erro ao extrair token do login"
  echo "Resposta: $LOGIN_RESPONSE"
  exit 1
fi

echo "✅ Login realizado com sucesso"
echo ""

# 2. Buscar tipos de endereço
echo "🏷️ Buscando tipos de endereço disponíveis..."
ADDRESS_TYPES_RESPONSE=$(curl -s -X GET "${API_BASE_URL}/address-types?limit=100" \
  -H "Authorization: Bearer ${TOKEN}")

echo "Resposta dos tipos de endereço:"
echo $ADDRESS_TYPES_RESPONSE | jq '.' 2>/dev/null || echo $ADDRESS_TYPES_RESPONSE
echo ""

# 3. Extrair ID do tipo "Comercial" (que corresponde a 'main' no frontend)
COMERCIAL_ID=$(echo $ADDRESS_TYPES_RESPONSE | grep -o '"id":"[^"]*","name":"Comercial"' | cut -d'"' -f4)
FATURAMENTO_ID=$(echo $ADDRESS_TYPES_RESPONSE | grep -o '"id":"[^"]*","name":"Faturamento"' | cut -d'"' -f4)
ENTREGA_ID=$(echo $ADDRESS_TYPES_RESPONSE | grep -o '"id":"[^"]*","name":"Entrega"' | cut -d'"' -f4)

echo "IDs dos tipos de endereço:"
echo "  - Comercial (main): $COMERCIAL_ID"
echo "  - Faturamento (billing): $FATURAMENTO_ID"
echo "  - Entrega (shipping): $ENTREGA_ID"
echo ""

# 4. Buscar primeira entidade
echo "📋 Buscando primeira entidade..."
ENTITIES_RESPONSE=$(curl -s -X GET "${API_BASE_URL}/entities?limit=1" \
  -H "Authorization: Bearer ${TOKEN}")

ENTITY_ID=$(echo $ENTITIES_RESPONSE | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

if [[ -z "$ENTITY_ID" ]]; then
  echo "❌ Nenhuma entidade encontrada"
  exit 1
fi

echo "✅ Entidade encontrada: $ENTITY_ID"
echo ""

# 5. Testar criação de endereço com addressTypeId específico (simulando frontend)
echo "--- TESTE 1: Endereço tipo 'Comercial' ---"
echo "🏠 Criando endereço com tipo Comercial (simulando frontend 'main')..."

ADDRESS_DATA_COMERCIAL="{
  \"street\": \"Rua Comercial\",
  \"number\": \"100\",
  \"complement\": \"Loja 1\",
  \"district\": \"Centro\",
  \"city\": \"São Paulo\",
  \"state\": \"SP\",
  \"zipCode\": \"01000-000\",
  \"isDefault\": false,
  \"addressTypeId\": \"$COMERCIAL_ID\"
}"

echo "📤 Dados do endereço: $ADDRESS_DATA_COMERCIAL"

CREATE_RESPONSE_1=$(curl -s -X POST "${API_BASE_URL}/entities/${ENTITY_ID}/addresses" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d "$ADDRESS_DATA_COMERCIAL")

NEW_ADDRESS_ID_1=$(echo $CREATE_RESPONSE_1 | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

if [[ -z "$NEW_ADDRESS_ID_1" ]]; then
  echo "❌ Erro ao criar endereço comercial"
  echo "Resposta: $CREATE_RESPONSE_1"
else
  echo "✅ Endereço comercial criado: $NEW_ADDRESS_ID_1"
  echo "Resposta: $CREATE_RESPONSE_1"
fi
echo ""

# 6. Testar criação de endereço com tipo Faturamento
echo "--- TESTE 2: Endereço tipo 'Faturamento' ---"
echo "🏠 Criando endereço com tipo Faturamento (simulando frontend 'billing')..."

ADDRESS_DATA_FATURAMENTO="{
  \"street\": \"Rua Faturamento\",
  \"number\": \"200\",
  \"complement\": \"Sala 2\",
  \"district\": \"Vila Nova\",
  \"city\": \"São Paulo\",
  \"state\": \"SP\",
  \"zipCode\": \"02000-000\",
  \"isDefault\": false,
  \"addressTypeId\": \"$FATURAMENTO_ID\"
}"

echo "📤 Dados do endereço: $ADDRESS_DATA_FATURAMENTO"

CREATE_RESPONSE_2=$(curl -s -X POST "${API_BASE_URL}/entities/${ENTITY_ID}/addresses" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d "$ADDRESS_DATA_FATURAMENTO")

NEW_ADDRESS_ID_2=$(echo $CREATE_RESPONSE_2 | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

if [[ -z "$NEW_ADDRESS_ID_2" ]]; then
  echo "❌ Erro ao criar endereço de faturamento"
  echo "Resposta: $CREATE_RESPONSE_2"
else
  echo "✅ Endereço de faturamento criado: $NEW_ADDRESS_ID_2"
  echo "Resposta: $CREATE_RESPONSE_2"
fi
echo ""

# 7. Verificar se os endereços foram persistidos corretamente
echo "--- VERIFICAÇÃO FINAL ---"
echo "📍 Buscando todos os endereços da entidade..."
ADDRESSES_FINAL=$(curl -s -X GET "${API_BASE_URL}/entities/${ENTITY_ID}/addresses" \
  -H "Authorization: Bearer ${TOKEN}")

ADDRESSES_COUNT=$(echo $ADDRESSES_FINAL | grep -o '"id":"[^"]*"' | wc -l)
echo "Total de endereços encontrados: $ADDRESSES_COUNT"

echo ""
echo "Detalhes dos endereços:"
echo $ADDRESSES_FINAL | jq '.' 2>/dev/null || echo $ADDRESSES_FINAL

echo ""
echo "--- RESUMO DOS TESTES ---"
if [[ -n "$NEW_ADDRESS_ID_1" ]]; then
  echo "✅ Endereço Comercial: CRIADO"
  if echo $ADDRESSES_FINAL | grep -q "$COMERCIAL_ID"; then
    echo "✅ AddressTypeId Comercial: PERSISTIDO"
  else
    echo "❌ AddressTypeId Comercial: NÃO PERSISTIDO"
  fi
else
  echo "❌ Endereço Comercial: FALHOU"
fi

if [[ -n "$NEW_ADDRESS_ID_2" ]]; then
  echo "✅ Endereço Faturamento: CRIADO"
  if echo $ADDRESSES_FINAL | grep -q "$FATURAMENTO_ID"; then
    echo "✅ AddressTypeId Faturamento: PERSISTIDO"
  else
    echo "❌ AddressTypeId Faturamento: NÃO PERSISTIDO"
  fi
else
  echo "❌ Endereço Faturamento: FALHOU"
fi

echo ""
echo "🎉 Teste de mapeamento concluído!"
