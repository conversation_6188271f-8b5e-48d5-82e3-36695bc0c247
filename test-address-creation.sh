#!/bin/bash

# Script de teste para verificar a criação de endereços de entidades
# Execute com: chmod +x test-address-creation.sh && ./test-address-creation.sh

API_BASE_URL="http://localhost:3000/api"
EMAIL="<EMAIL>"
PASSWORD="Admin123"

echo "🚀 Iniciando teste de criação de endereços..."
echo ""

# 1. Login
echo "🔐 Fazendo login..."
LOGIN_RESPONSE=$(curl -s -X POST "${API_BASE_URL}/auth/login" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"${EMAIL}\",\"password\":\"${PASSWORD}\"}")

if [[ $? -ne 0 ]]; then
  echo "❌ Erro na requisição de login"
  exit 1
fi

TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)

if [[ -z "$TOKEN" ]]; then
  echo "❌ Erro ao extrair token do login"
  echo "Resposta: $LOGIN_RESPONSE"
  exit 1
fi

echo "✅ Login realizado com sucesso"
echo ""

# 2. Buscar primeira entidade
echo "📋 Buscando primeira entidade..."
ENTITIES_RESPONSE=$(curl -s -X GET "${API_BASE_URL}/entities?limit=1" \
  -H "Authorization: Bearer ${TOKEN}")

if [[ $? -ne 0 ]]; then
  echo "❌ Erro na requisição de entidades"
  exit 1
fi

ENTITY_ID=$(echo $ENTITIES_RESPONSE | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

if [[ -z "$ENTITY_ID" ]]; then
  echo "❌ Nenhuma entidade encontrada"
  echo "Resposta: $ENTITIES_RESPONSE"
  exit 1
fi

echo "✅ Entidade encontrada: $ENTITY_ID"
echo ""

# 3. Buscar tipos de endereço
echo "🏷️ Buscando tipos de endereço..."
ADDRESS_TYPES_RESPONSE=$(curl -s -X GET "${API_BASE_URL}/address-types?limit=100" \
  -H "Authorization: Bearer ${TOKEN}")

echo "Tipos de endereço disponíveis:"
echo $ADDRESS_TYPES_RESPONSE | grep -o '"name":"[^"]*"' | cut -d'"' -f4
echo ""

# 4. Buscar endereços existentes
echo "--- ANTES DA CRIAÇÃO ---"
echo "📍 Buscando endereços da entidade..."
ADDRESSES_BEFORE=$(curl -s -X GET "${API_BASE_URL}/entities/${ENTITY_ID}/addresses" \
  -H "Authorization: Bearer ${TOKEN}")

ADDRESSES_COUNT_BEFORE=$(echo $ADDRESSES_BEFORE | grep -o '"id":"[^"]*"' | wc -l)
echo "Endereços existentes: $ADDRESSES_COUNT_BEFORE"
echo ""

# 5. Criar novo endereço
echo "--- CRIANDO ENDEREÇO ---"
echo "🏠 Criando endereço de teste..."

ADDRESS_DATA='{
  "street": "Rua de Teste",
  "number": "123",
  "complement": "Sala 456",
  "district": "Bairro Teste",
  "city": "São Paulo",
  "state": "SP",
  "zipCode": "01234-567",
  "isDefault": true
}'

echo "📤 Dados do endereço: $ADDRESS_DATA"

CREATE_RESPONSE=$(curl -s -X POST "${API_BASE_URL}/entities/${ENTITY_ID}/addresses" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d "$ADDRESS_DATA")

if [[ $? -ne 0 ]]; then
  echo "❌ Erro na requisição de criação"
  exit 1
fi

NEW_ADDRESS_ID=$(echo $CREATE_RESPONSE | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

if [[ -z "$NEW_ADDRESS_ID" ]]; then
  echo "❌ Erro ao criar endereço"
  echo "Resposta: $CREATE_RESPONSE"
  exit 1
fi

echo "✅ Endereço criado com sucesso: $NEW_ADDRESS_ID"
echo "Resposta completa: $CREATE_RESPONSE"
echo ""

# 6. Buscar endereços novamente
echo "--- DEPOIS DA CRIAÇÃO ---"
echo "📍 Buscando endereços da entidade novamente..."
ADDRESSES_AFTER=$(curl -s -X GET "${API_BASE_URL}/entities/${ENTITY_ID}/addresses" \
  -H "Authorization: Bearer ${TOKEN}")

ADDRESSES_COUNT_AFTER=$(echo $ADDRESSES_AFTER | grep -o '"id":"[^"]*"' | wc -l)
echo "Endereços após criação: $ADDRESSES_COUNT_AFTER"
echo ""

# 7. Verificar se o endereço foi persistido corretamente
echo "--- VERIFICAÇÃO ---"
if echo $ADDRESSES_AFTER | grep -q "$NEW_ADDRESS_ID"; then
  echo "✅ Endereço encontrado na consulta posterior!"
  
  # Verificar campos específicos
  if echo $ADDRESSES_AFTER | grep -q '"entityId":"'$ENTITY_ID'"'; then
    echo "✅ EntityID persistido: ✓"
  else
    echo "❌ EntityID persistido: ✗"
  fi
  
  if echo $ADDRESSES_AFTER | grep -q '"addressTypeId":"[^"]*"'; then
    echo "✅ AddressTypeID persistido: ✓"
  else
    echo "❌ AddressTypeID persistido: ✗"
  fi
  
  if echo $ADDRESSES_AFTER | grep -q '"street":"Rua de Teste"'; then
    echo "✅ Dados completos: ✓"
  else
    echo "❌ Dados completos: ✗"
  fi
  
else
  echo "❌ Endereço NÃO encontrado na consulta posterior!"
fi

echo ""
echo "📊 Resumo:"
echo "  - Endereços antes: $ADDRESSES_COUNT_BEFORE"
echo "  - Endereços depois: $ADDRESSES_COUNT_AFTER"
echo "  - Novo endereço ID: $NEW_ADDRESS_ID"
echo ""
echo "🎉 Teste concluído!"
