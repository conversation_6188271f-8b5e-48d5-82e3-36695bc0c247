#!/bin/bash

# Teste final para verificar se o problema de categorias hierárquicas foi resolvido
API_BASE="http://localhost:3000/api"

echo "🎯 TESTE FINAL: Verificando problema de categorias hierárquicas"
echo "=============================================================="

# Fazer login
echo "🔐 Fazendo login..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin123"}')

TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
  echo "❌ FALHA: Erro no login"
  exit 1
fi

echo "✅ Login realizado com sucesso"

# Cenário do problema: Criar categoria com categoria pai
echo ""
echo "📝 CENÁRIO: Criando categoria com categoria pai..."

# 1. Criar categoria pai
echo "1️⃣ Criando categoria pai 'Despesas Operacionais'..."
PARENT_RESPONSE=$(curl -s -X POST "$API_BASE/categories" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"name":"Despesas Operacionais","transactionType":"payable"}')

PARENT_ID=$(echo $PARENT_RESPONSE | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

if [ -z "$PARENT_ID" ]; then
  echo "❌ FALHA: Erro ao criar categoria pai"
  echo "Resposta: $PARENT_RESPONSE"
  exit 1
fi

echo "✅ Categoria pai criada com ID: $PARENT_ID"

# 2. Criar categoria filha selecionando a categoria pai
echo ""
echo "2️⃣ Criando categoria filha 'Material de Escritório' com categoria pai..."
CHILD_RESPONSE=$(curl -s -X POST "$API_BASE/categories" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"name\":\"Material de Escritório\",\"transactionType\":\"payable\",\"parentCategoryId\":\"$PARENT_ID\"}")

CHILD_ID=$(echo $CHILD_RESPONSE | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

if [ -z "$CHILD_ID" ]; then
  echo "❌ FALHA: Erro ao criar categoria filha"
  echo "Resposta: $CHILD_RESPONSE"
  exit 1
fi

echo "✅ Categoria filha criada com ID: $CHILD_ID"

# 3. Verificar se ambas aparecem na listagem (endpoint /tree)
echo ""
echo "3️⃣ Verificando se as categorias aparecem na listagem..."
TREE_RESPONSE=$(curl -s -X GET "$API_BASE/categories/tree" \
  -H "Authorization: Bearer $TOKEN")

# Verificar categoria pai
PARENT_IN_TREE=$(echo $TREE_RESPONSE | jq --arg id "$PARENT_ID" '.[] | select(.id == $id)')

if [ -z "$PARENT_IN_TREE" ]; then
  echo "❌ FALHA: Categoria pai não encontrada na listagem"
  echo "Árvore atual:"
  echo $TREE_RESPONSE | jq '.'
  exit 1
fi

echo "✅ Categoria pai encontrada na listagem"

# Verificar categoria filha aninhada
CHILD_IN_TREE=$(echo $TREE_RESPONSE | jq --arg parent_id "$PARENT_ID" --arg child_id "$CHILD_ID" '.[] | select(.id == $parent_id) | .children[]? | select(.id == $child_id)')

if [ -z "$CHILD_IN_TREE" ]; then
  echo "❌ FALHA: Categoria filha não encontrada aninhada na categoria pai"
  echo "Categoria pai encontrada:"
  echo $PARENT_IN_TREE | jq '.'
  exit 1
fi

echo "✅ Categoria filha encontrada corretamente aninhada na categoria pai"

# 4. Verificar estrutura hierárquica
echo ""
echo "4️⃣ Verificando estrutura hierárquica..."
PARENT_NAME=$(echo $PARENT_IN_TREE | jq -r '.name')
CHILD_NAME=$(echo $CHILD_IN_TREE | jq -r '.name')
CHILD_PARENT_ID=$(echo $CHILD_IN_TREE | jq -r '.parentCategoryId')

echo "📁 $PARENT_NAME (ID: $PARENT_ID)"
echo "  └── 📄 $CHILD_NAME (ID: $CHILD_ID, Parent: $CHILD_PARENT_ID)"

if [ "$CHILD_PARENT_ID" = "$PARENT_ID" ]; then
  echo "✅ Relacionamento pai-filho correto"
else
  echo "❌ FALHA: Relacionamento pai-filho incorreto"
  exit 1
fi

# 5. Verificar endpoint de listagem plana
echo ""
echo "5️⃣ Verificando endpoint de listagem plana..."
LIST_RESPONSE=$(curl -s -X GET "$API_BASE/categories" \
  -H "Authorization: Bearer $TOKEN")

PARENT_IN_LIST=$(echo $LIST_RESPONSE | jq --arg id "$PARENT_ID" '.data[] | select(.id == $id)')
CHILD_IN_LIST=$(echo $LIST_RESPONSE | jq --arg id "$CHILD_ID" '.data[] | select(.id == $id)')

if [ -n "$PARENT_IN_LIST" ] && [ -n "$CHILD_IN_LIST" ]; then
  echo "✅ Ambas as categorias aparecem na listagem plana"
else
  echo "❌ FALHA: Categorias não encontradas na listagem plana"
  echo "Listagem atual:"
  echo $LIST_RESPONSE | jq '.data'
  exit 1
fi

# Resultado final
echo ""
echo "🎉 SUCESSO: PROBLEMA RESOLVIDO!"
echo "============================================"
echo "✅ Categoria pai criada e exibida corretamente"
echo "✅ Categoria filha criada com categoria pai"
echo "✅ Categoria filha aparece na listagem hierárquica"
echo "✅ Relacionamento pai-filho funciona corretamente"
echo "✅ Ambas aparecem na listagem plana"
echo ""
echo "📊 Estrutura final das categorias:"
echo $TREE_RESPONSE | jq '.'

# Limpeza
echo ""
echo "🧹 Limpando categorias de teste..."
curl -s -X DELETE "$API_BASE/categories/$CHILD_ID" -H "Authorization: Bearer $TOKEN" > /dev/null
curl -s -X DELETE "$API_BASE/categories/$PARENT_ID" -H "Authorization: Bearer $TOKEN" > /dev/null
echo "✅ Limpeza concluída"

echo ""
echo "🌐 Para testar na interface web:"
echo "   URL: http://localhost:3001/categories"
echo "   Login: <EMAIL>"
echo "   Senha: Admin123"
