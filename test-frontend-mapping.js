// Teste para simular o mapeamento do frontend
// Execute com: node test-frontend-mapping.js

// Simular a função mapBackendAddressType
function mapBackendAddressType(addressType) {
  console.log('[mapBackendAddressType] Recebido:', addressType);
  console.log('[mapBackendAddressType] addressType.name:', addressType?.name);
  
  if (!addressType?.name) {
    console.log('[mapBackendAddressType] Sem nome, retornando main');
    return 'main';
  }

  let result;
  switch (addressType.name) {
    case 'Comercial':
      result = 'main';
      break;
    case 'Faturamento':
      result = 'billing';
      break;
    case 'Entrega':
      result = 'shipping';
      break;
    default:
      result = 'main';
      break;
  }
  
  console.log('[mapBackendAddressType] Mapeando', addressType.name, '->', result);
  return result;
}

// Simular dados que vêm do backend (baseado nos logs)
const mockBackendData = [
  {
    id: 'bce366bb-4e36-4121-a97a-a7d58216cf7b',
    entityId: 'c21a751a-8c80-4477-9dfa-2fbd36415525',
    addressTypeId: 'cb9376f2-a1a8-4607-9974-1c106e1d4cf0',
    addressType: { id: 'cb9376f2-a1a8-4607-9974-1c106e1d4cf0', name: 'Comercial' },
    street: 'Rua Ângelo Vial',
    isDefault: false
  },
  {
    id: 'fbcd842a-5b45-402a-8e00-223162a1e73d',
    entityId: 'c21a751a-8c80-4477-9dfa-2fbd36415525',
    addressTypeId: '4bb1134a-4466-486f-abb9-619eaa805e1b',
    addressType: { id: '4bb1134a-4466-486f-abb9-619eaa805e1b', name: 'Faturamento' },
    street: 'Rua José Cláudio de Oliveira Prince Rodrigues',
    isDefault: false
  }
];

console.log('=== Teste de Mapeamento do Frontend ===\n');

console.log('Dados simulados do backend:');
console.log(JSON.stringify(mockBackendData, null, 2));
console.log('');

console.log('Processando endereços...');
const processedAddresses = mockBackendData.map(addr => {
  console.log('\n[EntityAddressManager] Processando endereço:', {
    id: addr.id,
    addressType: addr.addressType,
    addressTypeName: addr.addressType?.name,
    rawAddr: addr
  });
  
  const mappedType = mapBackendAddressType(addr.addressType);
  console.log('[EntityAddressManager] Tipo mapeado:', mappedType);
  
  return {
    ...addr,
    type: mappedType,
  };
});

console.log('\n=== Resultado Final ===');
processedAddresses.forEach((addr, index) => {
  console.log(`\nEndereço ${index + 1}:`);
  console.log('  ID:', addr.id);
  console.log('  Street:', addr.street);
  console.log('  AddressType.name:', addr.addressType?.name);
  console.log('  Tipo mapeado (type):', addr.type);
  console.log('  IsDefault:', addr.isDefault);
});

console.log('\n=== Verificação ===');
const comercialAddr = processedAddresses.find(addr => addr.addressType?.name === 'Comercial');
const faturamentoAddr = processedAddresses.find(addr => addr.addressType?.name === 'Faturamento');

console.log('Endereço Comercial mapeado para:', comercialAddr?.type, '(esperado: main)');
console.log('Endereço Faturamento mapeado para:', faturamentoAddr?.type, '(esperado: billing)');

if (comercialAddr?.type === 'main' && faturamentoAddr?.type === 'billing') {
  console.log('\n✅ Mapeamento está funcionando corretamente!');
  console.log('❓ O problema pode estar em outro lugar...');
} else {
  console.log('\n❌ Problema encontrado no mapeamento!');
}
