// Script para debugar os tipos de endereço
// Execute com: node debug-address-types.js

const https = require('https');
const http = require('http');

// Configurações
const API_BASE_URL = 'http://localhost:3000/api';

// Função para fazer requisições HTTP
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (options.body) {
      req.write(JSON.stringify(options.body));
    }

    req.end();
  });
}

async function debugAddressTypes() {
  console.log('=== Debug dos Tipos de Endereço ===\n');

  try {
    // 1. Verificar se a API está funcionando
    console.log('1. Verificando se a API está funcionando...');
    const healthResponse = await makeRequest(`${API_BASE_URL}/health`);
    console.log('Health check:', healthResponse.status, healthResponse.data);
    console.log('');

    // 2. Tentar criar um usuário de teste
    console.log('2. Tentando criar usuário de teste...');
    const registerResponse = await makeRequest(
      `${API_BASE_URL}/auth/register`,
      {
        method: 'POST',
        body: {
          email: '<EMAIL>',
          password: '123456',
          profile: {
            firstName: 'Test',
            lastName: 'Debug',
          },
        },
      }
    );

    console.log('Registro:', registerResponse.status, registerResponse.data);

    // 3. Tentar fazer login com o usuário criado
    console.log('3. Tentando fazer login...');
    const loginResponse = await makeRequest(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      body: {
        email: '<EMAIL>',
        password: '123456',
      },
    });

    if (loginResponse.status !== 200) {
      console.log(
        '❌ Falha no login:',
        loginResponse.status,
        loginResponse.data
      );

      // Tentar outras credenciais conhecidas
      console.log('Tentando credenciais conhecidas...');
      const credentials = [
        { email: '<EMAIL>', password: 'password123' },
        { email: '<EMAIL>', password: 'admin123' },
        { email: '<EMAIL>', password: 'Admin123' },
        { email: '<EMAIL>', password: '123456' },
      ];

      let accessToken = null;
      for (const cred of credentials) {
        const testLogin = await makeRequest(`${API_BASE_URL}/auth/login`, {
          method: 'POST',
          body: cred,
        });

        if (testLogin.status === 200) {
          console.log(`✅ Login realizado com ${cred.email}`);
          accessToken = testLogin.data.accessToken;
          break;
        }
      }

      if (!accessToken) {
        console.log('❌ Não foi possível fazer login com nenhuma credencial');
        return;
      }
    } else {
      console.log('✅ Login realizado com sucesso');
      var accessToken = loginResponse.data.accessToken;
    }

    if (!accessToken) {
      console.log('❌ Token de acesso não encontrado');
      return;
    }

    console.log('Token obtido:', accessToken.substring(0, 20) + '...');
    console.log('');

    // 4. Buscar tipos de endereço
    console.log('4. Buscando tipos de endereço...');
    const addressTypesResponse = await makeRequest(
      `${API_BASE_URL}/address-types?limit=100`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    console.log('Status:', addressTypesResponse.status);
    console.log(
      'Tipos de endereço:',
      JSON.stringify(addressTypesResponse.data, null, 2)
    );
    console.log('');

    // 5. Buscar entidades
    console.log('5. Buscando entidades...');
    const entitiesResponse = await makeRequest(
      `${API_BASE_URL}/entities?limit=5`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    console.log('Status:', entitiesResponse.status);
    if (entitiesResponse.data && entitiesResponse.data.length > 0) {
      const firstEntity = entitiesResponse.data[0];
      console.log('Primeira entidade:', firstEntity.id, firstEntity.name);
      console.log('');

      // 6. Buscar endereços da primeira entidade
      console.log('6. Buscando endereços da primeira entidade...');
      const addressesResponse = await makeRequest(
        `${API_BASE_URL}/entities/${firstEntity.id}/addresses`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      console.log('Status:', addressesResponse.status);
      console.log(
        'Endereços:',
        JSON.stringify(addressesResponse.data, null, 2)
      );

      // Analisar cada endereço
      if (Array.isArray(addressesResponse.data)) {
        console.log('\n=== Análise dos Endereços ===');
        addressesResponse.data.forEach((address, index) => {
          console.log(`\nEndereço ${index + 1}:`);
          console.log('  ID:', address.id);
          console.log('  Street:', address.street);
          console.log('  AddressTypeId:', address.addressTypeId);
          console.log('  AddressType:', address.addressType);
          console.log('  AddressType.name:', address.addressType?.name);
          console.log('  IsDefault:', address.isDefault);
        });
      }
    } else {
      console.log('Nenhuma entidade encontrada');
    }
  } catch (error) {
    console.error('Erro durante o debug:', error);
  }
}

// Executar o debug
debugAddressTypes();
