#!/bin/bash

# Script para associar o usuário <EMAIL> à empresa existente

echo "🔗 Associando usuário à empresa..."

# Conectar ao banco de dados e executar as queries
./dev.sh exec backend 'psql $DATABASE_URL -c "
-- <PERSON>car o usuário <EMAIL>
SELECT id, email, status FROM users WHERE email = '\''<EMAIL>'\'';

-- Buscar a empresa existente
SELECT id, name FROM companies WHERE deleted_at IS NULL LIMIT 1;

-- Buscar o papel de Administrador
SELECT id, name FROM roles WHERE name = '\''Administrador'\'' LIMIT 1;

-- C<PERSON>r associação usuário-empresa-papel
INSERT INTO user_company_roles (id, user_id, company_id, role_id, created_at, updated_at)
SELECT 
  gen_random_uuid(),
  u.id,
  c.id,
  r.id,
  NOW(),
  NOW()
FROM users u, companies c, roles r
WHERE u.email = '\''<EMAIL>'\''
  AND c.deleted_at IS NULL
  AND r.name = '\''Administrador'\''
  AND NOT EXISTS (
    SELECT 1 FROM user_company_roles ucr 
    WHERE ucr.user_id = u.id AND ucr.company_id = c.id
  );

-- Atualizar status do usuário para active
UPDATE users 
SET status = '\''active'\''
WHERE email = '\''<EMAIL>'\'' AND status = '\''no_company'\'';

-- Verificar a associação criada
SELECT 
  u.email,
  u.status,
  c.name as company_name,
  r.name as role_name
FROM user_company_roles ucr
JOIN users u ON ucr.user_id = u.id
JOIN companies c ON ucr.company_id = c.id
JOIN roles r ON ucr.role_id = r.id
WHERE u.email = '\''<EMAIL>'\'';
"'

echo "✅ Associação concluída!"
