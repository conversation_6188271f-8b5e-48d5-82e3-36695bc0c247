// Script para testar a validação de tipos entre categoria pai e subcategoria

const API_BASE = 'http://localhost:3000';

// Função para fazer login e obter token
async function login() {
  const response = await fetch(`${API_BASE}/api/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'Admin123',
    }),
  });

  if (!response.ok) {
    throw new Error(`Login failed: ${response.status}`);
  }

  const data = await response.json();
  return data.accessToken;
}

// Função para criar categoria
async function createCategory(token, categoryData) {
  const response = await fetch(`${API_BASE}/api/categories`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(categoryData),
  });

  const data = await response.json();
  return { status: response.status, data };
}

// Função principal de teste
async function testCategoryValidation() {
  try {
    console.log('🔐 Fazendo login...');
    const token = await login();
    console.log('✅ Login realizado com sucesso');

    // Teste 1: Criar categoria pai de receita
    console.log('\n📝 Teste 1: Criando categoria pai de receita...');
    const parentReceivable = await createCategory(token, {
      name: 'Receitas Principais',
      transactionType: 'receivable',
    });

    if (parentReceivable.status === 201) {
      console.log(
        '✅ Categoria pai de receita criada:',
        parentReceivable.data.name
      );
    } else {
      console.log(
        '❌ Erro ao criar categoria pai de receita:',
        parentReceivable.data
      );
      return;
    }

    // Teste 2: Criar categoria pai de despesa
    console.log('\n📝 Teste 2: Criando categoria pai de despesa...');
    const parentPayable = await createCategory(token, {
      name: 'Despesas Principais',
      transactionType: 'payable',
    });

    if (parentPayable.status === 201) {
      console.log(
        '✅ Categoria pai de despesa criada:',
        parentPayable.data.name
      );
    } else {
      console.log(
        '❌ Erro ao criar categoria pai de despesa:',
        parentPayable.data
      );
      return;
    }

    // Teste 3: Criar subcategoria de receita com pai de receita (deve funcionar)
    console.log(
      '\n📝 Teste 3: Criando subcategoria de receita com pai de receita (deve funcionar)...'
    );
    const subReceivableValid = await createCategory(token, {
      name: 'Vendas Online',
      transactionType: 'receivable',
      parentCategoryId: parentReceivable.data.id,
    });

    if (subReceivableValid.status === 201) {
      console.log(
        '✅ Subcategoria de receita criada com sucesso:',
        subReceivableValid.data.name
      );
    } else {
      console.log(
        '❌ Erro inesperado ao criar subcategoria válida:',
        subReceivableValid.data
      );
    }

    // Teste 4: Criar subcategoria de despesa com pai de despesa (deve funcionar)
    console.log(
      '\n📝 Teste 4: Criando subcategoria de despesa com pai de despesa (deve funcionar)...'
    );
    const subPayableValid = await createCategory(token, {
      name: 'Material de Escritório',
      transactionType: 'payable',
      parentCategoryId: parentPayable.data.id,
    });

    if (subPayableValid.status === 201) {
      console.log(
        '✅ Subcategoria de despesa criada com sucesso:',
        subPayableValid.data.name
      );
    } else {
      console.log(
        '❌ Erro inesperado ao criar subcategoria válida:',
        subPayableValid.data
      );
    }

    // Teste 5: Tentar criar subcategoria de receita com pai de despesa (deve falhar)
    console.log(
      '\n📝 Teste 5: Tentando criar subcategoria de receita com pai de despesa (deve falhar)...'
    );
    const subReceivableInvalid = await createCategory(token, {
      name: 'Vendas Inválidas',
      transactionType: 'receivable',
      parentCategoryId: parentPayable.data.id,
    });

    if (subReceivableInvalid.status === 400) {
      console.log(
        '✅ Validação funcionou! Erro esperado:',
        subReceivableInvalid.data.message
      );
    } else {
      console.log(
        '❌ Validação falhou! Deveria ter retornado erro 400, mas retornou:',
        subReceivableInvalid.status
      );
    }

    // Teste 6: Tentar criar subcategoria de despesa com pai de receita (deve falhar)
    console.log(
      '\n📝 Teste 6: Tentando criar subcategoria de despesa com pai de receita (deve falhar)...'
    );
    const subPayableInvalid = await createCategory(token, {
      name: 'Despesas Inválidas',
      transactionType: 'payable',
      parentCategoryId: parentReceivable.data.id,
    });

    if (subPayableInvalid.status === 400) {
      console.log(
        '✅ Validação funcionou! Erro esperado:',
        subPayableInvalid.data.message
      );
    } else {
      console.log(
        '❌ Validação falhou! Deveria ter retornado erro 400, mas retornou:',
        subPayableInvalid.status
      );
    }

    console.log('\n🎉 Testes concluídos!');
  } catch (error) {
    console.error('❌ Erro durante os testes:', error.message);
  }
}

// Executar os testes
testCategoryValidation();
