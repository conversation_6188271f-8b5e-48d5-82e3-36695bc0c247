// Script para testar o frontend via console do navegador
// Cole este código no console do navegador (F12 -> <PERSON><PERSON><PERSON>)

// Simular a criação de um endereço como o frontend faria
async function testAddressCreation() {
  console.log('🚀 Testando criação de endereço via frontend...');
  
  // Dados do endereço como o frontend enviaria
  const addressData = {
    street: 'Rua Frontend Test',
    number: '999',
    complement: 'Teste Console',
    district: 'Bairro Frontend',
    city: 'São Paulo',
    state: 'SP',
    zipCode: '01234-567',
    isDefault: false,
    type: 'main' // Tipo que o frontend envia
  };
  
  console.log('📤 Dados do endereço (frontend):', addressData);
  
  try {
    // Simular a chamada do serviço
    const response = await fetch('http://localhost:3000/api/entities/3abc6ea7-82c0-4991-8604-e244bcba4934/addresses', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + localStorage.getItem('token') // Assumindo que o token está no localStorage
      },
      body: JSON.stringify(addressData)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const result = await response.json();
    console.log('✅ Endereço criado:', result);
    
    // Verificar se o addressTypeId foi persistido
    if (result.addressTypeId) {
      console.log('✅ AddressTypeId persistido:', result.addressTypeId);
    } else {
      console.log('❌ AddressTypeId NÃO persistido');
    }
    
  } catch (error) {
    console.error('❌ Erro ao criar endereço:', error);
  }
}

// Executar o teste
testAddressCreation();

// Também disponibilizar a função globalmente para teste manual
window.testAddressCreation = testAddressCreation;
