# Correção do Sistema de Endereço Padrão

## Problema Identificado

O endereço marcado como padrão (default) no cadastro de entidades não estava sendo exibido visualmente como padrão na interface do usuário, mesmo que no banco de dados o campo `isDefault` estivesse corretamente definido.

## Causa Raiz

A propriedade `isDefault` estava **ausente no modelo `AddressDto`** do backend, que é usado para retornar dados da API para o frontend. Isso significa que mesmo que o banco de dados tivesse a informação correta, ela não estava sendo transmitida para o frontend.

### Análise Detalhada

1. **Banco de Dados**: ✅ Correto
   - Campo `is_default` definido corretamente no schema Prisma
   - Constraint e default value configurados adequadamente

2. **Backend Service**: ✅ Correto
   - Lógica de negócio funcionando corretamente
   - Queries incluindo a propriedade `isDefault`
   - Logs mostrando que os dados estavam sendo processados corretamente

3. **DTOs de Input**: ✅ Correto
   - `CreateEntityAddressDto` incluía `isDefault`
   - `UpdateEntityAddressDto` incluía `isDefault`

4. **DTO de Output**: ❌ **PROBLEMA ENCONTRADO**
   - `AddressDto` **NÃO incluía** a propriedade `isDefault`
   - Dados sendo perdidos na serialização da resposta da API

5. **Frontend**: ✅ Correto
   - Componentes preparados para receber e exibir `isDefault`
   - Lógica visual implementada corretamente

## Solução Implementada

### 1. Correção do AddressDto

**Arquivo**: `backend/src/models/address.model.ts`

Adicionada a propriedade `isDefault` ao modelo `AddressDto`:

```typescript
@ApiProperty({ 
  description: 'Indica se é o endereço padrão', 
  example: false,
  required: false 
})
isDefault?: boolean;
```

### 2. Correção do UpdateAddressDto

Também adicionada a propriedade `isDefault` ao `UpdateAddressDto` para garantir consistência:

```typescript
@ApiProperty({ 
  description: 'Indica se é o endereço padrão', 
  example: false,
  required: false 
})
@IsOptional()
isDefault?: boolean;
```

## Componentes Afetados

### Backend
- ✅ `backend/src/models/address.model.ts` - Corrigido
- ✅ `backend/src/routes/entities/entity-addresses.service.ts` - Já funcionava
- ✅ `backend/src/routes/entities/dto/create-entity-address.dto.ts` - Já funcionava
- ✅ `backend/src/routes/entities/dto/update-entity-address.dto.ts` - Melhorado

### Frontend
- ✅ `frontend/src/components/address/AddressCard.tsx` - Já funcionava
- ✅ `frontend/src/components/address/EntityAddressManager.tsx` - Já funcionava
- ✅ `frontend/src/services/api/entityAddressService.ts` - Já funcionava

## Funcionalidades Restauradas

1. **Indicador Visual**: Endereços padrão agora exibem:
   - Badge "Padrão" no cabeçalho do card
   - Borda azul diferenciada
   - Ícone de estrela preenchida no canto superior direito
   - Fundo azul claro

2. **Funcionalidade de Definir Padrão**: 
   - Botão "Definir como padrão" aparece apenas em endereços não-padrão
   - Ao clicar, o endereço é marcado como padrão e outros são desmarcados

3. **Ordenação**: Endereços padrão aparecem primeiro na lista

## Teste da Correção

Para testar a correção:

1. Acesse uma entidade existente com endereços cadastrados
2. Verifique se o endereço padrão está visualmente destacado
3. Teste a funcionalidade de definir outro endereço como padrão
4. Confirme que apenas um endereço permanece como padrão

## Impacto

- ✅ **Sem Breaking Changes**: A correção é retrocompatível
- ✅ **Dados Preservados**: Todos os dados existentes no banco permanecem íntegros
- ✅ **Performance**: Nenhum impacto na performance
- ✅ **Funcionalidade Completa**: Sistema de endereço padrão totalmente funcional

## Prevenção

Para evitar problemas similares no futuro:

1. **Validação de DTOs**: Sempre verificar se DTOs de output incluem todas as propriedades necessárias
2. **Testes de Integração**: Implementar testes que validem a transmissão completa de dados entre backend e frontend
3. **Documentação da API**: Manter documentação atualizada com todos os campos retornados
