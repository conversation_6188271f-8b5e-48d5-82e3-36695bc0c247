#!/bin/bash

# Teste final para validar a correção completa do problema de persistência de endereços
# Execute com: chmod +x test-final-validation.sh && ./test-final-validation.sh

API_BASE_URL="http://localhost:3000/api"
EMAIL="<EMAIL>"
PASSWORD="Admin123"

echo "🎯 TESTE FINAL - Validação da Correção Completa"
echo "=============================================="
echo ""

# 1. Login
echo "🔐 Fazendo login..."
LOGIN_RESPONSE=$(curl -s -X POST "${API_BASE_URL}/auth/login" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"${EMAIL}\",\"password\":\"${PASSWORD}\"}")

TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)

if [[ -z "$TOKEN" ]]; then
  echo "❌ Erro ao extrair token do login"
  exit 1
fi

echo "✅ Login realizado com sucesso"
echo ""

# 2. Buscar entidade de teste
ENTITY_ID="3abc6ea7-82c0-4991-8604-e244bcba4934"
echo "📋 Usando entidade de teste: $ENTITY_ID"
echo ""

# 3. Contar endereços antes
echo "--- ESTADO INICIAL ---"
ADDRESSES_BEFORE=$(curl -s -X GET "${API_BASE_URL}/entities/${ENTITY_ID}/addresses" \
  -H "Authorization: Bearer ${TOKEN}")

ADDRESSES_COUNT_BEFORE=$(echo $ADDRESSES_BEFORE | grep -o '"id":"[^"]*"' | wc -l)
echo "Endereços existentes: $ADDRESSES_COUNT_BEFORE"
echo ""

# 4. Teste 1: Criar endereço com tipo 'main' (como o frontend faria)
echo "--- TESTE 1: Tipo 'main' (Principal) ---"
ADDRESS_DATA_MAIN="{
  \"street\": \"Rua Teste Main\",
  \"number\": \"100\",
  \"complement\": \"Teste Main\",
  \"district\": \"Bairro Main\",
  \"city\": \"São Paulo\",
  \"state\": \"SP\",
  \"zipCode\": \"01000-000\",
  \"isDefault\": false,
  \"type\": \"main\"
}"

echo "📤 Criando endereço tipo 'main'..."
CREATE_RESPONSE_1=$(curl -s -X POST "${API_BASE_URL}/entities/${ENTITY_ID}/addresses" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d "$ADDRESS_DATA_MAIN")

NEW_ADDRESS_ID_1=$(echo $CREATE_RESPONSE_1 | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
ADDRESS_TYPE_ID_1=$(echo $CREATE_RESPONSE_1 | grep -o '"addressTypeId":"[^"]*"' | cut -d'"' -f4)

if [[ -n "$NEW_ADDRESS_ID_1" && -n "$ADDRESS_TYPE_ID_1" ]]; then
  echo "✅ Endereço 'main' criado: $NEW_ADDRESS_ID_1"
  echo "✅ AddressTypeId persistido: $ADDRESS_TYPE_ID_1"
else
  echo "❌ Falha ao criar endereço 'main'"
  echo "Resposta: $CREATE_RESPONSE_1"
fi
echo ""

# 5. Teste 2: Criar endereço com tipo 'billing'
echo "--- TESTE 2: Tipo 'billing' (Faturamento) ---"
ADDRESS_DATA_BILLING="{
  \"street\": \"Rua Teste Billing\",
  \"number\": \"200\",
  \"complement\": \"Teste Billing\",
  \"district\": \"Bairro Billing\",
  \"city\": \"São Paulo\",
  \"state\": \"SP\",
  \"zipCode\": \"02000-000\",
  \"isDefault\": false,
  \"type\": \"billing\"
}"

echo "📤 Criando endereço tipo 'billing'..."
CREATE_RESPONSE_2=$(curl -s -X POST "${API_BASE_URL}/entities/${ENTITY_ID}/addresses" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d "$ADDRESS_DATA_BILLING")

NEW_ADDRESS_ID_2=$(echo $CREATE_RESPONSE_2 | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
ADDRESS_TYPE_ID_2=$(echo $CREATE_RESPONSE_2 | grep -o '"addressTypeId":"[^"]*"' | cut -d'"' -f4)

if [[ -n "$NEW_ADDRESS_ID_2" && -n "$ADDRESS_TYPE_ID_2" ]]; then
  echo "✅ Endereço 'billing' criado: $NEW_ADDRESS_ID_2"
  echo "✅ AddressTypeId persistido: $ADDRESS_TYPE_ID_2"
else
  echo "❌ Falha ao criar endereço 'billing'"
  echo "Resposta: $CREATE_RESPONSE_2"
fi
echo ""

# 6. Teste 3: Criar endereço com tipo 'shipping'
echo "--- TESTE 3: Tipo 'shipping' (Entrega) ---"
ADDRESS_DATA_SHIPPING="{
  \"street\": \"Rua Teste Shipping\",
  \"number\": \"300\",
  \"complement\": \"Teste Shipping\",
  \"district\": \"Bairro Shipping\",
  \"city\": \"São Paulo\",
  \"state\": \"SP\",
  \"zipCode\": \"03000-000\",
  \"isDefault\": false,
  \"type\": \"shipping\"
}"

echo "📤 Criando endereço tipo 'shipping'..."
CREATE_RESPONSE_3=$(curl -s -X POST "${API_BASE_URL}/entities/${ENTITY_ID}/addresses" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d "$ADDRESS_DATA_SHIPPING")

NEW_ADDRESS_ID_3=$(echo $CREATE_RESPONSE_3 | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
ADDRESS_TYPE_ID_3=$(echo $CREATE_RESPONSE_3 | grep -o '"addressTypeId":"[^"]*"' | cut -d'"' -f4)

if [[ -n "$NEW_ADDRESS_ID_3" && -n "$ADDRESS_TYPE_ID_3" ]]; then
  echo "✅ Endereço 'shipping' criado: $NEW_ADDRESS_ID_3"
  echo "✅ AddressTypeId persistido: $ADDRESS_TYPE_ID_3"
else
  echo "❌ Falha ao criar endereço 'shipping'"
  echo "Resposta: $CREATE_RESPONSE_3"
fi
echo ""

# 7. Verificação final
echo "--- VERIFICAÇÃO FINAL ---"
echo "📍 Consultando todos os endereços da entidade..."
ADDRESSES_FINAL=$(curl -s -X GET "${API_BASE_URL}/entities/${ENTITY_ID}/addresses" \
  -H "Authorization: Bearer ${TOKEN}")

ADDRESSES_COUNT_FINAL=$(echo $ADDRESSES_FINAL | grep -o '"id":"[^"]*"' | wc -l)
echo "Total de endereços após testes: $ADDRESSES_COUNT_FINAL"
echo ""

# Verificar se os novos endereços estão na lista
echo "Verificando persistência dos endereços criados:"

if [[ -n "$NEW_ADDRESS_ID_1" ]] && echo $ADDRESSES_FINAL | grep -q "$NEW_ADDRESS_ID_1"; then
  echo "✅ Endereço 'main' encontrado na consulta"
  if echo $ADDRESSES_FINAL | grep -q "$ADDRESS_TYPE_ID_1"; then
    echo "✅ AddressTypeId 'main' persistido corretamente"
  else
    echo "❌ AddressTypeId 'main' não persistido"
  fi
else
  echo "❌ Endereço 'main' não encontrado na consulta"
fi

if [[ -n "$NEW_ADDRESS_ID_2" ]] && echo $ADDRESSES_FINAL | grep -q "$NEW_ADDRESS_ID_2"; then
  echo "✅ Endereço 'billing' encontrado na consulta"
  if echo $ADDRESSES_FINAL | grep -q "$ADDRESS_TYPE_ID_2"; then
    echo "✅ AddressTypeId 'billing' persistido corretamente"
  else
    echo "❌ AddressTypeId 'billing' não persistido"
  fi
else
  echo "❌ Endereço 'billing' não encontrado na consulta"
fi

if [[ -n "$NEW_ADDRESS_ID_3" ]] && echo $ADDRESSES_FINAL | grep -q "$NEW_ADDRESS_ID_3"; then
  echo "✅ Endereço 'shipping' encontrado na consulta"
  if echo $ADDRESSES_FINAL | grep -q "$ADDRESS_TYPE_ID_3"; then
    echo "✅ AddressTypeId 'shipping' persistido corretamente"
  else
    echo "❌ AddressTypeId 'shipping' não persistido"
  fi
else
  echo "❌ Endereço 'shipping' não encontrado na consulta"
fi

echo ""
echo "--- RESUMO FINAL ---"
echo "📊 Estatísticas:"
echo "  - Endereços antes dos testes: $ADDRESSES_COUNT_BEFORE"
echo "  - Endereços após os testes: $ADDRESSES_COUNT_FINAL"
echo "  - Endereços criados: $((ADDRESSES_COUNT_FINAL - ADDRESSES_COUNT_BEFORE))"
echo ""

# Verificar se todos os testes passaram
TESTS_PASSED=0
if [[ -n "$NEW_ADDRESS_ID_1" && -n "$ADDRESS_TYPE_ID_1" ]]; then
  ((TESTS_PASSED++))
fi
if [[ -n "$NEW_ADDRESS_ID_2" && -n "$ADDRESS_TYPE_ID_2" ]]; then
  ((TESTS_PASSED++))
fi
if [[ -n "$NEW_ADDRESS_ID_3" && -n "$ADDRESS_TYPE_ID_3" ]]; then
  ((TESTS_PASSED++))
fi

echo "🎯 Resultado dos Testes: $TESTS_PASSED/3 passaram"

if [[ $TESTS_PASSED -eq 3 ]]; then
  echo ""
  echo "🎉 SUCESSO! Todos os testes passaram!"
  echo "✅ O problema de persistência de endereços foi CORRIGIDO!"
  echo "✅ Os tipos do frontend são convertidos corretamente para addressTypeId"
  echo "✅ Os endereços são persistidos com todos os relacionamentos"
  echo "✅ As consultas retornam os endereços com tipos corretos"
else
  echo ""
  echo "❌ FALHA! Alguns testes falharam."
  echo "⚠️  O problema pode não estar completamente resolvido."
fi

echo ""
echo "🏁 Teste final concluído!"
