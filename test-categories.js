// Script de teste para verificar o sistema de categorias hierárquicas
const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

// Credenciais de teste
const credentials = {
  email: '<EMAIL>',
  password: 'Admin123'
};

let authToken = '';

async function login() {
  try {
    console.log('🔐 Fazendo login...');
    const response = await axios.post(`${API_BASE}/auth/login`, credentials);
    authToken = response.data.accessToken;
    console.log('✅ Login realizado com sucesso');
    return response.data;
  } catch (error) {
    console.error('❌ Erro no login:', error.response?.data || error.message);
    throw error;
  }
}

async function createCategory(name, transactionType, parentCategoryId = null) {
  try {
    console.log(`📝 Criando categoria: ${name}${parentCategoryId ? ' (filha)' : ' (pai)'}`);
    const response = await axios.post(`${API_BASE}/categories`, {
      name,
      transactionType,
      parentCategoryId
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log(`✅ Categoria "${name}" criada com ID: ${response.data.id}`);
    return response.data;
  } catch (error) {
    console.error(`❌ Erro ao criar categoria "${name}":`, error.response?.data || error.message);
    throw error;
  }
}

async function getCategoryTree() {
  try {
    console.log('📋 Buscando árvore de categorias...');
    const response = await axios.get(`${API_BASE}/categories/tree`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Árvore de categorias obtida');
    return response.data;
  } catch (error) {
    console.error('❌ Erro ao buscar árvore de categorias:', error.response?.data || error.message);
    throw error;
  }
}

function printCategoryTree(categories, level = 0) {
  const indent = '  '.repeat(level);
  categories.forEach(category => {
    console.log(`${indent}📁 ${category.name} (${category.transactionType}) - ID: ${category.id}`);
    if (category.children && category.children.length > 0) {
      printCategoryTree(category.children, level + 1);
    }
  });
}

async function deleteCategory(id, name) {
  try {
    console.log(`🗑️ Deletando categoria: ${name}`);
    await axios.delete(`${API_BASE}/categories/${id}`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log(`✅ Categoria "${name}" deletada`);
  } catch (error) {
    console.error(`❌ Erro ao deletar categoria "${name}":`, error.response?.data || error.message);
  }
}

async function testCategoryHierarchy() {
  try {
    // 1. Fazer login
    await login();

    // 2. Buscar categorias existentes
    console.log('\n📋 Estado inicial das categorias:');
    let tree = await getCategoryTree();
    printCategoryTree(tree);

    // 3. Criar categoria pai
    const parentCategory = await createCategory('Categoria Pai Teste', 'payable');

    // 4. Criar categoria filha
    const childCategory = await createCategory('Categoria Filha Teste', 'payable', parentCategory.id);

    // 5. Verificar se ambas aparecem na árvore
    console.log('\n📋 Estado após criação das categorias:');
    tree = await getCategoryTree();
    printCategoryTree(tree);

    // 6. Verificar se a categoria filha está corretamente aninhada
    const parentInTree = tree.find(cat => cat.id === parentCategory.id);
    if (parentInTree) {
      console.log('\n✅ Categoria pai encontrada na árvore');
      if (parentInTree.children && parentInTree.children.length > 0) {
        const childInTree = parentInTree.children.find(child => child.id === childCategory.id);
        if (childInTree) {
          console.log('✅ Categoria filha encontrada corretamente aninhada na categoria pai');
          console.log('🎉 TESTE PASSOU: Hierarquia de categorias funcionando corretamente!');
        } else {
          console.log('❌ TESTE FALHOU: Categoria filha não encontrada na categoria pai');
        }
      } else {
        console.log('❌ TESTE FALHOU: Categoria pai não possui filhos');
      }
    } else {
      console.log('❌ TESTE FALHOU: Categoria pai não encontrada na árvore');
    }

    // 7. Limpeza - deletar categorias de teste
    console.log('\n🧹 Limpando categorias de teste...');
    await deleteCategory(childCategory.id, childCategory.name);
    await deleteCategory(parentCategory.id, parentCategory.name);

    console.log('\n📋 Estado final das categorias:');
    tree = await getCategoryTree();
    printCategoryTree(tree);

  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
  }
}

// Executar o teste
testCategoryHierarchy();
